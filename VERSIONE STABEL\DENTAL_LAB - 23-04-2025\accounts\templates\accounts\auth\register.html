<!-- accounts/templates/accounts/auth/register.html -->
{% extends 'base.html' %}
{% load static %}

{% block title %}Register | Dental Lab{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <img src="{% static 'img/dental.jpg' %}" alt="Logo" class="mb-3" style="width: 80px;">
                        <h2 class="font-weight-bold">Create Account</h2>
                        <p class="text-muted">Please fill in the information below</p>
                    </div>

                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">First Name</label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.first_name.errors|join:", " }}
                                </div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">Last Name</label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.last_name.errors|join:", " }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">Email Address</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                {{ form.email }}
                            </div>
                            {% if form.email.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.email.errors|join:", " }}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.password1.id_for_label }}" class="form-label">Password</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                {{ form.password1 }}
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword1">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            {% if form.password1.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.password1.errors|join:", " }}
                            </div>
                            {% endif %}
                            <div class="form-text">{{ form.password1.help_text|safe }}</div>
                        </div>

                        <div class="mb-4">
                            <label for="{{ form.password2.id_for_label }}" class="form-label">Confirm Password</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                {{ form.password2 }}
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword2">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            {% if form.password2.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.password2.errors|join:", " }}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-4">
                            <div class="form-check">
                                {{ form.terms_agreement }}
                                <label class="form-check-label" for="{{ form.terms_agreement.id_for_label }}">
                                    I agree to the <a href="#" class="text-primary">Terms and Conditions</a>
                                </label>
                            </div>
                            {% if form.terms_agreement.errors %}
                            <div class="invalid-feedback d-block">
                                {{ form.terms_agreement.errors|join:", " }}
                            </div>
                            {% endif %}
                        </div>

                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-user-plus me-2"></i>Register
                        </button>

                        <p class="text-center mb-0">
                            Already have an account? 
                            <a href="{% url 'accounts:login' %}" class="text-primary text-decoration-none">
                                Login here
                            </a>
                        </p>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password visibility toggles
    function setupPasswordToggle(toggleId, inputId) {
        const toggleButton = document.getElementById(toggleId);
        const passwordInput = document.getElementById(inputId);
        
        if (toggleButton && passwordInput) {
            toggleButton.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                
                const icon = this.querySelector('i');
                icon.classList.toggle('fa-eye');
                icon.classList.toggle('fa-eye-slash');
            });
        }
    }

    setupPasswordToggle('togglePassword1', '{{ form.password1.id_for_label }}');
    setupPasswordToggle('togglePassword2', '{{ form.password2.id_for_label }}');

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
});
</script>
{% endblock %}

{% block extra_css %}
<style>
.card {
    border: none;
    border-radius: 15px;
}

.form-control {
    border-radius: 8px;
    padding: 10px 15px;
}

.input-group-text {
    background-color: transparent;
    border-right: none;
}

.form-control {
    border-left: none;
}

.form-control:focus {
    box-shadow: none;
    border-color: #ced4da;
}

.btn-primary {
    border-radius: 8px;
    padding: 12px;
    font-weight: 500;
}

.alert {
    border-radius: 8px;
}

.invalid-feedback {
    font-size: 0.875em;
}

.form-text {
    font-size: 0.875em;
    color: #6c757d;
}

.form-check-label {
    font-size: 0.875em;
}
</style>
{% endblock %}
{% endblock content %}