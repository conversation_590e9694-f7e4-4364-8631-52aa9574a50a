# billing/forms.py

from django import forms
from django.forms import inlineformset_factory, BaseInlineFormSet
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from decimal import Decimal

# Importo modelet nga aplikacioni yt 'billing'
from .models import Invoice, InvoiceItem, PurchaseOrder, PurchaseOrderItem

# Importo modelet e lidhura nga aplikacionet e tjera
# Sigurohu qe shtigjet e importit jane korrekte
from Dentists.models import Dentist
from case.models import Case
from items.models import Item, Currency, Supplier, RawMaterial, Unit


# --- Format për Invoice ---

class InvoiceForm(forms.ModelForm):
    """
    Formular për krijimin dhe përditësimin e një objekti Invoice.
    Nuk përfshin item-at, ata trajtohen nga formset-i.
    """
    # Kemi hequr case_id, pasi tani Invoice ka OneToOneField me Case
    # dhe do ta lidhim ne view.

    class Meta:
        model = Invoice
        # Fushat qe mund te modifikohen direkt ne fature
        fields = [
            'dentist', 'patient_name', 'currency',
            'date', 'due_date', 'status', 'notes'
        ]
        widgets = {
            'dentist': forms.Select(attrs={'class': 'form-select form-select-lg'}),
            'patient_name': forms.TextInput(attrs={'class': 'form-control form-control-lg'}),
            'currency': forms.Select(attrs={'class': 'form-select'}),
            'date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'due_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
        labels = {
            'patient_name': _("Patient's Full Name"),
            'date': _("Invoice Issue Date"),
            'due_date': _("Payment Due Date"),
        }
        help_texts = {
            'patient_name': _("Enter the name as it should appear on the invoice."),
            'currency': _("Select the main currency for this invoice."),
            'due_date': _("Optional: Set a deadline for payment."),
            'status': _("Select the current status of the invoice."),
            'notes': _("Add any relevant notes (optional)."),
        }

    def __init__(self, *args, **kwargs):
        # Merr rastin (case) nese kalohet nga view per parambushje
        self.case = kwargs.pop('case', None)
        super().__init__(*args, **kwargs)

        # Vendos vlerat fillestare nese eshte krijim i ri dhe ka rast (case)
        if not self.instance.pk and self.case:
            self.fields['dentist'].initial = self.case.dentist
            self.fields['patient_name'].initial = f"{self.case.patient.first_name or ''} {self.case.patient.last_name or ''}".strip()
            # Mund te vendosesh dhe monedhen default bazuar ne dentist/klinike nese ke logjike
            # self.fields['currency'].initial = self.case.dentist.preferred_currency # Shembull
        elif self.instance.pk:
            # Nese po modifikohet, beje dentistin read-only? (Opsionale)
            # self.fields['dentist'].disabled = True
            pass # Mund te shtosh logjike tjeter per modifikim

        # Bej fushat te kerkuara (required) sipas nevojes
        self.fields['dentist'].required = True
        self.fields['patient_name'].required = True
        self.fields['currency'].required = True
        self.fields['date'].required = True
        self.fields['status'].required = True

    def clean_date(self):
        date = self.cleaned_data.get('date')
        if date and date > timezone.now().date():
            raise ValidationError(_("Invoice date cannot be in the future."))
        return date

    def clean_due_date(self):
        due_date = self.cleaned_data.get('due_date')
        invoice_date = self.cleaned_data.get('date')
        if due_date and invoice_date and due_date < invoice_date:
            raise ValidationError(_("Due date cannot be before the invoice date."))
        return due_date

    def clean_status(self):
        status = self.cleaned_data.get('status')
        # Nese po modifikohet dhe ka pagesa, mos lejo te ndryshohet ne 'cancelled' pa logjike shtese?
        if self.instance.pk and status == 'cancelled':
             # Sigurohu qe related_name eshte korrekt
             if self.instance.invoice_payments_finance.exists():
                 raise ValidationError(
                     _("Cannot cancel an invoice that has payments. Please delete payments first.")
                 )
        return status


class InvoiceItemForm(forms.ModelForm):
    """ Formular per nje artikull te vetem brenda fatures (per perdorim ne FormSet). """

    class Meta:
        model = InvoiceItem
        fields = ['item', 'description', 'quantity', 'selling_price', 'currency']
        widgets = {
            # Perdor Select2 ose autocomplete per fushen 'item' ne template per performance
            'item': forms.Select(attrs={'class': 'form-select item-select'}),
            'description': forms.TextInput(attrs={'class': 'form-control item-description'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control item-quantity', 'step': '0.01', 'min': '0.01'}),
            'selling_price': forms.NumberInput(attrs={'class': 'form-control item-price', 'step': '0.01', 'min': '0.00'}),
            'currency': forms.Select(attrs={'class': 'form-select item-currency'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Vendos queryset per artikujt
        self.fields['item'].queryset = Item.objects.all().select_related('currency')
        self.fields['currency'].queryset = Currency.objects.all()

        # Mbush automatikisht fushat kur zgjidhet nje artikull (me JavaScript ne frontend)
        # por gjithashtu vendos disa vlera fillestare ketu nese eshte e mundur
        if 'item' in self.data: # Nese formulari po ri-paraqitet me te dhena
            try:
                item_id = int(self.data.get('item'))
                item = Item.objects.get(id=item_id)
                if not self.initial.get('description'): self.initial['description'] = item.name
                if not self.initial.get('selling_price'): self.initial['selling_price'] = item.selling_price
                if not self.initial.get('currency'): self.initial['currency'] = item.currency
            except (ValueError, TypeError, Item.DoesNotExist):
                pass
        elif self.instance.pk and self.instance.item: # Nese po modifikohet nje item ekzistues
             if not self.initial.get('description'): self.initial['description'] = self.instance.item.name
             if not self.initial.get('selling_price'): self.initial['selling_price'] = self.instance.item.selling_price
             if not self.initial.get('currency'): self.initial['currency'] = self.instance.item.currency


    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')
        if quantity is not None and quantity <= 0:
            raise ValidationError(_("Quantity must be positive."), code='positive_quantity')
        return quantity

    def clean_selling_price(self):
        selling_price = self.cleaned_data.get('selling_price')
        if selling_price is not None and selling_price < 0:
            raise ValidationError(_("Price cannot be negative."), code='negative_price')
        return selling_price

    def clean(self):
        cleaned_data = super().clean()
        # Nese eshte zgjedhur nje artikull, bej fushat e tjera te kerkuara
        item = cleaned_data.get('item')
        quantity = cleaned_data.get('quantity')
        selling_price = cleaned_data.get('selling_price')
        currency = cleaned_data.get('currency')

        # Nese eshte nje rresht qe po fshihet, nuk ka nevoje per validim
        if self.cleaned_data.get('DELETE', False):
            return cleaned_data

        # Ky validim eshte per BaseInvoiceItemFormSet qe te anashkaloje format boshe
        is_empty_form = not any([item, quantity, selling_price, currency])
        if is_empty_form:
            # Nese eshte bosh, nuk ka nevoje per validime te metejshme ketu
            # Base formset do ta anashkaloje
            return cleaned_data

        # Nese nuk eshte bosh dhe nuk ka artikull, shfaq error
        if not item and not is_empty_form:
             self.add_error('item', ValidationError(_("Please select an item."), code='required'))

        # Nese ka artikull, fushat e tjera jane te kerkuara
        if item:
            if quantity is None and 'quantity' not in self._errors:
                self.add_error('quantity', ValidationError(_("This field is required."), code='required'))
            if selling_price is None and 'selling_price' not in self._errors:
                self.add_error('selling_price', ValidationError(_("This field is required."), code='required'))
            if currency is None and 'currency' not in self._errors:
                self.add_error('currency', ValidationError(_("This field is required."), code='required'))

        return cleaned_data


class BaseInvoiceItemFormSet(BaseInlineFormSet):
    """ FormSet baze per artikujt e fatures per te trajtuar validimet shtese. """

    def clean(self):
        super().clean()
        # Ngre nje gabim nese asnje artikull nuk eshte futur fare
        valid_forms_count = 0
        for form in self.forms:
            # Kontrollo nese formulari eshte valid DHE nuk eshte bosh DHE nuk eshte shenuar per fshirje
            if form.is_valid() and not form.cleaned_data.get('DELETE', False):
                 # Kontrollo nese ka te pakten nje vlere ne fushat kryesore
                 if any(form.cleaned_data.get(field) for field in ['item', 'quantity', 'selling_price', 'currency']):
                     valid_forms_count += 1

        if valid_forms_count == 0:
            # Nese asnje form nuk eshte valid dhe i plotesuar, shfaq nje gabim te pergjithshem per formset-in
            raise ValidationError(
                _("You must add at least one item to the invoice."),
                code='no_items'
            )

        # Mund te shtosh validime te tjera per te gjithe formset-in ketu, nese eshte nevoja
        # p.sh., per te kontrolluar qe totali nuk e kalon nje limit, etj.


# Krijo FormSet-in duke perdorur factory dhe klasen baze te personalizuar
InvoiceItemFormSet = inlineformset_factory(
    Invoice,                   # Modeli prind
    InvoiceItem,               # Modeli femije (inline)
    form=InvoiceItemForm,      # Formulari qe do perdoret per cdo item
    formset=BaseInvoiceItemFormSet, # FormSet-i baze i personalizuar
    fields=['item', 'description', 'quantity', 'selling_price', 'currency'], # Fushat qe do shfaqen
    extra=1,                   # Sa forma boshe te shfaqen fillimisht (1 eshte mire per perdorshmeri)
    can_delete=True,           # Lejo fshirjen e item-ave ekzistues
    min_num=0,                 # Nuk kerkohet minimumi i item-ave (validimi behet ne BaseInvoiceItemFormSet)
    validate_min=False,        # Mos e valido minimumin ketu
)


# --- Format për Purchase Order ---

class PurchaseOrderForm(forms.ModelForm):
    """ Formular per krijimin dhe perditesimin e PurchaseOrder. """

    class Meta:
        model = PurchaseOrder
        fields = [
            'supplier', 'order_date', 'expected_delivery_date',
            'status', 'notes'
            # 'total_amount' hiqet, llogaritet ne view
        ]
        widgets = {
            'supplier': forms.Select(attrs={'class': 'form-select'}),
            'order_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'expected_delivery_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'status': forms.Select(attrs={'class': 'form-select'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['supplier'].queryset = Supplier.objects.all()
        # Mund te kufizosh statuset qe mund te zgjidhen ne krijim vs modifikim
        if not self.instance.pk:
            # Ne krijim, lejo vetem draft ose submitted?
            self.fields['status'].choices = [
                 ('draft', _('Draft')),
                 ('submitted', _('Submitted')),
             ]
            self.fields['status'].initial = 'draft'
        #else: # Ne modifikim, lejo te gjitha statuset pervec draft?
             # choices = list(PurchaseOrder.STATUS_CHOICES)
             # self.fields['status'].choices = [c for c in choices if c[0] != 'draft']


    def clean_expected_delivery_date(self):
        expected_date = self.cleaned_data.get('expected_delivery_date')
        order_date = self.cleaned_data.get('order_date')
        if expected_date and order_date and expected_date < order_date:
            raise ValidationError(_("Expected delivery date cannot be before the order date."))
        return expected_date


class PurchaseOrderItemForm(forms.ModelForm):
    """ Formular per nje artikull te vetem brenda PurchaseOrder (per FormSet). """

    class Meta:
        model = PurchaseOrderItem
        fields = ['raw_material', 'quantity', 'unit', 'price_per_unit', 'currency']
        widgets = {
            'raw_material': forms.Select(attrs={'class': 'form-select item-select'}),
            'quantity': forms.NumberInput(attrs={'class': 'form-control item-quantity', 'step': '0.01', 'min': '0.01'}),
            'unit': forms.Select(attrs={'class': 'form-select item-unit'}),
            'price_per_unit': forms.NumberInput(attrs={'class': 'form-control item-price', 'step': '0.01', 'min': '0.00'}),
            'currency': forms.Select(attrs={'class': 'form-select item-currency'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['raw_material'].queryset = RawMaterial.objects.all().select_related('unit', 'currency')
        self.fields['unit'].queryset = Unit.objects.all()
        self.fields['currency'].queryset = Currency.objects.all()

        # Autopopulate nese zgjidhet material (perdoret me mire JS ne frontend)
        if 'raw_material' in self.data:
            try:
                material_id = int(self.data.get('raw_material'))
                material = RawMaterial.objects.get(id=material_id)
                if not self.initial.get('unit'): self.initial['unit'] = material.unit
                if not self.initial.get('currency'): self.initial['currency'] = material.currency
                # Mund te vendosesh dhe nje cmim default nese e ke ne modelin RawMaterial
                # if not self.initial.get('price_per_unit'): self.initial['price_per_unit'] = material.default_purchase_price
            except (ValueError, TypeError, RawMaterial.DoesNotExist):
                pass
        elif self.instance.pk and self.instance.raw_material:
             if not self.initial.get('unit'): self.initial['unit'] = self.instance.raw_material.unit
             if not self.initial.get('currency'): self.initial['currency'] = self.instance.raw_material.currency


    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')
        if quantity is not None and quantity <= 0:
            raise ValidationError(_("Quantity must be positive."), code='positive_quantity')
        return quantity

    def clean_price_per_unit(self):
        price = self.cleaned_data.get('price_per_unit')
        if price is not None and price < 0:
            raise ValidationError(_("Price cannot be negative."), code='negative_price')
        return price

    def clean(self):
        cleaned_data = super().clean()
        # Nese eshte zgjedhur nje material, bej fushat e tjera te kerkuara
        material = cleaned_data.get('raw_material')
        quantity = cleaned_data.get('quantity')
        unit = cleaned_data.get('unit')
        price = cleaned_data.get('price_per_unit')
        currency = cleaned_data.get('currency')

        if self.cleaned_data.get('DELETE', False):
            return cleaned_data

        is_empty_form = not any([material, quantity, unit, price, currency])
        if is_empty_form:
            return cleaned_data

        if not material and not is_empty_form:
             self.add_error('raw_material', ValidationError(_("Please select a material."), code='required'))

        if material:
            if quantity is None and 'quantity' not in self._errors:
                self.add_error('quantity', ValidationError(_("This field is required."), code='required'))
            if unit is None and 'unit' not in self._errors:
                self.add_error('unit', ValidationError(_("This field is required."), code='required'))
            if price is None and 'price_per_unit' not in self._errors:
                self.add_error('price_per_unit', ValidationError(_("This field is required."), code='required'))
            if currency is None and 'currency' not in self._errors:
                self.add_error('currency', ValidationError(_("This field is required."), code='required'))

        return cleaned_data


class BasePurchaseOrderItemFormSet(BaseInlineFormSet):
    """ FormSet baze per artikujt e PO per te trajtuar validimet shtese. """

    def clean(self):
        super().clean()
        valid_forms_count = 0
        for form in self.forms:
            if form.is_valid() and not form.cleaned_data.get('DELETE', False):
                 if any(form.cleaned_data.get(field) for field in ['raw_material', 'quantity', 'price_per_unit']):
                     valid_forms_count += 1

        if valid_forms_count == 0:
            raise ValidationError(
                _("You must add at least one item to the purchase order."),
                code='no_items'
            )


# Krijo FormSet-in per PO items
PurchaseOrderItemFormSet = inlineformset_factory(
    PurchaseOrder,
    PurchaseOrderItem,
    form=PurchaseOrderItemForm,
    formset=BasePurchaseOrderItemFormSet,
    fields=['raw_material', 'quantity', 'unit', 'price_per_unit', 'currency'],
    extra=1,
    can_delete=True,
    min_num=0,
    validate_min=False,
)


# --- Format për Select Case ---
# Kjo forme perdoret ne SelectCaseView per te zgjedhur nje rast per te krijuar fature

class SelectCaseForm(forms.Form):
    """ Formular per te zgjedhur nje Case per te krijuar fature. """
    case = forms.ModelChoiceField(
        queryset=Case.objects.none(), # Queryset vendoset ne view
        widget=forms.RadioSelect(attrs={'class': 'case-radio'}),
        empty_label=None, # Mos shfaq opsion bosh
        label="", # Hiq labelin default
        error_messages={
            'required': _('Please select a case to create an invoice for.'),
            'invalid_choice': _('Invalid case selected.'),
        }
    )

    def __init__(self, *args, **kwargs):
        # Merr queryset nga view
        cases_queryset = kwargs.pop('cases_queryset', Case.objects.all())
        super().__init__(*args, **kwargs)

        # Filtro rastet:
        # 1. Qe nuk kane tashme nje fature (invoice__isnull=True)
        # 2. Qe nuk jane anuluar (status != 'cancelled')
        # 3. Qe kane status qe lejon faturimin (psh, 'closed', 'ready_to_ship', etj. - shto sipas nevojes)
        valid_statuses_for_invoicing = ['closed', 'ready_to_ship'] # Shembull, pershtate sipas logjikes tende
        self.fields['case'].queryset = cases_queryset.filter(
            invoice__isnull=True,
            # status__in=valid_statuses_for_invoicing # Shto kete nese deshiron te kufizosh sipas statusit
        ).exclude(
            status='cancelled'
        ).select_related('dentist', 'patient') # Optimizim

    def clean_case(self):
        case = self.cleaned_data.get('case')
        if case and hasattr(case, 'invoice') and case.invoice is not None:
             # Kontroll shtese nese rastesisht ka kaluar nje rast me fature
             raise ValidationError(
                 _("This case already has an invoice (%(invoice_id)s). Please select another case."),
                 code='case_already_invoiced',
                 params={'invoice_id': case.invoice.id}
             )
        if case and case.status == 'cancelled':
            raise ValidationError(
                _("Cannot create an invoice for a cancelled case."),
                code='case_cancelled'
            )
        # Mund te shtosh kontrolle te tjera per statusin ketu
        # if case and case.status not in ['closed', 'ready_to_ship']:
        #     raise ValidationError(_("Invoice can only be created for cases that are Ready to Ship or Closed."))
        return case
