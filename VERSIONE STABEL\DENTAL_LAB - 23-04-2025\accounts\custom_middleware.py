# accounts/custom_middleware.py

from django.core.exceptions import PermissionDenied
from django.http import HttpResponse
from django.template.loader import render_to_string

class Custom403Middleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        return self.get_response(request)

    def process_exception(self, request, exception):
        if isinstance(exception, PermissionDenied):
            print("Custom403Middleware is handling PermissionDenied")  # Shtoni këtë linjë për debugging
            return HttpResponse(render_to_string('accounts/403.html'), status=403)


# import os
# from django.conf import settings
# from django.core.exceptions import PermissionDenied
# from django.http import HttpResponse
# from django.template.loader import render_to_string

# class Custom403Middleware:
#     def __init__(self, get_response):
#         self.get_response = get_response

#     def __call__(self, request):
#         return self.get_response(request)

#     def process_exception(self, request, exception):
#         if isinstance(exception, PermissionDenied):
#             print("Custom403Middleware is handling PermissionDenied")
#             template_path = os.path.join(settings.BASE_DIR, 'accounts', 'templates', 'accounts', '403.html')
#             print(f"Looking for template at: {template_path}")
#             if os.path.exists(template_path):
#                 print(f"Template file exists at {template_path}")
#             else:
#                 print(f"Template file does not exist at {template_path}")
#             return HttpResponse(render_to_string('accounts/403.html'), status=403)