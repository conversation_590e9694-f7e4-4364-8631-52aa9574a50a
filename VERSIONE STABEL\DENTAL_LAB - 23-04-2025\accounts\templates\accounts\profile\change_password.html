{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Change Password" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12 col-md-6 offset-md-3">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Change Password" %}</h3>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        {{ form|crispy }}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                {% trans "Change Password" %}
                            </button>
                            <a href="{% url 'accounts:profile' %}" class="btn btn-secondary">
                                {% trans "Cancel" %}
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title">{% trans "Password Requirements" %}</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> {% trans "At least 8 characters long" %}</li>
                        <li><i class="fas fa-check text-success"></i> {% trans "Contains at least one uppercase letter" %}</li>
                        <li><i class="fas fa-check text-success"></i> {% trans "Contains at least one lowercase letter" %}</li>
                        <li><i class="fas fa-check text-success"></i> {% trans "Contains at least one number" %}</li>
                        <li><i class="fas fa-check text-success"></i> {% trans "Contains at least one special character" %}</li>
                        <li><i class="fas fa-exclamation-triangle text-warning"></i> {% trans "Must not be similar to your other personal information" %}</li>
                        <li><i class="fas fa-exclamation-triangle text-warning"></i> {% trans "Must not be a commonly used password" %}</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Add password strength meter functionality if needed
    $('input[type="password"]').on('keyup', function() {
        // Add your password strength validation logic here
    });
});
</script>
{% endblock %}

{% block extra_css %}
<style>
    .card {
        margin-bottom: 20px;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    
    .alert {
        margin-top: 15px;
    }
    
    .list-unstyled li {
        margin-bottom: 8px;
    }
    
    .fas {
        margin-right: 8px;
    }
</style>
{% endblock %}