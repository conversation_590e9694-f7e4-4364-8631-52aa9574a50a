# accounts/mixins.py
from django.utils import timezone
from datetime import datetime
from django.contrib.auth.mixins import UserPassesTestMixin, LoginRequiredMixin
from django.core.exceptions import PermissionDenied
from django.shortcuts import redirect
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.urls import reverse_lazy

class SuperUserRequiredMixin(UserPassesTestMixin):
    """Verify that the current user is a superuser."""
    
    def test_func(self):
        return self.request.user.is_superuser

    def handle_no_permission(self):
        if not self.request.user.is_authenticated:
            messages.error(
                self.request,
                _('Please login to access this page.')
            )
            return redirect('accounts:login')
        messages.error(
            self.request,
            _('You do not have permission to access this page.')
        )
        return redirect('home')

class StaffRequiredMixin(UserPassesTestMixin):
    """Verify that the current user is a staff member."""
    
    def test_func(self):
        return self.request.user.is_staff

    def handle_no_permission(self):
        if not self.request.user.is_authenticated:
            messages.error(
                self.request,
                _('Please login to access this page.')
            )
            return redirect('accounts:login')
        messages.error(
            self.request,
            _('Staff access required.')
        )
        return redirect('home')

class DentistRequiredMixin(UserPassesTestMixin):
    """Verify that the current user is a dentist."""
    
    def test_func(self):
        return self.request.user.is_dentist()

    def handle_no_permission(self):
        if not self.request.user.is_authenticated:
            messages.error(
                self.request,
                _('Please login to access this page.')
            )
            return redirect('accounts:login')
        messages.error(
            self.request,
            _('Dentist access required.')
        )
        return redirect('home')

class ProfileCompletionRequiredMixin(LoginRequiredMixin):
    """
    Verify that the user has completed their profile before accessing certain views.
    """
    
    def dispatch(self, request, *args, **kwargs):
        if not self.is_profile_complete(request.user):
            messages.warning(
                request,
                _('Please complete your profile before accessing this page.')
            )
            return redirect('accounts:profile')
        return super().dispatch(request, *args, **kwargs)

    def is_profile_complete(self, user):
        required_fields = [
            'first_name', 'last_name', 'phone_number',
            'address_line1', 'city', 'country'
        ]
        return all(getattr(user, field) for field in required_fields)

class OwnershipRequiredMixin(LoginRequiredMixin):
    """
    Verify that the current user owns the object they're trying to access.
    """
    
    def get_object(self, queryset=None):
        obj = super().get_object(queryset)
        if not self.is_owner(obj):
            raise PermissionDenied(_("You don't have permission to access this."))
        return obj

    def is_owner(self, obj):
        return getattr(obj, 'user_id', None) == self.request.user.id

class EmailVerificationRequiredMixin(LoginRequiredMixin):
    """
    Verify that the user's email is verified before allowing access.
    """
    
    def dispatch(self, request, *args, **kwargs):
        if not request.user.email_verified:
            messages.warning(
                request,
                _('Please verify your email address before accessing this page.')
            )
            return redirect('accounts:resend_verification')
        return super().dispatch(request, *args, **kwargs)

class PreventAuthenticatedMixin:
    """
    Prevent authenticated users from accessing certain views (like login/register).
    """
    
    def dispatch(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            messages.info(request, _('You are already logged in.'))
            return redirect('home')
        return super().dispatch(request, *args, **kwargs)

class ActivityLogMixin:
    """
    Mixin to log user activity.
    """
    
    def log_activity(self, user, action, details=None):
        from .models import UserActivityLog
        UserActivityLog.objects.create(
            user=user,
            action=action,
            ip_address=self.request.META.get('REMOTE_ADDR'),
            user_agent=self.request.META.get('HTTP_USER_AGENT'),
            details=details or {}
        )

class SessionTimeoutMixin:
    """
    Mixin to handle session timeout for sensitive views.
    """
    
    session_timeout = 300  # 5 minutes

    def dispatch(self, request, *args, **kwargs):
        if not request.session.get('last_activity'):
            request.session['last_activity'] = str(timezone.now())
        
        last_activity = datetime.fromisoformat(request.session['last_activity'])
        if (timezone.now() - last_activity).seconds > self.session_timeout:
            messages.warning(request, _('Your session has expired. Please login again.'))
            return redirect('accounts:login')
        
        request.session['last_activity'] = str(timezone.now())
        return super().dispatch(request, *args, **kwargs)