{% extends 'base.html' %}

{% block extra_css %}
<style>
    .form-container {
        background-color: #fff;
        border-radius: 0.375rem;
        padding: 1.5rem;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        margin-top: 2rem;
    }
    .form-header {
        margin-bottom: 1.5rem;
        color: #333;
        font-weight: 700;
    }
    .form-group label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        display: block;
    }
    .btn-custom {
        width: auto;
        padding: 0.5rem 1rem;
        font-size: 1rem;
        margin-right: 0.5rem;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    .btn-custom i {
        font-size: 1.2rem;
    }
    .invalid-feedback {
        display: block;
    }
    .form-group {
        margin-bottom: 1.5rem;
    }
</style>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css">
{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 form-container">
            <h2 class="form-header">Update Task {% if task.case %}for Case {{ task.case.case_number }}{% endif %}</h2>
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
            <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                {% csrf_token %}
                <div class="form-group">
                    {{ form.case.label_tag }}
                    {{ form.case }}
                    {% if form.case.errors %}
                        <div class="invalid-feedback">{{ form.case.errors.as_text }}</div>
                    {% endif %}
                </div>
                <div class="form-group">
                    {{ form.workflow_stage.label_tag }}
                    {{ form.workflow_stage }}
                    {% if form.workflow_stage.errors %}
                        <div class="invalid-feedback">{{ form.workflow_stage.errors.as_text }}</div>
                    {% endif %}
                </div>
                <div class="form-group">
                    {{ form.assigned_to.label_tag }}
                    {{ form.assigned_to }}
                    {% if form.assigned_to.errors %}
                        <div class="invalid-feedback">{{ form.assigned_to.errors.as_text }}</div>
                    {% endif %}
                </div>
                <div class="form-group">
                    {{ form.title.label_tag }}
                    {{ form.title }}
                    {% if form.title.errors %}
                        <div class="invalid-feedback">{{ form.title.errors.as_text }}</div>
                    {% endif %}
                </div>
                <div class="form-group">
                    {{ form.description.label_tag }}
                    {{ form.description }}
                    {% if form.description.errors %}
                        <div class="invalid-feedback">{{ form.description.errors.as_text }}</div>
                    {% endif %}
                </div>
                <div class="form-group">
                    {{ form.status.label_tag }}
                    {{ form.status }}
                    {% if form.status.errors %}
                        <div class="invalid-feedback">{{ form.status.errors.as_text }}</div>
                    {% endif %}
                </div>
                <div class="form-group">
                    {{ form.priority.label_tag }}
                    {{ form.priority }}
                    {% if form.priority.errors %}
                        <div class="invalid-feedback">{{ form.priority.errors.as_text }}</div>
                    {% endif %}
                </div>
                <div class="form-group">
                    {{ form.estimated_duration.label_tag }}
                    {{ form.estimated_duration }}
                    {% if form.estimated_duration.errors %}
                        <div class="invalid-feedback">{{ form.estimated_duration.errors.as_text }}</div>
                    {% endif %}
                </div>
                <div class="form-group">
                    {{ form.scheduled_start_time.label_tag }}
                    {{ form.scheduled_start_time }}
                    {% if form.scheduled_start_time.errors %}
                        <div class="invalid-feedback">{{ form.scheduled_start_time.errors.as_text }}</div>
                    {% endif %}
                </div>
                <div class="form-group">
                    {{ form.scheduled_end_time.label_tag }}
                    {{ form.scheduled_end_time }}
                    {% if form.scheduled_end_time.errors %}
                        <div class="invalid-feedback">{{ form.scheduled_end_time.errors.as_text }}</div>
                    {% endif %}
                </div>
                <div class="text-center">
                    <button type="submit" class="btn btn-primary btn-custom">
                        <i class="bi bi-check-lg"></i> Submit
                    </button>
                    {% if task.case %}
                        <a href="{% url 'case:case_detail' task.case.case_number %}" class="btn btn-secondary btn-custom">
                            <i class="bi bi-arrow-left"></i> Cancel
                        </a>
                    {% else %}
                        <a href="{% url 'case:task_list' %}" class="btn btn-secondary btn-custom">
                            <i class="bi bi-list"></i> Cancel
                        </a>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
