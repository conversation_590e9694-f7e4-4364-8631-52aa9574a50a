# accounts/decorators.py

from functools import wraps
from django.conf import settings
from django.shortcuts import redirect
import requests
from django.contrib import messages

def check_recaptcha(view_func):
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        request.recaptcha_is_valid = True
        if request.method == 'POST':
            recaptcha_response = request.POST.get('g-recaptcha-response')
            if recaptcha_response:
                try:
                    url = 'https://www.google.com/recaptcha/api/siteverify'
                    data = {
                        'secret': settings.RECAPTCHA_SECRET_KEY,
                        'response': recaptcha_response,
                        'remoteip': get_client_ip(request)
                    }
                    r = requests.post(url, data=data)
                    result = r.json()
                    request.recaptcha_is_valid = result.get('success', False)
                except:
                    request.recaptcha_is_valid = False
            else:
                request.recaptcha_is_valid = False
                
            if not request.recaptcha_is_valid:
                messages.error(request, 'Invalid reCAPTCHA. Please try again.')
        
        return view_func(request, *args, **kwargs)
    return _wrapped_view

def get_client_ip(request):
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

def user_not_authenticated(function=None, redirect_url='/'):
    """
    Decorator for views that checks that the user is NOT logged in, redirecting
    to the homepage if necessary.
    """
    def decorator(view_func):
        def _wrapped_view(request, *args, **kwargs):
            if request.user.is_authenticated:
                return redirect(redirect_url)
            return view_func(request, *args, **kwargs)
        return _wrapped_view

    if function:
        return decorator(function)
    return decorator

def allowed_users(allowed_roles=[]):
    """
    Decorator for views that checks whether a user has a particular role,
    redirecting to the home page if necessary.
    """
    def decorator(view_func):
        def wrapper_func(request, *args, **kwargs):
            user_roles = []
            if request.user.groups.exists():
                user_roles = [group.name for group in request.user.groups.all()]
            
            if any(role in allowed_roles for role in user_roles):
                return view_func(request, *args, **kwargs)
            else:
                messages.error(request, 'You are not authorized to view this page.')
                return redirect('home')
        return wrapper_func
    return decorator