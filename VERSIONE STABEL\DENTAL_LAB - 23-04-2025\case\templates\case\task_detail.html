{% extends 'base.html' %}

{% block content %}
<div class="container mt-4 animate__animated animate__fadeIn">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
            <li class="breadcrumb-item"><a href="{% url 'case:task_list' %}">Tasks</a></li>
            <li class="breadcrumb-item active" aria-current="page">Task Detail</li>
        </ol>
    </nav>

    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="text-primary">
            <i class="bi bi-clipboard-check me-2"></i>Task Details
        </h2>
        <div class="btn-group">
            <a href="{% url 'case:task_update' task.id %}" class="btn btn-outline-primary">
                <i class="bi bi-pencil-square"></i> Edit
            </a>
            <a href="{% url 'case:task_delete' task.id %}" 
               class="btn btn-outline-danger" 
               onclick="return confirm('Are you sure you want to delete this task? This action cannot be undone.');">
                <i class="bi bi-trash"></i> Delete
            </a>
        </div>
    </div>

    <!-- Main Card -->
    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    {{ task.name }}
                </h5>
                <span class="badge {% if task.status == 'completed' %}bg-success
                                 {% elif task.status == 'in_progress' %}bg-warning text-dark
                                 {% elif task.status == 'pending' %}bg-primary
                                 {% elif task.status == 'cancelled' %}bg-danger
                                 {% else %}bg-secondary{% endif %}">
                    <i class="bi bi-circle-fill me-1"></i>
                    {{ task.get_status_display }}
                </span>
            </div>
        </div>

        <div class="card-body">
            <!-- Task Details Grid -->
            <div class="row g-4">
                <!-- Left Column -->
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="text-muted small">Case Number</label>
                        <p class="h6">{{ task.case.case_number }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="text-muted small">Task ID</label>
                        <p class="h6">#{{ task.id }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="text-muted small">Assigned To</label>
                        <p class="h6">
                            <i class="bi bi-person-circle me-1"></i>
                            {{ task.assigned_to.get_full_name }}
                        </p>
                    </div>
                </div>
                
                <!-- Right Column -->
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="text-muted small">Due Date</label>
                        <p class="h6">
                            <i class="bi bi-calendar me-1"></i>
                            {{ task.due_date|date:"F j, Y" }}
                            {% if task.due_date %}
                                <small class="text-muted">
                                    ({{ task.due_date|timeuntil }} remaining)
                                </small>
                            {% endif %}
                        </p>
                    </div>
                    <div class="mb-3">
                        <label class="text-muted small">Created</label>
                        <p class="h6">
                            <i class="bi bi-clock-history me-1"></i>
                            {{ task.created_at|date:"F j, Y H:i" }}
                        </p>
                    </div>
                    <div class="mb-3">
                        <label class="text-muted small">Last Updated</label>
                        <p class="h6">
                            <i class="bi bi-clock me-1"></i>
                            {{ task.updated_at|date:"F j, Y H:i" }}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Description Section -->
            <div class="mt-4">
                <label class="text-muted small">Description</label>
                <div class="p-3 bg-light rounded">
                    {% if task.description %}
                        {{ task.description|linebreaks }}
                    {% else %}
                        <p class="text-muted mb-0"><em>No description provided</em></p>
                    {% endif %}
                </div>
            </div>

            <!-- Related Items Section -->
            {% if task.attachments.exists %}
            <div class="mt-4">
                <h6 class="mb-3">Attachments</h6>
                <div class="list-group">
                    {% for attachment in task.attachments.all %}
                    <a href="{{ attachment.file.url }}" class="list-group-item list-group-item-action">
                        <i class="bi bi-paperclip me-2"></i>
                        {{ attachment.filename }}
                    </a>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Card Footer -->
        <div class="card-footer bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <a href="{% url 'case:task_list' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-1"></i> Back to List
                </a>
                <small class="text-muted">
                    Last activity: {{ task.updated_at|timesince }} ago
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this task? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="{% url 'case:task_delete' task.id %}" method="POST" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<style>
    .card {
        transition: all 0.3s ease;
    }
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;
    }
    .badge {
        transition: all 0.3s ease;
    }
    .badge:hover {
        transform: scale(1.1);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Enable tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    })

    // Delete confirmation using modal instead of browser confirm
    document.querySelector('[data-action="delete"]').addEventListener('click', function(e) {
        e.preventDefault();
        var deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
        deleteModal.show();
    });
</script>
{% endblock %}