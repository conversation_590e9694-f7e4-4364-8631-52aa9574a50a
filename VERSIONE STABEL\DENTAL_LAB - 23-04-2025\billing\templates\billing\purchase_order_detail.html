{% extends "base.html" %}
{% load i18n %}
{% load static %}
{% load billing_filters %}

{% block title %}{% trans "Purchase Order" %} #{{ purchase_order.id }} | {% trans "Billing" %}{% endblock %}

{% block extra_css %}
<style>
  :root {
    /* Primary Colors - Fresher, Lighter Palette */
    --primary: #4285F4; /* Google Blue */
    --primary-light: rgba(66, 133, 244, 0.1);
    --secondary: #8ab4f8;
    --success: #34A853; /* Google Green */
    --success-light: rgba(52, 168, 83, 0.1);
    --danger: #EA4335; /* Google Red */
    --danger-light: rgba(234, 67, 53, 0.1);
    --warning: #FBBC05; /* Google Yellow */
    --warning-light: rgba(251, 188, 5, 0.1);
    --info: #46bdc6;
    --info-light: rgba(70, 189, 198, 0.1);
    --dark: #3c4043;
    --light: #f8f9fa;
    --white: #ffffff;

    /* Background and Text Colors */
    --bg-main: #f8f9fa;
    --text-main: #202124;
    --card-bg: #ffffff;
    --border-color: rgba(0,0,0,0.08);

    /* UI Elements */
    --shadow-sm: 0 1px 2px rgba(0,0,0,0.05);
    --shadow: 0 4px 6px rgba(0,0,0,0.05);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.05);
    --border-radius: 8px;
    --transition: all 0.3s ease;
  }

  /* Dark Mode Colors - Softer Dark Theme */
  [data-theme="dark"] {
    --primary: #8ab4f8; /* Lighter blue for dark mode */
    --primary-light: rgba(138, 180, 248, 0.15);
    --success: #81c995; /* Lighter green for dark mode */
    --success-light: rgba(129, 201, 149, 0.15);
    --danger: #f28b82; /* Lighter red for dark mode */
    --danger-light: rgba(242, 139, 130, 0.15);
    --warning: #fdd663; /* Lighter yellow for dark mode */
    --warning-light: rgba(253, 214, 99, 0.15);
    --info: #78d9ec;
    --info-light: rgba(120, 217, 236, 0.15);

    --dark: #e8eaed;
    --light: #3c4043;
    --white: #202124;

    --bg-main: #202124;
    --text-main: #e8eaed;
    --card-bg: #292a2d;
    --border-color: rgba(255,255,255,0.08);
  }

  body {
    background-color: var(--bg-main);
    color: var(--text-main);
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Card styles */
  .card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    margin-bottom: 1.5rem;
  }

  .card:hover {
    box-shadow: var(--shadow);
  }

  .card-header {
    background-color: var(--primary-light);
    color: var(--primary);
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
  }

  /* Table styles */
  .table {
    color: var(--text-main);
  }

  .table thead th {
    background-color: var(--primary-light);
    color: var(--primary);
    font-weight: 600;
    border-bottom: none;
  }

  /* Status badges */
  .badge-draft {
    background-color: var(--light);
    color: var(--dark);
  }

  .badge-submitted {
    background-color: var(--info-light);
    color: var(--info);
  }

  .badge-approved {
    background-color: var(--primary-light);
    color: var(--primary);
  }

  .badge-partially_received {
    background-color: var(--warning-light);
    color: var(--warning);
  }

  .badge-completed {
    background-color: var(--success-light);
    color: var(--success);
  }

  .badge-cancelled {
    background-color: var(--danger-light);
    color: var(--danger);
  }

  /* PO details */
  .po-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
  }

  .po-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
  }

  .po-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }

  .po-id {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .po-date {
    color: var(--text-muted);
    margin-bottom: 0.5rem;
  }

  .po-status {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
  }

  .po-parties {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
  }

  .po-party {
    flex: 1;
    max-width: 48%;
  }

  .po-party-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary);
  }

  .po-totals {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }

  .po-total-row {
    display: flex;
    justify-content: space-between;
    width: 100%;
    max-width: 300px;
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
  }

  .po-total-row:last-child {
    border-bottom: none;
    font-weight: 700;
    font-size: 1.25rem;
    margin-top: 0.5rem;
  }

  .po-total-label {
    color: var(--text-muted);
  }

  .po-total-value {
    font-weight: 600;
  }

  .po-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-top: 2rem;
  }

  /* Timeline */
  .timeline {
    position: relative;
    padding-left: 2rem;
    margin-bottom: 2rem;
  }

  .timeline::before {
    content: '';
    position: absolute;
    left: 0.5rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--border-color);
  }

  .timeline-item {
    position: relative;
    padding-bottom: 1.5rem;
  }

  .timeline-item:last-child {
    padding-bottom: 0;
  }

  .timeline-item::before {
    content: '';
    position: absolute;
    left: -2rem;
    top: 0.25rem;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    background-color: var(--primary);
  }

  .timeline-date {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: 0.25rem;
  }

  .timeline-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
  }

  .timeline-content {
    color: var(--text-main);
  }

  /* Print styles */
  @media print {
    .po-actions, .navbar, .footer, .no-print {
      display: none !important;
    }

    .container {
      width: 100%;
      max-width: 100%;
    }

    .card {
      border: none;
      box-shadow: none;
    }

    body {
      background-color: white;
      color: black;
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
  <!-- Page Header with Actions -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div class="d-flex align-items-center">
      <a href="{% url 'billing:purchase_order_list' %}" class="btn btn-outline-primary me-3">
        <i class="fas fa-arrow-left"></i>
      </a>
      <div>
        <h1 class="mb-0">{% trans "Purchase Order" %} #{{ purchase_order.id }}</h1>
        <p class="text-muted mb-0">{{ purchase_order.order_date|date:"F j, Y" }}</p>
      </div>
    </div>
    <div class="po-actions">
      <a href="{% url 'billing:purchase_order_update' purchase_order.id %}" class="btn btn-warning">
        <i class="fas fa-edit me-1"></i> {% trans "Edit" %}
      </a>
      {% if purchase_order.status == 'draft' %}
        <a href="{% url 'billing:purchase_order_delete' purchase_order.id %}" class="btn btn-danger">
          <i class="fas fa-trash me-1"></i> {% trans "Delete" %}
        </a>
      {% endif %}
      <button class="btn btn-primary print-po">
        <i class="fas fa-print me-1"></i> {% trans "Print" %}
      </button>
    </div>
  </div>

  <div class="row">
    <!-- Purchase Order Details Column -->
    <div class="col-lg-8">
      <!-- Purchase Order Information Card -->
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">{% trans "Purchase Order Information" %}</h5>
          <span class="badge badge-{{ purchase_order.status }}">{{ purchase_order.get_status_display }}</span>
        </div>
        <div class="card-body">
          <div class="row mb-4">
            <div class="col-md-6">
              <h6 class="text-primary">{% trans "Supplier" %}</h6>
              <p class="mb-1"><strong>{{ purchase_order.supplier.name }}</strong></p>
              <p class="mb-1">{{ purchase_order.supplier.address|default:"-" }}</p>
              <p class="mb-1">{{ purchase_order.supplier.email|default:"-" }}</p>
              <p class="mb-1">{{ purchase_order.supplier.phone|default:"-" }}</p>
            </div>
            <div class="col-md-6">
              <h6 class="text-primary">{% trans "Order Details" %}</h6>
              <p class="mb-1"><strong>{% trans "PO Number" %}:</strong> #{{ purchase_order.id }}</p>
              <p class="mb-1"><strong>{% trans "Order Date" %}:</strong> {{ purchase_order.order_date|date:"Y-m-d" }}</p>
              <p class="mb-1"><strong>{% trans "Expected Delivery" %}:</strong> {{ purchase_order.expected_delivery_date|date:"Y-m-d"|default:"-" }}</p>
              <p class="mb-1"><strong>{% trans "Status" %}:</strong> {{ purchase_order.get_status_display }}</p>
            </div>
          </div>

          <!-- Purchase Order Items Table -->
          <h6 class="text-primary mb-3">{% trans "Order Items" %}</h6>
          <div class="table-responsive">
            <table class="table table-bordered">
              <thead>
                <tr>
                  <th>{% trans "Raw Material" %}</th>
                  <th>{% trans "Description" %}</th>
                  <th class="text-end">{% trans "Quantity" %}</th>
                  <th class="text-end">{% trans "Unit" %}</th>
                  <th class="text-end">{% trans "Unit Price" %}</th>
                  <th class="text-end">{% trans "Total" %}</th>
                </tr>
              </thead>
              <tbody>
                {% for item in purchase_order.purchaseorderitem_set.all %}
                  <tr>
                    <td>{{ item.raw_material.name }}</td>
                    <td>{{ item.description|default:"-" }}</td>
                    <td class="text-end">{{ item.quantity }}</td>
                    <td class="text-end">{{ item.unit.name }}</td>
                    <td class="text-end">{{ item.price_per_unit }} {{ item.currency.code }}</td>
                    <td class="text-end">{{ item.price_per_unit|floatformat:2 }} × {{ item.quantity|floatformat:2 }} = {{ item.price_per_unit|multiply:item.quantity|floatformat:2 }} {{ item.currency.code }}</td>
                  </tr>
                {% empty %}
                  <tr>
                    <td colspan="6" class="text-center">{% trans "No items found" %}</td>
                  </tr>
                {% endfor %}
              </tbody>
              <tfoot>
                <tr>
                  <td colspan="4"></td>
                  <td class="text-end"><strong>{% trans "Total" %}:</strong></td>
                  <td class="text-end"><strong>{{ purchase_order.total_amount }}</strong></td>
                </tr>
              </tfoot>
            </table>
          </div>

          <!-- Notes Section -->
          {% if purchase_order.notes %}
            <div class="mt-4">
              <h6 class="text-primary mb-2">{% trans "Notes" %}</h6>
              <div class="p-3 bg-light rounded">
                {{ purchase_order.notes|linebreaks }}
              </div>
            </div>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Status and Timeline Column -->
    <div class="col-lg-4">
      <!-- Status Card -->
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">{% trans "Status Information" %}</h5>
        </div>
        <div class="card-body">
          <div class="d-flex justify-content-between mb-3">
            <span>{% trans "Current Status" %}:</span>
            <span class="badge badge-{{ purchase_order.status }}">{{ purchase_order.get_status_display }}</span>
          </div>

          <div class="progress mb-4" style="height: 10px;">
            {% if purchase_order.status == 'draft' %}
              <div class="progress-bar bg-secondary" role="progressbar" style="width: 20%;" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
            {% elif purchase_order.status == 'submitted' %}
              <div class="progress-bar bg-info" role="progressbar" style="width: 40%;" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100"></div>
            {% elif purchase_order.status == 'approved' %}
              <div class="progress-bar bg-primary" role="progressbar" style="width: 60%;" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100"></div>
            {% elif purchase_order.status == 'partially_received' %}
              <div class="progress-bar bg-warning" role="progressbar" style="width: 80%;" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100"></div>
            {% elif purchase_order.status == 'completed' %}
              <div class="progress-bar bg-success" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
            {% elif purchase_order.status == 'cancelled' %}
              <div class="progress-bar bg-danger" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
            {% endif %}
          </div>

          <!-- Status Actions -->
          <div class="d-grid gap-2">
            {% if purchase_order.status == 'draft' %}
              <button class="btn btn-primary" id="submitPO">
                <i class="fas fa-paper-plane me-1"></i> {% trans "Submit Purchase Order" %}
              </button>
            {% elif purchase_order.status == 'submitted' %}
              <button class="btn btn-primary" id="approvePO">
                <i class="fas fa-check me-1"></i> {% trans "Approve Purchase Order" %}
              </button>
              <button class="btn btn-danger" id="cancelPO">
                <i class="fas fa-times me-1"></i> {% trans "Cancel Purchase Order" %}
              </button>
            {% elif purchase_order.status == 'approved' %}
              <button class="btn btn-success" id="receivePO">
                <i class="fas fa-truck me-1"></i> {% trans "Mark as Received" %}
              </button>
              <button class="btn btn-warning" id="partialReceivePO">
                <i class="fas fa-truck me-1"></i> {% trans "Mark as Partially Received" %}
              </button>
            {% elif purchase_order.status == 'partially_received' %}
              <button class="btn btn-success" id="completePO">
                <i class="fas fa-check-circle me-1"></i> {% trans "Mark as Completed" %}
              </button>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Timeline Card -->
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">{% trans "Order Timeline" %}</h5>
        </div>
        <div class="card-body">
          <div class="timeline">
            <div class="timeline-item">
              <div class="timeline-date">{{ purchase_order.created_at|date:"Y-m-d H:i" }}</div>
              <div class="timeline-title">{% trans "Purchase Order Created" %}</div>
              <div class="timeline-content">{% trans "Initial draft created" %}</div>
            </div>

            <div class="timeline-item">
              <div class="timeline-date">{{ purchase_order.order_date|date:"Y-m-d" }}</div>
              <div class="timeline-title">{% trans "Order Date" %}</div>
              <div class="timeline-content">{% trans "Purchase order dated" %}</div>
            </div>

            {% if purchase_order.status != 'draft' %}
              <div class="timeline-item">
                <div class="timeline-date">{{ purchase_order.updated_at|date:"Y-m-d H:i" }}</div>
                <div class="timeline-title">{% trans "Status Updated" %}</div>
                <div class="timeline-content">{% trans "Status changed to" %} {{ purchase_order.get_status_display }}</div>
              </div>
            {% endif %}

            {% if purchase_order.expected_delivery_date %}
              <div class="timeline-item">
                <div class="timeline-date">{{ purchase_order.expected_delivery_date|date:"Y-m-d" }}</div>
                <div class="timeline-title">{% trans "Expected Delivery" %}</div>
                <div class="timeline-content">{% trans "Scheduled delivery date" %}</div>
              </div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Supplier Information Card -->
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">{% trans "Supplier Information" %}</h5>
        </div>
        <div class="card-body">
          <p><strong>{% trans "Name" %}:</strong> {{ purchase_order.supplier.name }}</p>
          <p><strong>{% trans "Contact Person" %}:</strong> {{ purchase_order.supplier.contact_person|default:"-" }}</p>
          <p><strong>{% trans "Email" %}:</strong> {{ purchase_order.supplier.email|default:"-" }}</p>
          <p><strong>{% trans "Phone" %}:</strong> {{ purchase_order.supplier.phone|default:"-" }}</p>
          <p><strong>{% trans "Address" %}:</strong> {{ purchase_order.supplier.address|default:"-" }}</p>

          <a href="#" class="btn btn-outline-primary btn-sm w-100 mt-2">
            <i class="fas fa-external-link-alt me-1"></i> {% trans "View Supplier Details" %}
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Print functionality
    document.querySelectorAll('.print-po').forEach(button => {
      button.addEventListener('click', function() {
        window.print();
      });
    });

    // Status update buttons
    const statusMap = {
      'submitPO': 'submitted',
      'approvePO': 'approved',
      'cancelPO': 'cancelled',
      'receivePO': 'completed',
      'partialReceivePO': 'partially_received',
      'completePO': 'completed'
    };

    const statusButtons = [
      'submitPO', 'approvePO', 'cancelPO', 'receivePO',
      'partialReceivePO', 'completePO'
    ];

    statusButtons.forEach(buttonId => {
      const button = document.getElementById(buttonId);
      if (button) {
        button.addEventListener('click', function() {
          if (confirm('{% trans "Are you sure you want to change the status of this purchase order?" %}')) {
            // Create a form and submit it to the status update URL
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{% url "billing:purchase_order_status_update" purchase_order.id %}';

            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = 'csrfmiddlewaretoken';
            csrfToken.value = '{{ csrf_token }}';
            form.appendChild(csrfToken);

            // Add status field
            const statusField = document.createElement('input');
            statusField.type = 'hidden';
            statusField.name = 'status';
            statusField.value = statusMap[buttonId];
            form.appendChild(statusField);

            // Append form to body and submit
            document.body.appendChild(form);
            form.submit();
          }
        });
      }
    });
  });
</script>
{% endblock %}
