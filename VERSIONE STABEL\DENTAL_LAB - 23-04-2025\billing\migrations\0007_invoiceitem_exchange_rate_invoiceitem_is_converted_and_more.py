# Generated by Django 5.0.4 on 2025-04-22 00:21

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("billing", "0006_purchaseorderitem_description"),
        ("items", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="invoiceitem",
            name="exchange_rate",
            field=models.DecimalField(
                blank=True,
                decimal_places=6,
                help_text="The exchange rate used for conversion.",
                max_digits=14,
                null=True,
                verbose_name="Exchange Rate",
            ),
        ),
        migrations.AddField(
            model_name="invoiceitem",
            name="is_converted",
            field=models.BooleanField(
                default=False,
                help_text="Indicates if the price has been converted from another currency.",
                verbose_name="Is Converted",
            ),
        ),
        migrations.AddField(
            model_name="invoiceitem",
            name="original_currency",
            field=models.ForeignKey(
                blank=True,
                help_text="The original currency before any conversion.",
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="invoice_items_original_currency",
                to="items.currency",
            ),
        ),
        migrations.AddField(
            model_name="invoiceitem",
            name="original_price",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                help_text="The original price before any currency conversion.",
                max_digits=10,
                null=True,
                verbose_name="Original Price",
            ),
        ),
    ]
