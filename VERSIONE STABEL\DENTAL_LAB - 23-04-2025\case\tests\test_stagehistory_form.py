from django.test import TestCase
from case.forms import *
from case.models import *
from decimal import Decimal
from django.utils import timezone

class StageHistoryFormTest(TestCase):
    """Test case for the StageHistoryForm."""
    
    def setUp(self):
        """Set up test data."""
        # TODO: Create test data
        pass
    
    def test_form_valid(self):
        """Test that the form is valid with valid data."""
        # TODO: Implement test
        pass
    
    def test_form_invalid(self):
        """Test that the form is invalid with invalid data."""
        # TODO: Implement test
        pass
