{% extends 'base.html' %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-4">Workflow Stages</h1>
    <a href="{% url 'case:workflow_stage_create' %}" class="btn btn-primary mb-3">
        <i class="bi bi-plus-lg"></i> Create New Workflow Stage
    </a>
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Name</th>
                <th>Department</th>
                <th>Order</th>
                <th>Duration</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for stage in workflow_stages %}
            <tr>
                <td>{{ stage.name }}</td>
                <td>{{ stage.department }}</td>
                <td>{{ stage.order }}</td>
                <td>{{ stage.duration }}</td>
                <td>
                    <a href="{% url 'case:workflow_stage_detail' stage.pk %}" class="btn btn-sm btn-info">
                        <i class="bi bi-eye"></i> View
                    </a>
                    <a href="{% url 'case:workflow_stage_update' stage.pk %}" class="btn btn-sm btn-warning">
                        <i class="bi bi-pencil"></i> Edit
                    </a>
                    <a href="{% url 'case:workflow_stage_delete' stage.pk %}" class="btn btn-sm btn-danger">
                        <i class="bi bi-trash"></i> Delete
                    </a>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5">No workflow stages found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}