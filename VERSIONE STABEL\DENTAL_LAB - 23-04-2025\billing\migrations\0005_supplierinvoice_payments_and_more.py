# Generated by Django 5.2 on 2025-04-19 00:10

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("billing", "0004_supplierinvoiceitem_supplierinvoice"),
        ("finance", "0003_supplierinvoicepayment"),
        ("items", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="supplierinvoice",
            name="payments",
            field=models.ManyToManyField(
                blank=True,
                related_name="supplier_invoices_paid",
                through="finance.SupplierInvoicePayment",
                to="finance.supplierpayment",
            ),
        ),
        migrations.AddField(
            model_name="supplierinvoice",
            name="purchase_order",
            field=models.ForeignKey(
                blank=True,
                help_text="Optional: The purchase order this invoice is related to.",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="supplier_invoices",
                to="billing.purchaseorder",
            ),
        ),
        migrations.AddField(
            model_name="supplierinvoiceitem",
            name="currency",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="supplier_invoice_items_priced_in",
                to="items.currency",
            ),
        ),
        migrations.AddField(
            model_name="supplierinvoiceitem",
            name="invoice",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="invoice_items",
                to="billing.supplierinvoice",
            ),
        ),
        migrations.AddField(
            model_name="supplierinvoiceitem",
            name="raw_material",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="supplier_invoice_lines",
                to="items.rawmaterial",
            ),
        ),
        migrations.AddField(
            model_name="supplierinvoiceitem",
            name="unit",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="supplier_invoice_items_in_unit",
                to="items.unit",
            ),
        ),
        migrations.AddIndex(
            model_name="supplierinvoice",
            index=models.Index(
                fields=["supplier_name", "status"],
                name="billing_sup_supplie_574f6d_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="supplierinvoice",
            index=models.Index(fields=["date"], name="billing_sup_date_3942ac_idx"),
        ),
        migrations.AddIndex(
            model_name="supplierinvoice",
            index=models.Index(
                fields=["invoice_number"], name="billing_sup_invoice_1107b9_idx"
            ),
        ),
    ]
