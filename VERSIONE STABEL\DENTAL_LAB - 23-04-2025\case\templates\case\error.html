{% extends "base.html" %}
{% load static %}

{% block title %}Error | Dashboard{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-100 flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
        <!-- Error Icon -->
        <div class="flex justify-center mb-6">
            <div class="bg-red-100 rounded-full p-4">
                <svg class="w-12 h-12 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z">
                    </path>
                </svg>
            </div>
        </div>

        <!-- Error Message -->
        <h1 class="text-2xl font-bold text-center text-gray-800 mb-4">
            An Error Occurred
        </h1>
        
        {% if error %}
        <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <p class="text-red-700">{{ error }}</p>
        </div>
        {% endif %}

        <!-- Action Buttons -->
        <div class="flex flex-col gap-3">
            <button onclick="window.history.back()" 
                    class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 
                           focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                Go Back
            </button>
            
            <a href="{% url 'home' %}" 
               class="w-full px-4 py-2 bg-gray-200 text-gray-700 rounded-md text-center hover:bg-gray-300 
                      focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                Return to Dashboard
            </a>
        </div>
    </div>
</div>
{% endblock %}