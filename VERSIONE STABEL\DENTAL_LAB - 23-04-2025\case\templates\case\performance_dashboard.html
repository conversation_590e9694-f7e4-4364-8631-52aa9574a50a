{% extends 'base.html' %}

{% block title %}Dental Lab Performance Dashboard{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/datepicker/1.0.10/datepicker.min.css">
<style>
    :root {
        --primary-color: #4e73df;
        --success-color: #1cc88a;
        --info-color: #36b9cc;
        --warning-color: #f6c23e;
        --danger-color: #e74a3b;
        --dark-color: #5a5c69;
        --light-color: #f8f9fc;
    }
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background-color: var(--light-color);
    }
    .dashboard-title {
        color: var(--dark-color);
        font-weight: 600;
        margin-bottom: 2rem;
    }
    .card {
        border-radius: 15px;
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        transition: transform 0.3s, box-shadow 0.3s;
    }
    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.3rem 2rem 0 rgba(58, 59, 69, 0.2);
    }
    .card-body {
        padding: 1.25rem;
    }
    .card-title {
        font-size: 0.9rem;
        font-weight: 600;
        text-transform: uppercase;
        margin-bottom: 0.5rem;
        color: var(--dark-color);
    }
    .kpi-value {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    .chart-container {
        position: relative;
        height: 150px;
        width: 100%;
    }
    .trend-indicator {
        font-size: 0.8rem;
        display: flex;
        align-items: center;
    }
    .trend-up::before {
        content: '▲';
        color: var(--success-color);
        margin-right: 0.25rem;
    }
    .trend-down::before {
        content: '▼';
        color: var(--danger-color);
        margin-right: 0.25rem;
    }
    .date-range-picker {
        margin-bottom: 2rem;
    }
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }
    .spinner {
        width: 50px;
        height: 50px;
        border: 5px solid var(--primary-color);
        border-top: 5px solid var(--light-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <h1 class="text-center dashboard-title">Dental Lab Performance Dashboard</h1>
    
    <div class="row mb-4">
        <div class="col-md-6 offset-md-3">
            <div class="date-range-picker d-flex justify-content-between align-items-center">
                <input type="text" id="startDate" class="form-control" placeholder="Start Date">
                <span class="mx-2">to</span>
                <input type="text" id="endDate" class="form-control" placeholder="End Date">
                <button id="updateDashboard" class="btn btn-primary ml-2">Update</button>
            </div>
        </div>
    </div>
    
    <div class="row g-4" id="kpiContainer">
        <!-- KPI cards will be dynamically inserted here -->
    </div>
</div>

<div id="loadingOverlay" class="loading-overlay" style="display: none;">
    <div class="spinner"></div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.0/chart.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/datepicker/1.0.10/datepicker.min.js"></script>
<script>
    const kpiData = [
        { chartId: 'caseProgressChart', title: 'Case Progress', value: {{ case_progress|default:0 }}, unit: '%', color: 'primary' },
        { chartId: 'taskEfficiencyChart', title: 'Task Efficiency', value: {{ task_efficiency|default:0 }}, unit: '%', color: 'success' },
        { chartId: 'avgDelayChart', title: 'Average Delay', value: {{ avg_delay|default:0 }}, unit: ' hours', color: 'warning' },
        { chartId: 'materialEfficiencyChart', title: 'Material Efficiency', value: {{ material_efficiency|default:0 }}, unit: '%', color: 'info' },
        { chartId: 'grossMarginChart', title: 'Gross Margin', value: {{ gross_margin|default:0 }}, unit: '%', color: 'success' },
        { chartId: 'outstandingPaymentsChart', title: 'Outstanding Payments', value: {{ outstanding_payments|default:0 }}, unit: '$', color: 'danger' },
        { chartId: 'onTimeCompletionChart', title: 'On-Time Completion', value: {{ on_time_completion_rate|default:0 }}, unit: '%', color: 'primary' },
        { chartId: 'avgCaseDurationChart', title: 'Avg Case Duration', value: {{ avg_case_duration|default:0 }}, unit: ' days', color: 'info' },
        { chartId: 'taskLoadChart', title: 'Avg Task Load', value: {{ avg_task_load_per_employee|default:0 }}, unit: '', color: 'warning' },
        { chartId: 'reworkRateChart', title: 'Rework Rate', value: {{ rework_rate|default:0 }}, unit: '%', color: 'danger' },
        { chartId: 'revenuePerCaseChart', title: 'Revenue per Case', value: {{ revenue_per_case|default:0 }}, unit: '$', color: 'success' },
        { chartId: 'capacityUtilizationChart', title: 'Capacity Utilization', value: {{ capacity_utilization|default:0 }}, unit: '%', color: 'primary' },
        { chartId: 'materialWasteChart', title: 'Material Waste', value: {{ material_waste_rate|default:0 }}, unit: '%', color: 'danger' },
        { chartId: 'customerSatisfactionChart', title: 'Customer Satisfaction', value: {{ avg_customer_feedback|default:0 }}, unit: '/5', color: 'success' },
        { chartId: 'avgCollectionTimeChart', title: 'Avg Collection Time', value: {{ avg_collection_time|default:0 }}, unit: ' days', color: 'warning' },
        { chartId: 'repeatBusinessChart', title: 'Repeat Business', value: {{ repeat_business_rate|default:0 }}, unit: '%', color: 'info' }
    ];

    function createKPICard(kpi) {
        return `
            <div class="col-md-3 mb-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">${kpi.title}</h5>
                        <div class="kpi-value" style="color: var(--${kpi.color}-color);">
                            ${kpi.value.toFixed(2)}${kpi.unit}
                        </div>
                        <div class="chart-container">
                            <canvas id="${kpi.chartId}"></canvas>
                        </div>
                        <div class="trend-indicator ${kpi.trend > 0 ? 'trend-up' : 'trend-down'}">
                            ${Math.abs(kpi.trend).toFixed(2)}% from last period
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    function createGradient(ctx, colorName) {
        const computedStyle = getComputedStyle(document.documentElement);
        const color = computedStyle.getPropertyValue(`--${colorName}-color`).trim();
    
        const gradient = ctx.createLinearGradient(0, 0, 0, 200);
        gradient.addColorStop(0, color);
        gradient.addColorStop(1, `${color}33`);
        return gradient;
    }

    function createChart(kpi) {
        const canvas = document.getElementById(kpi.chartId);
        if (canvas) {
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Value',
                        data: [65, 59, 80, 81, 56, kpi.value],
                        borderColor: `var(--${kpi.color}-color)`,
                        backgroundColor: createGradient(ctx, kpi.color),
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                        }
                    },
                    scales: {
                        x: {
                            display: false
                        },
                        y: {
                            display: false
                        }
                    },
                    elements: {
                        point: {
                            radius: 0
                        }
                    }
                }
            });
        } else {
            console.warn(`Canvas element with id ${kpi.chartId} not found`);
        }
    }

    function initializeDashboard() {
        const container = document.getElementById('kpiContainer');
        if (!container) {
            console.error('KPI container not found!');
            return;
        }

        let cardsHTML = '';
        kpiData.forEach(kpi => {
            kpi.trend = Math.random() * 10 - 5; // Mock trend data
            cardsHTML += createKPICard(kpi);
        });

        container.innerHTML = cardsHTML;
        kpiData.forEach(createChart);
    }

    function updateDashboard() {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        
        if (!startDate || !endDate) {
            alert('Please select both start and end dates.');
            return;
        }

        showLoading();

        // Simulating an API call
        setTimeout(() => {
            // Update KPI data with random values for demonstration
            kpiData.forEach(kpi => {
                kpi.value = Math.random() * 100;
                kpi.trend = Math.random() * 10 - 5;
            });

            initializeDashboard();
            hideLoading();
        }, 1500);
    }

    function showLoading() {
        document.getElementById('loadingOverlay').style.display = 'flex';
    }

    function hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    document.addEventListener('DOMContentLoaded', () => {
        if (typeof Chart === 'undefined') {
            console.error("Chart.js failed to load.");
            return;
        }

        initializeDashboard();

        // Initialize date pickers
        $('#startDate, #endDate').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true
        });

        // Set initial date range
        const today = new Date();
        const oneMonthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
        $('#startDate').datepicker('setDate', oneMonthAgo);
        $('#endDate').datepicker('setDate', today);

        // Add event listener for update button
        document.getElementById('updateDashboard').addEventListener('click', updateDashboard);
    });
</script>
{% endblock %}