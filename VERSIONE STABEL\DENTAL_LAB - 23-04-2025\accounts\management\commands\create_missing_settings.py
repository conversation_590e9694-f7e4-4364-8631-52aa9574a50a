# accounts/management/commands/create_missing_settings.py

from django.core.management.base import BaseCommand
from accounts.models import CustomUser, UserSettings

class Command(BaseCommand):
    help = 'Create missing UserSettings for existing users'

    def handle(self, *args, **kwargs):
        users = CustomUser.objects.all()
        created_count = 0
        
        for user in users:
            settings, created = UserSettings.objects.get_or_create(user=user)
            if created:
                created_count += 1
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {created_count} missing UserSettings'
            )
        )