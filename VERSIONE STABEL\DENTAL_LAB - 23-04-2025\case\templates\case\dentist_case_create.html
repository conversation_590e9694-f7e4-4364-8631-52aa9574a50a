{% extends 'base.html' %}

{% block title %}<PERSON><PERSON><PERSON>st të Ri{% endblock %}

{% block content %}
<style>
    .form-header {
        background-color: #007bff;
        color: white;
        padding: 20px;
        border-radius: 5px 5px 0 0;
        margin-bottom: 20px;
        text-align: center;
    }
    .form-container {
        background-color: #f8f9fa;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 0 0 5px 5px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
    .form-container h3 {
        border-bottom: 2px solid #007bff;
        padding-bottom: 10px;
        margin-bottom: 20px;
        color: #007bff;
    }
    .formset-item {
        border: 1px solid #ddd;
        padding: 15px;
        margin-bottom: 10px;
        border-radius: 5px;
        background-color: #fff;
    }
    .formset-item:last-child {
        margin-bottom: 0;
    }
    .btn-submit {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
        font-weight: bold;
        padding: 10px 20px;
        font-size: 1.1em;
    }
    .btn-submit:hover {
        background-color: #0056b3;
        border-color: #0056b3;
    }
</style>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="form-header">
                <h2>Krijo Rast të Ri</h2>
            </div>
            <div class="form-container">
                <form method="post">
                    {% csrf_token %}
                    {{ form.as_p }}
                    
                    <h3>Punimet</h3>
                    {{ formset.management_form }}
                    {% for form in formset %}
                        <div class="formset-item">
                            {{ form.as_p }}
                        </div>
                    {% endfor %}
                    
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-submit">Krijo Rastin</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script type="text/javascript">
    $(document).ready(function(){
        // Initialize datepicker for all date input fields
        $('input[type="date"]').datepicker({
            format: 'yyyy-mm-dd',  // Adjust the format as needed
            autoclose: true,
            todayHighlight: true
        });
    });
</script>
{% endblock %}
