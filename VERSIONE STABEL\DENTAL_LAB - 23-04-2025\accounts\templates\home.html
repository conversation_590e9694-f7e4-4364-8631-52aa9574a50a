{% extends "base.html" %}
{% load static humanize mathfilters %}

{% block title %}Dashboard | Dental Case Management{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
<style>
:root {
    --primary-hue: 211;
    --success-hue: 145;
    --danger-hue: 5;
    --warning-hue: 45;
    --info-hue: 185;
    --purple-hue: 270;

    /* Base Colors - Light Mode */
    --primary-light: hsl(var(--primary-hue), 90%, 58%);      /* #4285F4 */
    --success-light: hsl(var(--success-hue), 53%, 48%);      /* #34A853 */
    --danger-light: hsl(var(--danger-hue), 80%, 54%);       /* #EA4335 */
    --warning-light: hsl(var(--warning-hue), 96%, 50%);     /* #FBBC05 */
    --info-light: hsl(var(--info-hue), 56%, 52%);        /* #46bdc6 */
    --purple-light: hsl(var(--purple-hue), 88%, 64%);     /* #a142f4 */
    --dark-light: hsl(210, 5%, 25%);        /* #3c4043 */
    --gray-light: hsl(210, 5%, 40%);        /* #5f6368 */
    --light-gray-light: hsl(210, 17%, 98%);   /* #f8f9fa */
    --white-light: #ffffff;
    --border-light: hsla(0, 0%, 0%, 0.08);
    --shadow-color-light: 220, 3%, 15%;
    --bg-main-light: hsl(216, 33%, 97%);    /* #f5f7fa */
    --card-bg-light: var(--white-light);
    --text-main-light: var(--dark-light);
    --text-muted-light: var(--gray-light);

    /* Base Colors - Dark Mode */
    --primary-dark: hsl(var(--primary-hue), 85%, 75%);       /* #8ab4f8 */
    --success-dark: hsl(var(--success-hue), 45%, 65%);       /* #81c995 */
    --danger-dark: hsl(var(--danger-hue), 85%, 75%);        /* #f28b82 */
    --warning-dark: hsl(var(--warning-hue), 95%, 70%);      /* #fdd663 */
    --info-dark: hsl(var(--info-hue), 70%, 70%);         /* #78d9ec */
    --purple-dark: hsl(var(--purple-hue), 85%, 75%);      /* #c58af9 */
    --dark-dark: hsl(210, 15%, 90%);        /* #e8eaed */
    --gray-dark: hsl(210, 8%, 65%);         /* #9aa0a6 */
    --light-gray-dark: hsl(210, 5%, 25%);     /* #3c4043 */
    --white-dark: hsl(220, 4%, 13%);        /* #202124 */
    --border-dark: hsla(0, 0%, 100%, 0.08);
    --shadow-color-dark: 220, 40%, 2%;
    --bg-main-dark: var(--white-dark);
    --card-bg-dark: hsl(220, 3%, 16%);      /* #292a2d */
    --text-main-dark: var(--dark-dark);
    --text-muted-dark: var(--gray-dark);

    /* Default to Light Mode */
    --primary: var(--primary-light);
    --success: var(--success-light);
    --danger: var(--danger-light);
    --warning: var(--warning-light);
    --info: var(--info-light);
    --purple: var(--purple-light);
    --dark: var(--dark-light);
    --gray: var(--gray-light);
    --light-gray: var(--light-gray-light);
    --white: var(--white-light);
    --bg-main: var(--bg-main-light);
    --text-main: var(--text-main-light);
    --text-muted: var(--text-muted-light);
    --card-bg: var(--card-bg-light);
    --border-color: var(--border-light);
    --shadow-color: var(--shadow-color-light);

    --primary-light-alpha: hsla(var(--primary-hue), 90%, 58%, 0.1);
    --success-light-alpha: hsla(var(--success-hue), 53%, 48%, 0.1);
    --danger-light-alpha: hsla(var(--danger-hue), 80%, 54%, 0.1);
    --warning-light-alpha: hsla(var(--warning-hue), 96%, 50%, 0.1);
    --info-light-alpha: hsla(var(--info-hue), 56%, 52%, 0.1);
    --purple-light-alpha: hsla(var(--purple-hue), 88%, 64%, 0.1);

    --primary-dark-alpha: hsla(var(--primary-hue), 85%, 75%, 0.15);
    --success-dark-alpha: hsla(var(--success-hue), 45%, 65%, 0.15);
    --danger-dark-alpha: hsla(var(--danger-hue), 85%, 75%, 0.15);
    --warning-dark-alpha: hsla(var(--warning-hue), 95%, 70%, 0.15);
    --info-dark-alpha: hsla(var(--info-hue), 70%, 70%, 0.15);
    --purple-dark-alpha: hsla(var(--purple-hue), 85%, 75%, 0.15);

    --primary-alpha: var(--primary-light-alpha);
    --success-alpha: var(--success-light-alpha);
    --danger-alpha: var(--danger-light-alpha);
    --warning-alpha: var(--warning-light-alpha);
    --info-alpha: var(--info-light-alpha);
    --purple-alpha: var(--purple-light-alpha);

    /* Sizing & Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-xxl: 3rem;
    --border-radius-sm: 6px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;

    /* Shadows */
    --shadow-sm: 0 1px 2px hsla(var(--shadow-color), 0.05), 0 1px 3px hsla(var(--shadow-color), 0.1);
    --shadow-md: 0 4px 6px -1px hsla(var(--shadow-color), 0.1), 0 2px 4px -1px hsla(var(--shadow-color), 0.06);
    --shadow-lg: 0 10px 15px -3px hsla(var(--shadow-color), 0.1), 0 4px 6px -2px hsla(var(--shadow-color), 0.05);
    --shadow-xl: 0 20px 25px -5px hsla(var(--shadow-color), 0.1), 0 10px 10px -5px hsla(var(--shadow-color), 0.04);

    /* Transitions */
    --transition-fast: all 0.2s ease-in-out;
    --transition-base: all 0.3s ease-in-out;

    /* Font */
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Dark Mode Theme */
[data-theme="dark"] {
    --primary: var(--primary-dark);
    --success: var(--success-dark);
    --danger: var(--danger-dark);
    --warning: var(--warning-dark);
    --info: var(--info-dark);
    --purple: var(--purple-dark);
    --dark: var(--dark-dark);
    --gray: var(--gray-dark);
    --light-gray: var(--light-gray-dark);
    --white: var(--white-dark);
    --bg-main: var(--bg-main-dark);
    --text-main: var(--text-main-dark);
    --text-muted: var(--text-muted-dark);
    --card-bg: var(--card-bg-dark);
    --border-color: var(--border-dark);
    --shadow-color: var(--shadow-color-dark);

    --primary-alpha: var(--primary-dark-alpha);
    --success-alpha: var(--success-dark-alpha);
    --danger-alpha: var(--danger-dark-alpha);
    --warning-alpha: var(--warning-dark-alpha);
    --info-alpha: var(--info-dark-alpha);
    --purple-alpha: var(--purple-dark-alpha);
}

*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px; /* Base font size */
}

body {
    background-color: var(--bg-main);
    font-family: var(--font-sans);
    color: var(--text-main);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color var(--transition-base), color var(--transition-base);
}

.page-container {
    padding: var(--space-lg) var(--space-xl);
    max-width: 1600px;
    margin: 0 auto;
}

/* Header */
.dashboard-header {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-xl);
    gap: var(--space-md);
}

.dashboard-title-wrapper {
    flex-basis: 100%; /* Full width on small screens */
}
@media (min-width: 992px) {
    .dashboard-title-wrapper {
        flex-basis: auto; /* Auto width on larger screens */
    }
}

.dashboard-title {
    margin: 0;
    font-weight: 700;
    font-size: 1.875rem; /* 30px */
    color: var(--text-main);
    position: relative;
    padding-bottom: var(--space-sm);
}

.dashboard-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 4px;
    background: var(--primary);
    border-radius: 2px;
}

.dashboard-subtitle {
    color: var(--text-muted);
    margin-top: var(--space-xs);
    font-size: 1rem;
    font-weight: 400;
}

.dashboard-actions {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-md);
    align-items: center;
    flex-grow: 1;
    justify-content: flex-end;
}

.input-group-wrapper {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    background: var(--card-bg);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
}

.input-group-wrapper:focus-within {
    box-shadow: 0 0 0 2px var(--primary-alpha);
    border-color: var(--primary);
}

.input-group-wrapper i {
    color: var(--primary);
    font-size: 1.1rem;
}

.input-control {
    border: none;
    background: transparent;
    font-size: 0.875rem; /* 14px */
    color: var(--text-main);
    padding: var(--space-xs);
    outline: none;
    flex-grow: 1;
}

.search-input {
    min-width: 200px;
}

.input-control::placeholder {
    color: var(--text-muted);
    opacity: 0.7;
}

.action-buttons {
    display: flex;
    gap: var(--space-sm);
}

.btn-action {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: var(--card-bg);
    color: var(--text-muted);
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
    cursor: pointer;
    box-shadow: var(--shadow-sm);
    font-size: 1.1rem;
}

.btn-action:hover, .btn-action:focus {
    background: var(--primary);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary);
    outline: none;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.metric-card {
    background: var(--card-bg);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    padding: var(--space-md) var(--space-lg);
    transition: var(--transition-base);
    display: flex;
    align-items: flex-start;
    position: relative;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: var(--accent-color, var(--primary));
    transition: var(--transition-base);
    opacity: 0;
    border-top-left-radius: var(--border-radius-md);
    border-bottom-left-radius: var(--border-radius-md);
}

.metric-card:hover::before {
    opacity: 1;
}

.metric-card.accent-primary { --accent-color: var(--primary); }
.metric-card.accent-success { --accent-color: var(--success); }
.metric-card.accent-danger { --accent-color: var(--danger); }
.metric-card.accent-warning { --accent-color: var(--warning); }
.metric-card.accent-info { --accent-color: var(--info); }
.metric-card.accent-purple { --accent-color: var(--purple); }

.metric-icon {
    width: 52px;
    height: 52px;
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--space-md);
    flex-shrink: 0;
}

.metric-icon i {
    font-size: 1.75rem; /* 28px */
}

.metric-content {
    flex: 1;
}

.metric-label {
    font-size: 0.875rem; /* 14px */
    color: var(--text-muted);
    margin-bottom: var(--space-xs);
    display: block;
    font-weight: 500;
}

.metric-value {
    font-size: 1.875rem; /* 30px */
    font-weight: 700;
    color: var(--text-main);
    margin-bottom: var(--space-sm);
    display: flex;
    align-items: baseline;
    line-height: 1.2;
}

.metric-value .unit {
    font-size: 0.875rem; /* 14px */
    color: var(--text-muted);
    margin-left: var(--space-xs);
    font-weight: 500;
}

.metric-comparison {
    font-size: 0.8125rem; /* 13px */
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    color: var(--text-muted);
}

.trend-up { color: var(--success); }
.trend-down { color: var(--danger); }
.trend-neutral { color: var(--text-muted); }

.metric-comparison i {
    font-size: 1rem;
    position: relative;
    top: 1px;
}

.icon-primary { background-color: var(--primary-alpha); color: var(--primary); }
.icon-success { background-color: var(--success-alpha); color: var(--success); }
.icon-danger { background-color: var(--danger-alpha); color: var(--danger); }
.icon-warning { background-color: var(--warning-alpha); color: var(--warning); }
.icon-info { background-color: var(--info-alpha); color: var(--info); }
.icon-purple { background-color: var(--purple-alpha); color: var(--purple); }

/* General Card Styles */
.card {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--space-lg);
    overflow: hidden;
    transition: var(--transition-base);
    border: 1px solid var(--border-color);
}

.card:hover {
    box-shadow: var(--shadow-lg);
}

.card-header {
    background: var(--card-bg);
    padding: var(--space-md) var(--space-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: 1.125rem; /* 18px */
    font-weight: 600;
    color: var(--text-main);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.card-title i {
    color: var(--primary);
    font-size: 1.2em;
}

.card-body {
    padding: var(--space-lg);
}

.card-body.p-0 { padding: 0; }

/* Charts */
.chart-container {
    position: relative;
    height: 320px;
    width: 100%;
}
.chart-container-doughnut {
    height: 280px; /* Slightly smaller for doughnuts */
    max-width: 280px;
    margin: 0 auto;
}

/* Tab Controls */
.tab-controls {
    display: inline-flex;
    background: var(--bg-main);
    border-radius: var(--border-radius-sm);
    padding: var(--space-xs);
    border: 1px solid var(--border-color);
}

.tab-control {
    border: none;
    background: none;
    padding: var(--space-sm) var(--space-md);
    font-size: 0.875rem; /* 14px */
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
    color: var(--text-muted);
    font-weight: 500;
}

.tab-control.active {
    background: var(--card-bg);
    color: var(--primary);
    box-shadow: var(--shadow-sm);
}

/* Tables */
.table-responsive {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-size: 0.875rem; /* 14px */
}

.data-table th,
.data-table td {
    padding: var(--space-md) var(--space-lg);
    text-align: left;
    vertical-align: middle;
    color: var(--text-main);
    white-space: nowrap;
}

.data-table thead th {
    background: var(--light-gray);
    font-size: 0.75rem; /* 12px */
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--text-muted);
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table thead th:first-child {
    border-top-left-radius: var(--border-radius-lg);
}

.data-table thead th:last-child {
    border-top-right-radius: var(--border-radius-lg);
}

.data-table tbody tr {
    transition: background-color var(--transition-fast);
}

.data-table tbody tr:hover {
    background-color: var(--light-gray);
}

.data-table tbody tr:not(:last-child) {
    border-bottom: 1px solid var(--border-color);
}

.data-table tbody td:first-child {
    border-left: 3px solid transparent;
    transition: border-color var(--transition-fast);
}
.data-table tbody tr:hover td:first-child {
    border-left-color: var(--primary);
}

.case-number {
    font-weight: 600;
    color: var(--primary);
    text-decoration: none;
}

.case-number:hover {
    text-decoration: underline;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-weight: 500;
    font-size: 0.75rem; /* 12px */
    line-height: 1;
    white-space: nowrap;
    text-transform: capitalize;
}

.status-badge i {
    margin-right: 0.35rem;
    font-size: 0.7rem;
    position: relative;
    top: -1px;
}

.status-pending_acceptance { background-color: var(--warning-alpha); color: var(--warning); }
.status-in_progress { background-color: var(--info-alpha); color: var(--info); }
.status-on_hold { background-color: hsla(var(--gray-hue, 210), 10%, 50%, 0.2); color: var(--gray); } /* Assuming gray-hue exists */
.status-ready_to_ship { background-color: var(--purple-alpha); color: var(--purple); }
.status-shipped { background-color: var(--success-alpha); color: var(--success); }
.status-delivered { background-color: var(--success-alpha); color: var(--success); }
.status-closed { background-color: hsla(var(--dark-hue, 210), 10%, 50%, 0.15); color: var(--text-muted); } /* Assuming dark-hue exists */
.status-default { background-color: hsla(0, 0%, 50%, 0.1); color: var(--text-muted); }

.dentist-info {
    display: flex;
    align-items: center;
}

.avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: var(--white);
    background-color: var(--primary); /* Default color */
    font-size: 0.875rem; /* 14px */
    margin-right: var(--space-md);
    flex-shrink: 0;
    text-transform: uppercase;
}
/* Add more avatar colors based on first letter or random */
.avatar-A, .avatar-G, .avatar-M, .avatar-S, .avatar-Y { background-color: var(--success); }
.avatar-B, .avatar-H, .avatar-N, .avatar-T, .avatar-Z { background-color: var(--info); }
.avatar-C, .avatar-I, .avatar-O, .avatar-U { background-color: var(--warning); color: var(--dark-light); }
.avatar-D, .avatar-J, .avatar-P, .avatar-V { background-color: var(--danger); }
.avatar-E, .avatar-K, .avatar-Q, .avatar-W { background-color: var(--purple); }
/* Default: F, L, R, X -> var(--primary) */

.dentist-details {
    display: flex;
    flex-direction: column;
    line-height: 1.3;
}

.dentist-name {
    font-weight: 500;
    color: var(--text-main);
}

.dentist-clinic {
    font-size: 0.8125rem; /* 13px */
    color: var(--text-muted);
}

.actions-group {
    display: flex;
    gap: var(--space-sm);
    align-items: center;
}

/* Notifications */
.notification-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 0;
    margin: 0;
    list-style: none;
}

.notification-item {
    display: flex;
    padding: var(--space-md) var(--space-lg);
    border-bottom: 1px solid var(--border-color);
    transition: background-color var(--transition-fast);
    background-color: var(--card-bg);
    gap: var(--space-md);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item:hover {
    background-color: var(--light-gray);
}

.notification-important { border-left: 4px solid var(--primary); padding-left: calc(var(--space-lg) - 4px); }
.notification-urgent { border-left: 4px solid var(--danger); padding-left: calc(var(--space-lg) - 4px); }

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    color: var(--text-muted);
}
.notification-icon i { font-size: 1.2rem; }

.notification-important .notification-icon { background-color: var(--primary-alpha); color: var(--primary); }
.notification-urgent .notification-icon { background-color: var(--danger-alpha); color: var(--danger); }

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: var(--text-main);
    margin-bottom: var(--space-xs);
    font-size: 0.9375rem; /* 15px */
}

.notification-text {
    font-size: 0.875rem; /* 14px */
    color: var(--text-muted);
    margin-bottom: var(--space-xs);
}

.notification-time {
    font-size: 0.75rem; /* 12px */
    color: var(--text-muted);
    opacity: 0.8;
}

.notification-actions {
    display: flex;
    align-items: center;
    margin-left: auto; /* Push actions to the right */
}
.notification-actions .btn {
    font-size: 0.8rem;
    padding: 0.3rem 0.7rem;
}
.btn-primary { background-color: var(--primary); color: var(--white); border: none; border-radius: var(--border-radius-sm); cursor: pointer; }
.btn-outline-secondary { background-color: transparent; color: var(--text-muted); border: 1px solid var(--border-color); border-radius: var(--border-radius-sm); cursor: pointer; }

/* Empty State */
.empty-state {
    padding: var(--space-xxl) var(--space-xl);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    text-align: center;
}

.empty-state i {
    font-size: 3.5rem;
    margin-bottom: var(--space-md);
    opacity: 0.4;
    color: var(--gray);
}

.empty-state-text {
    font-size: 1rem;
    font-weight: 500;
    margin: 0;
}

/* Ready to Ship Banner */
.ready-to-ship-banner {
    background: linear-gradient(105deg, var(--success-alpha) 0%, var(--card-bg) 70%);
    border-radius: var(--border-radius-lg);
    padding: var(--space-lg);
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    border-left: 5px solid var(--success);
}

.banner-content {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    flex-grow: 1;
}

.banner-icon {
    width: 60px;
    height: 60px;
    background: var(--card-bg);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    color: var(--success);
    font-size: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.banner-text h3 {
    font-size: 1.25rem; /* 20px */
    font-weight: 600;
    margin: 0 0 var(--space-xs) 0;
    color: var(--text-main);
}

.banner-text p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.9375rem; /* 15px */
}

.banner-text strong {
    color: var(--text-main);
    font-weight: 600;
}

.banner-action {
    margin-left: var(--space-md);
    margin-top: var(--space-sm); /* Add margin for wrap */
}

.banner-action .btn-ship {
    background: var(--success);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius-sm);
    padding: 0.625rem 1.25rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    box-shadow: var(--shadow-sm);
}

.banner-action .btn-ship:hover {
    background: hsl(var(--success-hue), 53%, 40%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}
.banner-action .btn-ship i { font-size: 1em; }

/* Utility Classes */
.mb-4 { margin-bottom: var(--space-lg); }
.me-2 { margin-right: var(--space-sm); }
.p-0 { padding: 0 !important; }
.fw-semibold { font-weight: 600 !important; }
.text-center { text-align: center; }
.d-flex { display: flex !important; }
.align-items-center { align-items: center !important; }
.justify-content-center { justify-content: center !important; }
.h-100 { height: 100% !important; }

/* Bootstrap Grid (Simplified) */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-left: -calc(var(--space-lg) / 2);
    margin-right: -calc(var(--space-lg) / 2);
}
.col-12, .col-lg-4, .col-lg-8 {
    width: 100%;
    padding-left: calc(var(--space-lg) / 2);
    padding-right: calc(var(--space-lg) / 2);
}
@media (min-width: 992px) {
    .col-lg-4 { flex: 0 0 33.3333%; max-width: 33.3333%; }
    .col-lg-8 { flex: 0 0 66.6667%; max-width: 66.6667%; }
}

/* Alert Styles */
.alert {
    padding: var(--space-md);
    margin-bottom: var(--space-lg);
    border: 1px solid transparent;
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}
.alert-danger {
    color: var(--danger);
    background-color: var(--danger-alpha);
    border-color: var(--danger);
}
.alert i { font-size: 1.2em; }

/* Responsiveness */
@media (max-width: 991.98px) {
    .page-container {
        padding: var(--space-md) var(--space-lg);
    }
    .metrics-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: var(--space-md);
    }
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
    }
    .dashboard-actions {
        width: 100%;
        justify-content: flex-start;
        margin-top: var(--space-md);
    }
    .search-input {
        min-width: 150px;
    }
    .banner-content {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-md);
    }
    .banner-icon { margin-right: 0; }
    .ready-to-ship-banner { flex-direction: column; align-items: stretch; }
    .banner-action { align-self: flex-end; margin-left: 0; margin-top: var(--space-md); }
    .notification-item { flex-wrap: wrap; }
    .notification-actions { margin-left: 0; margin-top: var(--space-sm); width: 100%; justify-content: flex-end; }
}

@media (max-width: 767.98px) {
    .metrics-grid {
        grid-template-columns: 1fr 1fr; /* Two columns on medium screens */
    }
    .metric-card { padding: var(--space-md); }
    .metric-icon { width: 44px; height: 44px; }
    .metric-value { font-size: 1.625rem; }
    .tab-controls { width: 100%; display: flex; }
    .tab-control { flex: 1; text-align: center; }
    .chart-container { height: 280px; }
    .chart-container-doughnut { height: 240px; max-width: 240px; }
    .search-input { min-width: 0; width: 100%; }
    .input-group-wrapper { width: 100%; }
    .dashboard-actions { gap: var(--space-sm); }
    .action-buttons { margin-left: auto; /* Push to right */}
    .data-table th, .data-table td { padding: var(--space-sm) var(--space-md); }
}

@media (max-width: 575.98px) {
    .page-container { padding: var(--space-sm) var(--space-md); }
    .metrics-grid {
        grid-template-columns: 1fr; /* Single column on small screens */
    }
    .dashboard-title { font-size: 1.6rem; }
    .dashboard-subtitle { font-size: 0.9rem; }
    .banner-text h3 { font-size: 1.1rem; }
    .banner-text p { font-size: 0.875rem; }
    .banner-action .btn-ship { width: 100%; text-align: center; justify-content: center; }
}

</style>
{% endblock %}

{% block content %}
<div class="page-container">
    <header class="dashboard-header">
        <div class="dashboard-title-wrapper">
            <h1 class="dashboard-title">Dashboard</h1>
            <p class="dashboard-subtitle">Your Dental Case Management Overview</p>
        </div>
        <div class="dashboard-actions">
            <div class="input-group-wrapper">
                <i class="bi bi-search"></i>
                <input type="text" class="input-control search-input" placeholder="Search cases..." id="globalSearch" aria-label="Search cases">
                <button class="btn-action" title="Search" onclick="performSearch()">
                    <i class="bi bi-arrow-right"></i>
                </button>
            </div>
            <div class="input-group-wrapper">
                <i class="bi bi-calendar3"></i>
                <select class="input-control date-filter" id="dateRangeFilter" aria-label="Select Date Range">
                    <option value="7" {% if selected_range == '7' %}selected{% endif %}>Last 7 Days</option>
                    <option value="30" {% if selected_range == '30' %}selected{% endif %}>Last 30 Days</option>
                    <option value="180" {% if selected_range == '180' %}selected{% endif %}>Last 6 Months</option>
                    <option value="365" {% if selected_range == '365' %}selected{% endif %}>Last 12 Months</option>
                </select>
            </div>
            <div class="action-buttons">
                <button id="darkModeToggle" class="btn-action" title="Toggle Theme" aria-label="Toggle Dark Mode">
                    <i class="bi bi-moon-stars"></i>
                </button>
                <button class="btn-action" title="Refresh Data" onclick="window.location.reload();">
                    <i class="bi bi-arrow-clockwise"></i>
                </button>
                {# <button class="btn-action" title="Export Data"> #}
                {#     <i class="bi bi-download"></i> #}
                {# </button> #}
            </div>
        </div>
    </header>

    {% if error %}
    <div class="alert alert-danger" role="alert">
        <i class="bi bi-exclamation-triangle-fill me-2"></i> {{ error }} Please check system logs or contact support.
    </div>
    {% else %}

    <!-- Metrics Grid -->
    <div class="metrics-grid">
        <div class="metric-card accent-primary">
            <div class="metric-icon icon-primary"><i class="bi bi-folder2-open"></i></div>
            <div class="metric-content">
                <span class="metric-label">Total Cases</span>
                <div class="metric-value">{{ total_cases_count|intcomma }}</div>
                <div class="metric-comparison"><i class="bi bi-info-circle"></i> All-time record</div>
            </div>
        </div>

        <div class="metric-card accent-success">
            <div class="metric-icon icon-success"><i class="bi bi-calendar-plus"></i></div>
            <div class="metric-content">
                <span class="metric-label">Cases Today</span>
                <div class="metric-value">{{ cases_today_count|intcomma }}</div>
                <div class="metric-comparison">New cases received</div>
            </div>
        </div>

        <div class="metric-card accent-info">
            <div class="metric-icon icon-info"><i class="bi bi-calendar-week"></i></div>
            <div class="metric-content">
                <span class="metric-label">This Week</span>
                <div class="metric-value">{{ cases_this_week_count|intcomma }}</div>
                <div class="metric-comparison">
                    {% if week_difference is not None and week_difference != 0 %}
                        {% if week_difference > 0 %}
                            <span class="trend-up"><i class="bi bi-arrow-up-short"></i> {{ week_difference|abs|intcomma }} more</span>
                        {% elif week_difference < 0 %}
                            <span class="trend-down"><i class="bi bi-arrow-down-short"></i> {{ week_difference|abs|intcomma }} less</span>
                        {% endif %}
                        than last week
                    {% elif week_difference == 0 %}
                        <span class="trend-neutral"><i class="bi bi-dash"></i> Same as last week</span>
                    {% else %}
                        <span class="trend-neutral"><i class="bi bi-info-circle"></i> No comparison data</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="metric-card accent-warning">
            <div class="metric-icon icon-warning"><i class="bi bi-calendar-month"></i></div>
            <div class="metric-content">
                <span class="metric-label">This Month</span>
                <div class="metric-value">{{ cases_this_month_count|intcomma }}</div>
                 <div class="metric-comparison">
                    {% if month_difference is not None and month_difference != 0 %}
                        {% if month_difference > 0 %}
                            <span class="trend-up"><i class="bi bi-arrow-up-short"></i> {{ month_difference|abs|intcomma }} more</span>
                        {% elif month_difference < 0 %}
                            <span class="trend-down"><i class="bi bi-arrow-down-short"></i> {{ month_difference|abs|intcomma }} less</span>
                        {% endif %}
                        than last month
                    {% elif month_difference == 0 %}
                        <span class="trend-neutral"><i class="bi bi-dash"></i> Same as last month</span>
                    {% else %}
                        <span class="trend-neutral"><i class="bi bi-info-circle"></i> No comparison data</span>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="metric-card accent-danger">
            <div class="metric-icon icon-danger"><i class="bi bi-exclamation-octagon"></i></div>
            <div class="metric-content">
                <span class="metric-label">Overdue Cases</span>
                <div class="metric-value">{{ overdue_cases_count|intcomma }}</div>
                <div class="metric-comparison">Past their deadline</div>
            </div>
        </div>

        <div class="metric-card accent-purple">
            <div class="metric-icon icon-purple"><i class="bi bi-box-seam"></i></div>
            <div class="metric-content">
                <span class="metric-label">Ready to Ship</span>
                <div class="metric-value">{{ ready_to_ship_count|intcomma }}</div>
                <div class="metric-comparison">Awaiting shipment</div>
            </div>
        </div>

        <div class="metric-card accent-success">
            <div class="metric-icon icon-success"><i class="bi bi-stopwatch"></i></div>
            <div class="metric-content">
                <span class="metric-label">Avg. Completion</span>
                <div class="metric-value">{{ avg_completion_days|floatformat:1 }}<span class="unit">days</span></div>
                <div class="metric-comparison">Based on selected range</div>
            </div>
        </div>

        <div class="metric-card accent-primary">
            <div class="metric-icon icon-primary"><i class="bi bi-check2-circle"></i></div>
            <div class="metric-content">
                <span class="metric-label">On-Time Rate</span>
                <div class="metric-value">{{ on_time_completion_rate|floatformat:1 }}<span class="unit">%</span></div>
                <div class="metric-comparison">Based on selected range</div>
            </div>
        </div>
    </div>

    <!-- Notifications Section -->
    {% if notifications %}
    <div class="notifications-container mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title"><i class="bi bi-bell"></i>Notifications</h5>
                <button class="btn-action" title="Mark All as Read" aria-label="Mark All Notifications as Read">
                    <i class="bi bi-check-all"></i>
                </button>
            </div>
            <div class="card-body p-0">
                <ul class="notification-list">
                    {% for notification in notifications %}
                    <li class="notification-item {% if notification.is_important %}notification-important{% elif notification.is_urgent %}notification-urgent{% endif %}">
                        <div class="notification-icon">
                            <i class="{{ notification.icon_class|default:'bi bi-info-circle' }}"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-title">{{ notification.title }}</div>
                            <div class="notification-text">{{ notification.text }}</div>
                            <div class="notification-time">{{ notification.timestamp|timesince }} ago</div>
                        </div>
                        {% if notification.action_url %}
                        <div class="notification-actions">
                            <a href="{{ notification.action_url }}" class="btn {{ notification.action_class|default:'btn-primary' }} btn-sm">{{ notification.action_text|default:'View' }}</a>
                        </div>
                        {% endif %}
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Ready to Ship Banner -->
    {% if ready_to_ship_count > 0 %}
    <div class="ready-to-ship-banner">
        <div class="banner-content">
            <div class="banner-icon"><i class="bi bi-box-seam"></i></div>
            <div class="banner-text">
                <h3>Ready to Ship Cases</h3>
                <p>You have <strong>{{ ready_to_ship_count }} {{ ready_to_ship_count|pluralize:"case,cases" }}</strong> ready for shipment. Process them for timely delivery.</p>
            </div>
        </div>
        <div class="banner-action">
            <a href="{% url 'case:case_list' %}?status=ready_to_ship" class="btn-ship">Process Shipments <i class="bi bi-arrow-right"></i></a>
        </div>
    </div>
    {% endif %}

    <!-- Charts Row 1: Trend -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card" style="border-radius: var(--border-radius-lg); overflow: hidden; box-shadow: var(--shadow-lg);">
                <div class="card-header" style="background: linear-gradient(135deg, var(--primary-alpha) 0%, transparent 100%); border-bottom: 2px solid var(--primary-alpha);">
                    <h5 class="card-title" style="font-size: 1.25rem; font-weight: 700;"><i class="bi bi-graph-up" style="color: var(--primary); margin-right: 10px;"></i>Case Volume Trends</h5>
                    <div class="tab-controls" style="background: var(--card-bg); border: 1px solid var(--primary-alpha); box-shadow: var(--shadow-sm);">
                        <button class="tab-control active" id="trend30DaysBtn" aria-pressed="true" style="font-weight: 600;">Last 30 Days</button>
                        <button class="tab-control" id="trend12MonthsBtn" aria-pressed="false" style="font-weight: 600;">Last 12 Months</button>
                    </div>
                </div>
                <div class="card-body" style="padding: var(--space-xl); background: linear-gradient(180deg, var(--card-bg) 0%, var(--bg-main) 100%);">
                    <div class="chart-container" style="height: 380px; margin-top: 10px;">
                        <canvas id="trendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 2: Breakdowns -->
    <div class="row">
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title"><i class="bi bi-pie-chart"></i>Cases by Status</h5>
                </div>
                <div class="card-body d-flex align-items-center justify-content-center">
                    <div class="chart-container chart-container-doughnut">
                         {% if status_chart_data %}
                            <canvas id="statusChart"></canvas>
                         {% else %}
                            <div class="empty-state text-center">
                                <i class="bi bi-bar-chart-line"></i>
                                <p class="empty-state-text">No status data available</p>
                            </div>
                         {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title"><i class="bi bi-diagram-3"></i>Cases by Priority</h5>
                </div>
                <div class="card-body d-flex align-items-center justify-content-center">
                    <div class="chart-container chart-container-doughnut">
                         {% if priority_chart_data %}
                            <canvas id="priorityChart"></canvas>
                         {% else %}
                            <div class="empty-state text-center">
                                <i class="bi bi-bar-chart-line"></i>
                                <p class="empty-state-text">No priority data available</p>
                            </div>
                         {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title"><i class="bi bi-building"></i>Cases by Department</h5>
                </div>
                <div class="card-body">
                     {% if department_chart_data %}
                        <div class="chart-container" style="height: 280px;"> <!-- Adjust height for bar chart -->
                            <canvas id="departmentChart"></canvas>
                        </div>
                     {% else %}
                        <div class="empty-state text-center" style="padding-top: 2rem; padding-bottom: 2rem;">
                            <i class="bi bi-bar-chart-line"></i>
                            <p class="empty-state-text">No department data available</p>
                        </div>
                     {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Tables Row -->
    <div class="row">
        <div class="col-lg-8 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title"><i class="bi bi-journal-text"></i>Latest Cases</h5>
                    <a href="{% url 'case:case_list' %}" class="btn btn-sm btn-outline-primary" style="font-size: 0.8rem; padding: 0.3rem 0.7rem;">View All</a>
                </div>
                <div class="card-body p-0">
                    {% if latest_cases %}
                    <div class="table-responsive">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Case #</th>
                                    <th>Patient</th>
                                    <th>Dentist</th>
                                    <th>Status</th>
                                    <th>Received</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for case in latest_cases %}
                                <tr>
                                    <td><a href="{% url 'case:case_detail' case.pk %}" class="case-number">{{ case.case_number }}</a></td>
                                    <td>{{ case.patient.get_full_name|default:"N/A" }}</td>
                                    <td>{{ case.dentist.get_full_name|default:"N/A" }}</td>
                                    <td>
                                        <span class="status-badge status-{{ case.status|lower|slugify|default:'default' }}">
                                            <i class="bi
                                                {% if case.status == 'pending_acceptance' %}bi-hourglass-split
                                                {% elif case.status == 'in_progress' %}bi-gear
                                                {% elif case.status == 'on_hold' %}bi-pause-fill
                                                {% elif case.status == 'ready_to_ship' %}bi-box
                                                {% elif case.status == 'shipped' %}bi-truck
                                                {% elif case.status == 'delivered' %}bi-check2-circle
                                                {% elif case.status == 'closed' %}bi-archive
                                                {% else %}bi-question-circle{% endif %}"></i>
                                            {{ case.get_status_display|default:case.status }}
                                        </span>
                                    </td>
                                    <td>{{ case.received_date_time|date:"d M, Y"|default:"N/A" }}</td>
                                    <td>
                                        <div class="actions-group">
                                            <a href="{% url 'case:case_detail' case.pk %}" class="btn-action" title="View Details" aria-label="View Case {{ case.case_number }}">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="{% url 'case:case_update' case.pk %}" class="btn-action" title="Edit Case" aria-label="Edit Case {{ case.case_number }}">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="empty-state">
                        <i class="bi bi-folder-x"></i>
                        <p class="empty-state-text">No recent cases found in the selected range</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title"><i class="bi bi-person-badge"></i>Top Dentists</h5>
                    <a href="{% url 'Dentists:dentist_list' %}" class="btn btn-sm btn-outline-primary" style="font-size: 0.8rem; padding: 0.3rem 0.7rem;">View All</a>
                </div>
                <div class="card-body p-0">
                    {% if top_dentists %}
                    <div class="table-responsive">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Dentist</th>
                                    <th class="text-center">Cases</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for dentist in top_dentists %}
                                {% with initial=dentist.get_full_name|first|upper %}
                                <tr>
                                    <td>
                                        <div class="dentist-info">
                                            <div class="avatar avatar-{{ initial }}">{{ initial }}</div>
                                            <div class="dentist-details">
                                                <span class="dentist-name">{{ dentist.get_full_name }}</span>
                                                <span class="dentist-clinic">{{ dentist.clinic_name|default:"" }}</span>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <span class="fw-semibold">{{ dentist.total_cases_last_year|intcomma }}</span>
                                    </td>
                                    <td>
                                        <a href="{% url 'Dentists:dentist_detail' dentist.pk %}" class="btn-action" title="View Dentist Profile" aria-label="View Profile for {{ dentist.get_full_name }}">
                                            <i class="bi bi-person"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endwith %}
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="empty-state">
                        <i class="bi bi-people"></i>
                        <p class="empty-state-text">No active dentists found</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Store the trend data in JSON format -->
    {{ trend_30_days_json|json_script:"trend-30-data" }}
    {{ trend_12_months_json|json_script:"trend-12-data" }}

    {% endif %} {# End of {% if not error %} #}
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@2.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/esm/index.js" type="module"></script>

<script type="module">
    // import { format, parseISO } from 'https://cdn.jsdelivr.net/npm/date-fns@2.29.3/esm/index.js'; // Not strictly needed if adapter works

    document.addEventListener('DOMContentLoaded', function() {
        console.log("DOM Loaded. Initializing dashboard JS...");

        // --- Theme Detection and Handling ---
        const darkModeToggle = document.getElementById('darkModeToggle');
        const htmlElement = document.documentElement;
        const prefersDarkScheme = window.matchMedia("(prefers-color-scheme: dark)");
        const currentTheme = localStorage.getItem("theme");
        const initialTheme = currentTheme ? currentTheme : (prefersDarkScheme.matches ? "dark" : "light");
        let currentChartColors; // Declare variable here
        const charts = {}; // Store chart instances

        function getChartColors(theme) {
            const isDark = theme === 'dark';
            // Temporarily set attribute to get correct computed styles IF not already set
            const currentHtmlTheme = htmlElement.getAttribute('data-theme');
            if (!currentHtmlTheme) {
                 htmlElement.setAttribute('data-theme', theme);
            }

            const style = getComputedStyle(document.documentElement);
            const colors = {
                primary: style.getPropertyValue('--primary').trim(),
                success: style.getPropertyValue('--success').trim(),
                danger: style.getPropertyValue('--danger').trim(),
                warning: style.getPropertyValue('--warning').trim(),
                info: style.getPropertyValue('--info').trim(),
                purple: style.getPropertyValue('--purple').trim(),
                gridColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.07)',
                ticksColor: isDark ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
                tooltipBg: isDark ? 'rgba(40, 42, 45, 0.9)' : 'rgba(30, 41, 59, 0.9)',
                tooltipColor: isDark ? '#e8eaed' : '#ffffff',
                legendColor: isDark ? 'rgba(255, 255, 255, 0.8)' : 'rgba(0, 0, 0, 0.7)',
                cardBg: style.getPropertyValue('--card-bg').trim(), // Get card background for borders
            };

            // Restore original theme if we temporarily set it
            if (!currentHtmlTheme) {
                htmlElement.removeAttribute('data-theme');
            } else {
                htmlElement.setAttribute('data-theme', currentHtmlTheme); // Ensure it's set back if it existed
            }

            return colors;
        }

        // Initialize currentChartColors BEFORE the first call to setTheme
        currentChartColors = getChartColors(initialTheme);

        function updateChartDefaults() {
            Chart.defaults.font.family = "'Inter', -apple-system, BlinkMacSystemFont, sans-serif";
            Chart.defaults.font.size = 13;
            Chart.defaults.plugins.legend.labels.usePointStyle = true;
            Chart.defaults.plugins.legend.position = 'bottom';
            Chart.defaults.plugins.tooltip.backgroundColor = currentChartColors.tooltipBg;
            Chart.defaults.plugins.tooltip.titleColor = currentChartColors.tooltipColor;
            Chart.defaults.plugins.tooltip.bodyColor = currentChartColors.tooltipColor;
            Chart.defaults.plugins.tooltip.titleFont = { weight: '600', size: 14 };
            Chart.defaults.plugins.tooltip.bodyFont = { size: 13 };
            Chart.defaults.plugins.tooltip.padding = 12;
            Chart.defaults.plugins.tooltip.cornerRadius = 8;
            Chart.defaults.plugins.tooltip.displayColors = false; // Cleaner look
            Chart.defaults.plugins.tooltip.boxPadding = 6;
        }

        function updateExistingChartOptions() {
             // Update existing charts options that depend on theme colors
            Object.values(charts).forEach(chart => {
                if (chart?.options) { // Check if chart and options exist
                    if(chart.options.plugins?.legend?.labels) {
                        chart.options.plugins.legend.labels.color = currentChartColors.legendColor;
                    }
                    if (chart.options.scales?.x) {
                        if(chart.options.scales.x.grid) chart.options.scales.x.grid.color = currentChartColors.gridColor;
                        if(chart.options.scales.x.ticks) chart.options.scales.x.ticks.color = currentChartColors.ticksColor;
                    }
                    if (chart.options.scales?.y) {
                       if(chart.options.scales.y.grid) chart.options.scales.y.grid.color = currentChartColors.gridColor;
                       if(chart.options.scales.y.ticks) chart.options.scales.y.ticks.color = currentChartColors.ticksColor;
                    }

                     // Update doughnut/pie chart border colors to match new card background
                     if (chart.config.type === 'doughnut' || chart.config.type === 'pie') {
                         chart.config.data.datasets.forEach(dataset => {
                             dataset.borderColor = currentChartColors.cardBg;
                             dataset.hoverBorderColor = currentChartColors.cardBg;
                         });
                     }

                    chart.update(); // Redraw the chart with updated options
                }
            });
        }

        function setTheme(theme) {
            htmlElement.setAttribute('data-theme', theme); // Set the data-theme attribute first
            currentChartColors = getChartColors(theme); // THEN get the new colors
            updateChartDefaults(); // Update Chart.js defaults
            updateExistingChartOptions(); // Update options of existing charts

            if (darkModeToggle) {
                 if (theme === "dark") {
                    darkModeToggle.querySelector('i').classList.remove('bi-moon-stars');
                    darkModeToggle.querySelector('i').classList.add('bi-brightness-high'); // Sun icon
                    darkModeToggle.setAttribute('aria-pressed', 'true');
                } else {
                    darkModeToggle.querySelector('i').classList.remove('bi-brightness-high');
                    darkModeToggle.querySelector('i').classList.add('bi-moon-stars');
                    darkModeToggle.setAttribute('aria-pressed', 'false');
                }
            }
            localStorage.setItem('theme', theme);
        }

        // Set initial theme
        setTheme(initialTheme); // This now initializes defaults and options correctly

        // Listener for toggle button
        if (darkModeToggle) {
            darkModeToggle.addEventListener('click', () => {
                const newTheme = htmlElement.getAttribute('data-theme') === 'dark' ? 'light' : 'dark';
                setTheme(newTheme);
            });
        }

        // Listener for OS theme changes
        prefersDarkScheme.addEventListener('change', (e) => {
            // Only change if no theme is manually set in localStorage
            if (!localStorage.getItem("theme")) {
                setTheme(e.matches ? "dark" : "light");
            }
        });

        // --- Helper Function for Percentage Tooltip ---
        const getOrCreateTooltip = (chart) => {
            let tooltipEl = chart.canvas.parentNode.querySelector('div.chartjs-tooltip');

            if (!tooltipEl) {
                tooltipEl = document.createElement('div');
                tooltipEl.classList.add('chartjs-tooltip');
                tooltipEl.style.opacity = 1; // Start visible
                tooltipEl.style.pointerEvents = 'none';
                tooltipEl.style.position = 'absolute';
                tooltipEl.style.transform = 'translate(-50%, 0)';
                tooltipEl.style.transition = 'all .1s ease';
                tooltipEl.style.zIndex = '10';
                // Apply styles based on currentChartColors
                tooltipEl.style.background = currentChartColors.tooltipBg;
                tooltipEl.style.borderRadius = '8px';
                tooltipEl.style.color = currentChartColors.tooltipColor;
                tooltipEl.style.padding = '10px';
                tooltipEl.style.fontSize = '13px';
                tooltipEl.style.fontFamily = Chart.defaults.font.family;
                tooltipEl.style.boxShadow = '0 3px 8px rgba(0,0,0,0.15)';


                const table = document.createElement('table');
                table.style.margin = '0px';

                tooltipEl.appendChild(table);
                // Append to parentNode to ensure it's within relative positioning context
                chart.canvas.parentNode.appendChild(tooltipEl);
            } else {
                 // Update styles if theme changed
                 tooltipEl.style.background = currentChartColors.tooltipBg;
                 tooltipEl.style.color = currentChartColors.tooltipColor;
            }

            return tooltipEl;
        };

        const externalTooltipHandler = (context) => {
            const {chart, tooltip} = context;
            const tooltipEl = getOrCreateTooltip(chart);

            // Hide if no tooltip
            if (tooltip.opacity === 0) {
                tooltipEl.style.opacity = 0;
                return;
            }

            // Set Text
            if (tooltip.body) {
                const titleLines = tooltip.title || [];
                const bodyLines = tooltip.body.map(b => b.lines);

                // Get data points info
                const dataIndex = tooltip.dataPoints[0]?.dataIndex;
                const datasetIndex = tooltip.dataPoints[0]?.datasetIndex;

                // Check if indices are valid
                if (dataIndex === undefined || datasetIndex === undefined) {
                    tooltipEl.style.opacity = 0; // Hide tooltip if indices are invalid
                    return;
                }

                const tableHead = document.createElement('thead');
                tableHead.style.fontWeight = '600';

                titleLines.forEach(title => {
                    const tr = document.createElement('tr');
                    tr.style.borderWidth = 0;

                    const th = document.createElement('th');
                    th.style.borderWidth = 0;
                    th.style.textAlign = 'left';
                    th.style.paddingBottom = '5px';

                    const text = document.createTextNode(title);

                    th.appendChild(text);
                    tr.appendChild(th);
                    tableHead.appendChild(tr);
                });

                const tableBody = document.createElement('tbody');
                bodyLines.forEach((body, i) => {
                    // Ensure labelColors exists and has the current index
                    if (!tooltip.labelColors || !tooltip.labelColors[i]) return;

                    const colors = tooltip.labelColors[i];
                    const dataset = chart.config.data.datasets[datasetIndex];

                     // Ensure dataset and data exist and index is valid
                    if (!dataset || !dataset.data || dataset.data.length <= dataIndex) return;

                    const dataPoint = dataset.data[dataIndex]; // Could be {x,y} or just y
                    const value = typeof dataPoint === 'object' ? dataPoint.y : dataPoint; // Get the y value

                     // Ensure value is valid
                     if (value === undefined || value === null) return;

                    const total = chart.config.data.datasets[datasetIndex].data.reduce((acc, curr) => {
                        // Handle both {x,y} and simple y values in total calculation
                        const yVal = typeof curr === 'object' ? curr.y : curr;
                        return acc + (Number(yVal) || 0);
                     }, 0); // Sum numbers

                    const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;

                    const span = document.createElement('span');
                    span.style.background = colors.backgroundColor;
                    span.style.borderColor = colors.borderColor;
                    span.style.borderWidth = '2px';
                    span.style.marginRight = '8px';
                    span.style.height = '10px';
                    span.style.width = '10px';
                    span.style.borderRadius = '50%';
                    span.style.display = 'inline-block';

                    const tr = document.createElement('tr');
                    tr.style.backgroundColor = 'inherit';
                    tr.style.borderWidth = 0;

                    const td = document.createElement('td');
                    td.style.borderWidth = 0;
                    td.style.padding = '3px 0';

                    // Use label from the chart data configuration
                    const label = chart.config.data.labels[dataIndex] || '';
                    const formattedValue = typeof value === 'number' ? value.toLocaleString() : value;
                    const text = document.createTextNode(`${label}: ${formattedValue} (${percentage}%)`);

                    td.appendChild(span);
                    td.appendChild(text);
                    tr.appendChild(td);
                    tableBody.appendChild(tr);
                });

                const tableRoot = tooltipEl.querySelector('table');

                // Remove old children
                while (tableRoot.firstChild) {
                    tableRoot.firstChild.remove();
                }

                // Add new children
                tableRoot.appendChild(tableHead);
                tableRoot.appendChild(tableBody);
            }

            const {offsetLeft: positionX, offsetTop: positionY} = chart.canvas;

            // Display, position, and set styles for font
            tooltipEl.style.opacity = 1;
            tooltipEl.style.left = positionX + tooltip.caretX + 'px';
            tooltipEl.style.top = positionY + tooltip.caretY + 'px';
            tooltipEl.style.font = `${Chart.defaults.font.style || 'normal'} ${Chart.defaults.font.weight || 'normal'} ${Chart.defaults.font.size || 13}px ${Chart.defaults.font.family || 'sans-serif'}`;
            tooltipEl.style.padding = Chart.defaults.plugins.tooltip.padding + 'px';
        };


        // --- Status Chart (Doughnut) ---
        const statusCtx = document.getElementById('statusChart')?.getContext('2d');
        if (statusCtx) {
            const statusDataRaw = [ {% for item in status_chart_data %}{ label: "{{ item.status|default:'Unknown'|title|escapejs }}", value: {{ item.total|default:0 }} }, {% endfor %} ];
            if (statusDataRaw.length > 0 && statusDataRaw.some(item => item.value > 0)) {
                charts.status = new Chart(statusCtx, {
                    type: 'doughnut',
                    data: {
                        labels: statusDataRaw.map(item => item.label),
                        datasets: [{
                            data: statusDataRaw.map(item => item.value),
                            backgroundColor: [ currentChartColors.primary, currentChartColors.success, currentChartColors.warning, currentChartColors.danger, currentChartColors.purple, currentChartColors.info, '#6c757d', '#adb5bd' ].slice(0, statusDataRaw.length),
                            borderWidth: 2, borderColor: currentChartColors.cardBg, hoverOffset: 8, hoverBorderColor: currentChartColors.cardBg
                        }]
                    },
                    options: {
                        responsive: true, maintainAspectRatio: false, cutout: '70%',
                        plugins: {
                            legend: { labels: { padding: 15, color: currentChartColors.legendColor, boxWidth: 12, font: { size: 12 } } },
                            tooltip: { enabled: false, external: externalTooltipHandler }
                        }
                    }
                });
            } else { statusCtx.parentNode.innerHTML = '<div class="empty-state text-center"><i class="bi bi-pie-chart"></i><p class="empty-state-text">No status data</p></div>'; }
        }

        // --- Priority Chart (Doughnut) ---
        const priorityCtx = document.getElementById('priorityChart')?.getContext('2d');
        if (priorityCtx) {
            const priorityDataRaw = [ {% for item in priority_chart_data %}{ label: "{{ item.priority|default:'Unknown'|escapejs }}", value: {{ item.total|default:0 }} }, {% endfor %} ];
            const priorityOrder = {'Urgent': 1, 'High': 2, 'Medium': 3, 'Low': 4};
            priorityDataRaw.sort((a, b) => (priorityOrder[a.label] || 99) - (priorityOrder[b.label] || 99));
            if (priorityDataRaw.length > 0 && priorityDataRaw.some(item => item.value > 0)) {
                const priorityColorsMap = { 'Urgent': currentChartColors.danger, 'High': currentChartColors.warning, 'Medium': currentChartColors.info, 'Low': currentChartColors.success, 'Default': currentChartColors.purple };
                charts.priority = new Chart(priorityCtx, {
                    type: 'doughnut',
                    data: {
                        labels: priorityDataRaw.map(item => item.label),
                        datasets: [{
                            data: priorityDataRaw.map(item => item.value),
                            backgroundColor: priorityDataRaw.map(item => priorityColorsMap[item.label] || priorityColorsMap['Default']),
                            borderWidth: 2, borderColor: currentChartColors.cardBg, hoverOffset: 8, hoverBorderColor: currentChartColors.cardBg
                        }]
                    },
                    options: {
                        responsive: true, maintainAspectRatio: false, cutout: '70%',
                        plugins: {
                            legend: { labels: { padding: 15, color: currentChartColors.legendColor, boxWidth: 12, font: { size: 12 } } },
                            tooltip: { enabled: false, external: externalTooltipHandler }
                        }
                    }
                });
            } else { priorityCtx.parentNode.innerHTML = '<div class="empty-state text-center"><i class="bi bi-diagram-3"></i><p class="empty-state-text">No priority data</p></div>'; }
        }

        // --- Department Chart (Bar) ---
        const departmentCtx = document.getElementById('departmentChart')?.getContext('2d');
        if (departmentCtx) {
            const departmentDataRaw = [ {% for item in department_chart_data %}{ label: "{{ item.responsible_department__name|default:'Unassigned'|escapejs }}", value: {{ item.total_cases|default:0 }} }, {% endfor %} ];
            if (departmentDataRaw.length > 0 && departmentDataRaw.some(item => item.value > 0)) {
                departmentDataRaw.sort((a, b) => b.value - a.value);
                const backgroundColors = [ currentChartColors.primary, currentChartColors.info, currentChartColors.purple, currentChartColors.success, currentChartColors.warning, currentChartColors.danger, '#6c757d', '#adb5bd' ];
                charts.department = new Chart(departmentCtx, {
                    type: 'bar',
                    data: {
                        labels: departmentDataRaw.map(item => item.label),
                        datasets: [{
                            label: 'Cases', data: departmentDataRaw.map(item => item.value),
                            backgroundColor: backgroundColors.slice(0, departmentDataRaw.length).map(color => `${color}E6`),
                            borderColor: backgroundColors.slice(0, departmentDataRaw.length),
                            borderWidth: 1, borderRadius: 6, maxBarThickness: 30
                        }]
                    },
                    options: {
                        indexAxis: 'y', responsive: true, maintainAspectRatio: false,
                        scales: {
                            x: { beginAtZero: true, grid: { display: true, drawBorder: false, color: currentChartColors.gridColor, drawOnChartArea: true, }, ticks: { precision: 0, color: currentChartColors.ticksColor, font: { size: 11 } } },
                            y: { grid: { display: false }, ticks: { color: currentChartColors.ticksColor, font: { size: 12 } } }
                        },
                        plugins: {
                            legend: { display: false },
                            tooltip: { enabled: true, callbacks: { label: (ctx) => ` Cases: ${ctx.parsed.x !== undefined ? ctx.parsed.x.toLocaleString() : 'N/A'}` }}
                        }
                    }
                });
            } else { departmentCtx.parentNode.innerHTML = '<div class="empty-state text-center"><i class="bi bi-building"></i><p class="empty-state-text">No department data</p></div>'; }
        }


        // --- Trend Chart (Line) ---
        console.log("Setting up Trend Chart...");
        console.log("Chart.js Date Adapter:", Chart._adapters._date ? 'Registered' : 'NOT Registered!');

        const trendCanvasElement = document.getElementById('trendChart');
        const trendCtx = trendCanvasElement?.getContext('2d');
        const trend30DataElement = document.getElementById('trend-30-data');
        const trend12DataElement = document.getElementById('trend-12-data');

        let trend30DataRaw = null;
        let trend12DataRaw = null;
        let formatted30DayData = [];
        let formatted12MonthData = [];

        // Robust Data Retrieval and Parsing
        if (trend30DataElement) { try { trend30DataRaw = JSON.parse(trend30DataElement.textContent || '[]'); if (!Array.isArray(trend30DataRaw)) { console.error("Parsed 30 Day Data is NOT an array"); trend30DataRaw = []; } } catch (e) { console.error("Error parsing 30 Day JSON:", e); trend30DataRaw = []; } } else { console.error("Element '#trend-30-data' not found!"); trend30DataRaw = []; }
        if (trend12DataElement) { try { trend12DataRaw = JSON.parse(trend12DataElement.textContent || '[]'); if (!Array.isArray(trend12DataRaw)) { console.error("Parsed 12 Month Data is NOT an array"); trend12DataRaw = []; } } catch (e) { console.error("Error parsing 12 Month JSON:", e); trend12DataRaw = []; } } else { console.error("Element '#trend-12-data' not found!"); trend12DataRaw = []; }


        if (trendCtx) {
            try {
                // Format data explicitly
                // Format 30-day data
                formatted30DayData = trend30DataRaw.map(item => {
                    const parsedDate = Date.parse(item.date);
                    console.log(`Parsing 30-day date: ${item.date} -> ${parsedDate}`);
                    return { x: parsedDate, y: item.count };
                }).filter(item => {
                    const isValid = item && typeof item.x === 'number' && !isNaN(item.x);
                    if (!isValid) console.warn(`Invalid 30-day data point: ${JSON.stringify(item)}`);
                    return isValid;
                });

                // Format 12-month data
                formatted12MonthData = trend12DataRaw.map(item => {
                    // Ensure we have a valid date string
                    const dateStr = item.month || '';
                    console.log('Raw 12-month item:', item);

                    // Try to parse the date
                    let parsedDate;
                    try {
                        parsedDate = Date.parse(dateStr);
                        console.log(`Parsing 12-month date: ${dateStr} -> ${parsedDate}`);

                        // If parsing failed, try to create a date from year and month
                        if (isNaN(parsedDate) && item.month) {
                            // Try to extract year and month if it's in a different format
                            const parts = item.month.split('-');
                            if (parts.length >= 2) {
                                const year = parseInt(parts[0]);
                                const month = parseInt(parts[1]) - 1; // JS months are 0-based
                                const newDate = new Date(year, month, 1);
                                parsedDate = newDate.getTime();
                                console.log(`Created date from parts: ${year}-${month+1} -> ${parsedDate}`);
                            }
                        }
                    } catch (err) {
                        console.error(`Error parsing date ${dateStr}:`, err);
                        parsedDate = NaN;
                    }

                    return {
                        x: parsedDate,
                        y: item.count || 0 // Ensure count is a number
                    };
                }).filter(item => {
                    const isValid = item && typeof item.x === 'number' && !isNaN(item.x);
                    if (!isValid) console.warn(`Invalid 12-month data point: ${JSON.stringify(item)}`);
                    return isValid;
                });

                // Sort the data by date
                formatted12MonthData.sort((a, b) => a.x - b.x);

                console.log("Formatted 30 Day Data Points:", formatted30DayData.length);
                console.log("Formatted 12 Month Data Points:", formatted12MonthData.length);

                if (formatted30DayData.length > 0 || formatted12MonthData.length > 0) {
                    const initialData = formatted30DayData.length > 0 ? formatted30DayData : formatted12MonthData;
                    const initialUnit = formatted30DayData.length > 0 ? 'day' : 'month';

                    console.log(`Initializing Trend chart with ${initialUnit} data.`);

                    // **** ENHANCED Trend Chart Config ****
                    const trendConfig = {
                        type: 'line',
                        data: {
                            datasets: [{
                                label: 'New Cases',
                                data: initialData,
                                borderColor: currentChartColors.primary,
                                backgroundColor: (context) => {
                                    const chart = context.chart;
                                    const {ctx, chartArea} = chart;
                                    if (!chartArea) return null;

                                    // Parse the primary color to get RGB values
                                    let primaryColor = currentChartColors.primary;
                                    let r, g, b;

                                    // Handle different color formats
                                    if (primaryColor.startsWith('#')) {
                                        // Handle hex format
                                        const hex = primaryColor.substring(1);
                                        r = parseInt(hex.substring(0, 2), 16);
                                        g = parseInt(hex.substring(2, 4), 16);
                                        b = parseInt(hex.substring(4, 6), 16);
                                    } else if (primaryColor.startsWith('rgb')) {
                                        // Handle rgb/rgba format
                                        const rgbMatch = primaryColor.match(/\d+/g);
                                        if (rgbMatch && rgbMatch.length >= 3) {
                                            r = parseInt(rgbMatch[0]);
                                            g = parseInt(rgbMatch[1]);
                                            b = parseInt(rgbMatch[2]);
                                        }
                                    } else if (primaryColor.startsWith('hsl')) {
                                        // For HSL, we'll use a default fallback color
                                        r = 66; g = 133; b = 244; // Default blue
                                    } else {
                                        // Fallback
                                        r = 66; g = 133; b = 244; // Default blue
                                    }

                                    // Create gradient with proper rgba values
                                    const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
                                    gradient.addColorStop(0, `rgba(${r}, ${g}, ${b}, 0.1)`);
                                    gradient.addColorStop(0.5, `rgba(${r}, ${g}, ${b}, 0.4)`);
                                    gradient.addColorStop(1, `rgba(${r}, ${g}, ${b}, 0.6)`);
                                    return gradient;
                                },
                                borderWidth: 3,
                                pointRadius: 0,
                                pointHitRadius: 20,
                                pointHoverRadius: 6,
                                pointBackgroundColor: currentChartColors.cardBg,
                                pointBorderColor: currentChartColors.primary,
                                pointBorderWidth: 3,
                                pointHoverBackgroundColor: currentChartColors.cardBg,
                                pointHoverBorderColor: currentChartColors.primary,
                                pointHoverBorderWidth: 4,
                                tension: 0.4,
                                fill: true,
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            animation: {
                                duration: 1500,
                                easing: 'easeOutQuart'
                            },
                            scales: {
                                x: {
                                    type: 'time',
                                    adapters: {
                                        date: {}
                                    },
                                    time: {
                                        unit: initialUnit,
                                        tooltipFormat: initialUnit === 'day' ? 'MMM d, yyyy' : 'MMM yyyy',
                                        displayFormats: {
                                            day: 'd MMM',
                                            month: 'MMM yy'
                                        }
                                    },
                                    grid: {
                                        display: false,
                                        drawBorder: false
                                    },
                                    ticks: {
                                        color: currentChartColors.ticksColor,
                                        major: { enabled: true },
                                        maxRotation: 0,
                                        autoSkip: true,
                                        autoSkipPadding: 30,
                                        font: {
                                            size: 11,
                                            weight: 500
                                        }
                                    },
                                    border: {
                                        display: false
                                    }
                                },
                                y: {
                                    beginAtZero: true,
                                    grid: {
                                        color: currentChartColors.gridColor,
                                        drawBorder: false,
                                        lineWidth: 0.5,
                                        borderDash: [3, 3]
                                    },
                                    ticks: {
                                        precision: 0,
                                        color: currentChartColors.ticksColor,
                                        font: {
                                            size: 11,
                                            weight: 500
                                        },
                                        padding: 10,
                                        maxTicksLimit: 6,
                                        callback: function(value) {
                                            if (Number.isInteger(value)) { return value.toLocaleString(); }
                                        }
                                    },
                                    border: {
                                        display: false
                                    }
                                }
                            },
                            plugins: {
                                legend: { display: false },
                                tooltip: {
                                    enabled: true,
                                    intersect: false,
                                    mode: 'index',
                                    position: 'nearest',
                                    backgroundColor: currentChartColors.tooltipBg,
                                    titleFont: {
                                        size: 14,
                                        weight: 'bold'
                                    },
                                    bodyFont: {
                                        size: 13
                                    },
                                    padding: 12,
                                    cornerRadius: 8,
                                    caretSize: 6,
                                    boxPadding: 6,
                                    callbacks: {
                                        title: function(tooltipItems) {
                                            if (tooltipItems.length > 0) {
                                                const date = new Date(tooltipItems[0].parsed.x);
                                                const unit = charts.trend?.options?.scales?.x?.time?.unit || 'day';
                                                const options = unit === 'month'
                                                    ? { year: 'numeric', month: 'long' }
                                                    : { year: 'numeric', month: 'long', day: 'numeric' };
                                                return !isNaN(date) ? date.toLocaleDateString(undefined, options) : 'Invalid Date';
                                            } return '';
                                        },
                                        label: function(context) {
                                            return ` Cases: ${context.parsed.y !== undefined ? context.parsed.y.toLocaleString() : 'N/A'}`;
                                        }
                                    }
                                }
                            },
                            interaction: {
                                mode: 'nearest',
                                axis: 'x',
                                intersect: false
                            }
                        }
                    };
                    // **** End of Refined Config ****

                    charts.trend = new Chart(trendCtx, trendConfig);
                    console.log("Trend chart instance created:", charts.trend);

                    // --- Trend Chart Toggle ---
                    const trend30Btn = document.getElementById('trend30DaysBtn');
                    const trend12Btn = document.getElementById('trend12MonthsBtn');

                    function updateTrendChart(data, unit) {
                        console.log(`Updating trend chart to ${unit} view with data points:`, data.length);
                        if (charts.trend) {
                            try {
                                if (data && data.length > 0) {
                                    // Log data for debugging
                                    console.log(`First data point: ${JSON.stringify(data[0])}`);
                                    console.log(`Last data point: ${JSON.stringify(data[data.length-1])}`);

                                    // Update chart data
                                    charts.trend.data.datasets[0].data = data;

                                    // Update time unit and formats
                                    charts.trend.options.scales.x.time.unit = unit;
                                    charts.trend.options.scales.x.time.tooltipFormat = unit === 'day' ? 'MMM d, yyyy' : 'MMM yyyy';
                                    charts.trend.options.scales.x.time.displayFormats = {
                                        day: 'd MMM',
                                        month: 'MMM yy'
                                    };

                                    // Force complete redraw
                                    charts.trend.update('reset');
                                    console.log("Trend chart updated successfully.");
                                } else {
                                    charts.trend.data.datasets[0].data = [];
                                    charts.trend.options.scales.x.time.unit = unit;
                                    charts.trend.options.scales.x.time.tooltipFormat = unit === 'day' ? 'MMM d, yyyy' : 'MMM yyyy';
                                    charts.trend.update('reset');
                                    console.warn(`No data available for ${unit} view. Chart cleared.`);
                                }
                            } catch (err) {
                                console.error(`Error updating trend chart to ${unit} view:`, err);
                            }
                        } else {
                            console.error("Trend chart instance not found for update.");
                        }
                    }

                    trend30Btn?.addEventListener('click', () => {
                        console.log('30-day button clicked');
                        console.log('30-day data:', formatted30DayData);
                        if (formatted30DayData && formatted30DayData.length > 0) {
                            updateTrendChart(formatted30DayData, 'day');
                            trend30Btn.classList.add('active');
                            trend30Btn.setAttribute('aria-pressed', 'true');
                            trend12Btn?.classList.remove('active');
                            trend12Btn?.setAttribute('aria-pressed', 'false');
                        } else {
                            console.log("No 30-day data to switch to.");
                        }
                    });

                    trend12Btn?.addEventListener('click', () => {
                        console.log('12-month button clicked');
                        console.log('12-month data:', formatted12MonthData);
                        if (formatted12MonthData && formatted12MonthData.length > 0) {
                            updateTrendChart(formatted12MonthData, 'month');
                            trend12Btn.classList.add('active');
                            trend12Btn.setAttribute('aria-pressed', 'true');
                            trend30Btn?.classList.remove('active');
                            trend30Btn?.setAttribute('aria-pressed', 'false');
                        } else {
                            console.log("No 12-month data to switch to.");
                        }
                    });

                    // Set initial active button state
                    if (initialUnit === 'day' && formatted30DayData.length > 0) { trend30Btn?.classList.add('active'); trend30Btn?.setAttribute('aria-pressed', 'true'); }
                    else if (initialUnit === 'month' && formatted12MonthData.length > 0) { trend12Btn?.classList.add('active'); trend12Btn?.setAttribute('aria-pressed', 'true'); }
                    else { trend30Btn?.classList.add('active'); trend30Btn?.setAttribute('aria-pressed', 'true'); } // Default

                } else {
                    console.warn("Trend chart not created: No valid data points found after formatting.");
                    if (trendCanvasElement?.parentNode) { trendCanvasElement.parentNode.innerHTML = '<div class="empty-state text-center"><i class="bi bi-graph-down"></i><p class="empty-state-text">No trend data available</p></div>'; }
                }
            } catch (e) {
                console.error("Error processing or creating trend chart:", e);
                if (trendCanvasElement?.parentNode) { trendCanvasElement.parentNode.innerHTML = '<div class="empty-state text-center"><i class="bi bi-exclamation-triangle"></i><p class="empty-state-text">Error loading trend data</p></div>'; }
            }
        } else {
            console.warn("Trend chart canvas element ('#trendChart') not found or context could not be obtained.");
            if(trendCanvasElement?.parentNode) { trendCanvasElement.parentNode.innerHTML = '<div class="empty-state text-center"><i class="bi bi-graph-down"></i><p class="empty-state-text">Chart canvas not found</p></div>'; }
            else if (document.getElementById('trendChart')) { console.error("Canvas element found, but failed to get 2D context."); document.getElementById('trendChart').parentNode.innerHTML = '<div class="empty-state text-center"><i class="bi bi-exclamation-triangle"></i><p class="empty-state-text">Failed to initialize chart context</p></div>'; }
        }


        // --- Date range filter & Search ---
        document.getElementById('dateRangeFilter')?.addEventListener('change', function() { const days = this.value; const currentUrl = new URL(window.location.href); currentUrl.searchParams.set('range', days); window.location.href = currentUrl.toString(); });
        window.performSearch = function() { const searchTerm = document.getElementById('globalSearch')?.value.trim(); if (searchTerm) { const searchUrl = "{% url 'case:search_cases' %}"; if(searchUrl) { window.location.href = `${searchUrl}?q=${encodeURIComponent(searchTerm)}`; } else { console.error("Search URL 'case:search_cases' not found."); } } }
        document.getElementById('globalSearch')?.addEventListener('keypress', function(e) { if (e.key === 'Enter') { e.preventDefault(); performSearch(); } });

        // --- Initial Chart Defaults and Options Update ---
        updateChartDefaults();
        updateExistingChartOptions();
        console.log("Dashboard JS Initialization Complete.");

    }); // End DOMContentLoaded
    </script>
{% endblock %}