from django.contrib import admin
from .models import (
    CustomUser, WorkingHour, UserDepartment, UserAvailability,
    UserActivityLog, UserSettings, UserQualification, EmergencyContact,
    SystemSettings
)

class CustomUserAdmin(admin.ModelAdmin):
    list_display = ('email', 'first_name', 'last_name', 'user_type', 'is_active', 'is_staff')
    search_fields = ('email', 'first_name', 'last_name')
    list_filter = ('user_type', 'is_active', 'is_staff')

admin.site.register(CustomUser, CustomUserAdmin)
admin.site.register(WorkingHour)
admin.site.register(UserDepartment)
admin.site.register(UserAvailability)
admin.site.register(UserActivityLog)
admin.site.register(UserSettings)
admin.site.register(UserQualification)
admin.site.register(EmergencyContact)
admin.site.register(SystemSettings)
