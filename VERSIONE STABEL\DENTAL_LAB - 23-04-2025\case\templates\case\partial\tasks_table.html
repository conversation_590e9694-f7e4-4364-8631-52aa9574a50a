<!-- case/partials/tasks_table.html -->
{% if tasks %}
<div class="tasks-wrapper">
    <div class="table-responsive">
        <table class="table" id="tasksTable">
            <thead>
                <tr>
                    <th scope="col" class="sortable" data-sort="id">
                        <i class="bi bi-hash"></i> ID
                    </th>
                    <th scope="col" class="sortable" data-sort="name">
                        <i class="bi bi-card-text"></i> Name
                    </th>
                    <th scope="col" class="sortable" data-sort="assigned">
                        <i class="bi bi-person"></i> Assigned To
                    </th>
                    <th scope="col" class="sortable" data-sort="due">
                        <i class="bi bi-calendar"></i> Due Date
                    </th>
                    <th scope="col" class="sortable" data-sort="status">
                        <i class="bi bi-check-circle"></i> Status
                    </th>
                    <th scope="col">
                        <i class="bi bi-gear"></i> Actions
                    </th>
                </tr>
            </thead>
            <tbody>
                {% for task in tasks %}
                <tr class="task-row" data-task-id="{{ task.id }}">
                    <td>
                        <a href="{% url 'case:task_detail' task.id %}" 
                           class="text-primary fw-semibold text-decoration-none">
                            #{{ task.id }}
                        </a>
                    </td>
                    <td>
                        <div class="d-flex align-items-center gap-2">
                            <span class="task-name">{{ task.name }}</span>
                            {% if task.priority == 'high' %}
                                <i class="bi bi-exclamation-triangle-fill text-warning" 
                                   data-bs-toggle="tooltip" 
                                   title="High Priority"></i>
                            {% endif %}
                        </div>
                        {% if task.description %}
                        <small class="text-muted d-block text-truncate" 
                               style="max-width: 200px;"
                               data-bs-toggle="tooltip" 
                               title="{{ task.description }}">
                            {{ task.description }}
                        </small>
                        {% endif %}
                    </td>
                    <td>
                        <div class="d-flex align-items-center gap-2">
                            <div class="avatar avatar-sm">
                                {% if task.assigned_to.profile_image %}
                                    <img src="{{ task.assigned_to.profile_image.url }}" 
                                         alt="{{ task.assigned_to.get_full_name }}"
                                         class="rounded-circle"
                                         width="32" height="32">
                                {% else %}
                                    <div class="avatar-initial rounded-circle bg-primary">
                                        {{ task.assigned_to.get_initials }}
                                    </div>
                                {% endif %}
                            </div>
                            <span>{{ task.assigned_to.get_full_name }}</span>
                        </div>
                    </td>
                    <td>
                        <span class="{% if task.is_overdue %}text-danger{% endif %}">
                            {{ task.due_date|date:"M d, Y" }}
                        </span>
                        {% if task.is_overdue %}
                            <i class="bi bi-clock-history text-danger ms-1" 
                               data-bs-toggle="tooltip" 
                               title="Overdue"></i>
                        {% endif %}
                    </td>
                    <td>
                        <span class="badge bg-{{ task.status|lower }} status-badge">
                            {{ task.get_status_display }}
                        </span>
                    </td>
                    <td>
                        <div class="d-flex gap-2">
                            <a href="{% url 'case:task_update' task.id %}" 
                               class="btn btn-sm btn-outline-primary"
                               data-bs-toggle="tooltip" 
                               title="Edit Task">
                                <i class="bi bi-pencil"></i>
                            </a>
                            <button type="button" 
                                    class="btn btn-sm btn-outline-success complete-task-btn"
                                    data-task-id="{{ task.id }}"
                                    data-bs-toggle="tooltip" 
                                    title="Mark Complete"
                                    {% if task.status == 'completed' %}disabled{% endif %}>
                                <i class="bi bi-check-lg"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% else %}
<div class="empty-state text-center py-5">
    <i class="bi bi-clipboard-x display-4 text-muted"></i>
    <h5 class="mt-3">No Tasks Yet</h5>
    <p class="text-muted">There are no tasks assigned to this case.</p>
    <a href="{% url 'case:task_create' case.case_number %}" 
       class="btn btn-primary mt-2">
        <i class="bi bi-plus-circle me-2"></i>Add First Task
    </a>
</div>
{% endif %}