from django.test import TestCase, RequestFactory
from django.urls import reverse
from accounts.custom_middleware import Custom403Middleware # Replace with your actual middleware
from django.http import HttpResponse
from django.core.exceptions import PermissionDenied

class CustomMiddlewareTest(TestCase):
    def test_custom_403_middleware(self):
        # Create a dummy view that raises PermissionDenied
        def dummy_view(request):
            raise PermissionDenied()

        # Create a request
        request = RequestFactory().get('/')

        # Apply the middleware
        middleware = Custom403Middleware(get_response=dummy_view)
        response = middleware(request)

        # Check the response
        self.assertEqual(response.status_code, 403)
        # Add more assertions to check the content of the 403 page if needed
