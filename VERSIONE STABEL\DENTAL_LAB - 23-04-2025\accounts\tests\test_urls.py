from django.test import SimpleTestCase
from django.urls import reverse, resolve
from accounts import views

class AccountsUrlsTest(SimpleTestCase):

    def test_register_url_resolves(self):
        url = reverse('accounts:register')
        self.assertEqual(resolve(url).func.view_class, views.RegisterView)

    def test_login_url_resolves(self):
        url = reverse('accounts:login')
        self.assertEqual(resolve(url).func.view_class, views.LoginView)

    def test_logout_url_resolves(self):
        url = reverse('accounts:logout')
        self.assertEqual(resolve(url).func, views.logout_view)

    def test_profile_url_resolves(self):
        url = reverse('accounts:profile')
        self.assertEqual(resolve(url).func.view_class, views.ProfileView)

    def test_profile_settings_url_resolves(self):
        url = reverse('accounts:profile_settings')
        self.assertEqual(resolve(url).func.view_class, views.UserSettingsView)

    def test_change_password_url_resolves(self):
        url = reverse('accounts:change_password')
        self.assertEqual(resolve(url).func.view_class, views.PasswordManagementView)

    def test_update_profile_image_url_resolves(self):
        url = reverse('accounts:update_profile_image')
        self.assertEqual(resolve(url).func.view_class, views.ProfileImageUpdateView)

    def test_qualifications_url_resolves(self):
        url = reverse('accounts:qualifications')
        self.assertEqual(resolve(url).func.view_class, views.QualificationListView)

    def test_add_qualification_url_resolves(self):
        url = reverse('accounts:add_qualification')
        self.assertEqual(resolve(url).func.view_class, views.QualificationCreateView)

    def test_delete_qualification_url_resolves(self):
        url = reverse('accounts:delete_qualification', args=[1]) # Example pk=1
        self.assertEqual(resolve(url).func.view_class, views.QualificationDeleteView)

    def test_activity_log_url_resolves(self):
        url = reverse('accounts:activity_log')
        self.assertEqual(resolve(url).func.view_class, views.ActivityLogView)

    def test_password_reset_url_resolves(self):
        url = reverse('accounts:password_reset')
        self.assertEqual(resolve(url).func.view_class, views.PasswordResetView)

    def test_verify_email_url_resolves(self):
        url = reverse('accounts:verify_email', kwargs={'uidb64': 'MQ', 'token': 'testtoken'})
        self.assertEqual(resolve(url).func.view_class, views.EmailVerificationView)

    def test_resend_verification_url_resolves(self):
        url = reverse('accounts:resend_verification')
        self.assertEqual(resolve(url).func.view_class, views.ResendVerificationView)

    def test_user_management_url_resolves(self):
        url = reverse('accounts:user_management')
        self.assertEqual(resolve(url).func.view_class, views.UserManagementView)

    def test_user_detail_url_resolves(self):
        url = reverse('accounts:user_detail', args=[1]) # Example pk=1
        self.assertEqual(resolve(url).func.view_class, views.UserDetailView)

    def test_user_edit_url_resolves(self):
        url = reverse('accounts:user_edit', args=[1]) # Example pk=1
        self.assertEqual(resolve(url).func.view_class, views.UserUpdateView)

    def test_user_delete_url_resolves(self):
        url = reverse('accounts:user_delete', args=[1]) # Example pk=1
        self.assertEqual(resolve(url).func.view_class, views.UserDeleteView)

    def test_403_url_resolves(self):
        url = reverse('accounts:403')
        self.assertEqual(resolve(url).func, views.custom_403)

    def test_404_url_resolves(self):
        url = reverse('accounts:404')
        self.assertEqual(resolve(url).func, views.custom_404)

    def test_500_url_resolves(self):
        url = reverse('accounts:500')
        self.assertEqual(resolve(url).func, views.custom_500)

    def test_settings_update_url_resolves(self):
        url = reverse('accounts:settings_update')
        self.assertEqual(resolve(url).func.view_class, views.UserSettingsUpdateView)

    def test_setup_2fa_url_resolves(self):
        url = reverse('accounts:setup_2fa')
        self.assertEqual(resolve(url).func.view_class, views.TwoFactorSetupView)

    def test_export_activity_log_url_resolves(self):
        url = reverse('accounts:export_activity_log')
        self.assertEqual(resolve(url).func.view_class, views.ExportActivityLogView)

    def test_system_settings_url_resolves(self):
        url = reverse('accounts:system_settings')
        self.assertEqual(resolve(url).func.view_class, views.SystemSettingsView)
