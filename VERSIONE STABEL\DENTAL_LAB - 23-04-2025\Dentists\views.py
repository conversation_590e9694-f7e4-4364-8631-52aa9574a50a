# views.py

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, permission_required
from django.http import HttpResponse, JsonResponse, FileResponse
from django.template.loader import render_to_string, get_template
from django.db.models import Q, Count, Prefetch,Max,F,Avg
from django.contrib import messages
from django.core.paginator import Pa<PERSON>ator, EmptyPage, PageNotAnInteger
from django.views.decorators.http import require_http_methods
from django.core.cache import cache
from django.urls import reverse
from django.utils import timezone
from django.db import transaction
from django.conf import settings

from accounts.models import CustomUser

from .models import Dentist
from .forms import DentistForm, DentistLoginForm, DentistProfileForm, DentistSettingsForm
from case.models import Case, Notification, Tryout
from patients.models import Patient

import csv
from io import StringIO
from datetime import datetime, timedelta
import xlsxwriter
from xhtml2pdf import pisa
import json

# List and Search Views
@login_required
@permission_required('Dentists.view_dentist', raise_exception=True)
def dentist_list(request):
    """
    Main view for listing dentists with search, filter and sorting capabilities
    """
    search_query = request.GET.get('search', '').strip()
    clinic_filter = request.GET.get('clinic', '')
    sort_by = request.GET.get('sort', 'name')
    page = request.GET.get('page', 1)

    # Base queryset with annotations - updated related_names
    queryset = Dentist.objects.annotate(
        patient_count=Count('patients', distinct=True),  # Changed from cases__patient
        case_count=Count('dentist_cases', distinct=True),  # Changed from cases
        active_cases=Count('dentist_cases',
                         filter=Q(dentist_cases__status='in_progress'),
                         distinct=True)  # Changed from cases
    ).select_related('user')

    # Rest of the view remains the same
    if search_query:
        queryset = queryset.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(clinic_name__icontains=search_query) |
            Q(phone_number__icontains=search_query)
        )

    if clinic_filter:
        queryset = queryset.filter(clinic_name=clinic_filter)

    queryset = apply_sorting(queryset, sort_by)

    cache_key = 'clinic_names'
    clinic_names = cache.get(cache_key)
    if clinic_names is None:
        clinic_names = list(Dentist.objects.values_list('clinic_name', flat=True)
                          .distinct().order_by('clinic_name'))
        cache.set(cache_key, clinic_names, 300)

    paginator = Paginator(queryset, 10)
    try:
        dentists = paginator.page(page)
    except PageNotAnInteger:
        dentists = paginator.page(1)
    except EmptyPage:
        dentists = paginator.page(paginator.num_pages)

    context = {
        'dentists': dentists,
        'search_query': search_query,
        'clinic_filter': clinic_filter,
        'sort_by': sort_by,
        'clinic_names': clinic_names,
        'total_dentists': queryset.count(),
        'active_dentists': queryset.filter(user__is_active=True).count()
    }

    if request.headers.get('HX-Request'):
        return HttpResponse(
            render_to_string('Dentists/includes/dentists_table.html',
                           context, request=request)
        )

    return render(request, 'Dentists/dentist_list.html', context)

@login_required
@login_required
def dentist_list_api(request):
    """API endpoint for dentist list"""
    search = request.GET.get('search', '')
    queryset = Dentist.objects.filter(
        Q(first_name__icontains=search) |
        Q(last_name__icontains=search) |
        Q(clinic_name__icontains=search)
    )[:10]

    data = [{
        'id': dentist.id,
        'text': f"{dentist.first_name} {dentist.last_name} - {dentist.clinic_name}",
        'clinic': dentist.clinic_name,
        'phone': dentist.phone_number
    } for dentist in queryset]

    return JsonResponse({'results': data})

@login_required
def dentist_search(request):
    """HTMX endpoint for live search results"""
    search_query = request.GET.get('search', '').strip()
    queryset = Dentist.objects.annotate(
        patient_count=Count('cases__patient', distinct=True)
    )

    if search_query:
        queryset = queryset.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(clinic_name__icontains=search_query) |
            Q(phone_number__icontains=search_query)
        )

    context = {
        'dentists': queryset[:10],
        'search_query': search_query
    }

    return HttpResponse(
        render_to_string('Dentists/includes/dentist_search_results.html',
                        context, request=request)
    )
@login_required
def dentist_filter(request):
    """HTMX endpoint for filtering dentists"""
    clinic = request.GET.get('clinic', '')
    status = request.GET.get('status', '')

    queryset = Dentist.objects.annotate(
        patient_count=Count('patients', distinct=True),  # Updated
        active_cases=Count('dentist_cases',
                         filter=Q(dentist_cases__status='in_progress'),
                         distinct=True)  # Updated
    )

    if clinic:
        queryset = queryset.filter(clinic_name=clinic)
    if status:
        queryset = queryset.filter(user__is_active=(status == 'active'))

    context = {
        'dentists': queryset,
        'clinic_filter': clinic,
        'status_filter': status
    }

    return render(request, 'Dentists/partials/dentist_list_table.html', context)
@login_required
def dentist_sort(request):
    """HTMX endpoint for sorting dentists"""
    sort_by = request.GET.get('sort', 'name')
    queryset = Dentist.objects.annotate(
        patient_count=Count('patients', distinct=True),  # Updated
        active_cases=Count('dentist_cases',
                         filter=Q(dentist_cases__status='in_progress'),
                         distinct=True)  # Updated
    )

    if sort_by == 'name_desc':
        queryset = queryset.order_by('-first_name', '-last_name')
    elif sort_by == 'clinic':
        queryset = queryset.order_by('clinic_name')
    elif sort_by == 'patients':
        queryset = queryset.order_by('-patient_count')
    elif sort_by == 'active_cases':
        queryset = queryset.order_by('-active_cases')
    else:  # default sort by name
        queryset = queryset.order_by('first_name', 'last_name')

    context = {
        'dentists': queryset,
        'sort_by': sort_by
    }

    return render(request, 'Dentists/partials/dentist_list_table.html', context)



@login_required
def dentist_calendar(request, pk):
    """Calendar view for dentist appointments and cases"""
    dentist = get_object_or_404(Dentist, pk=pk)
    start_date = request.GET.get('start')
    end_date = request.GET.get('end')

    try:
        start_date = datetime.strptime(start_date, '%Y-%m-%d') if start_date else timezone.now()
        end_date = datetime.strptime(end_date, '%Y-%m-%d') if end_date else start_date + timedelta(days=30)
    except ValueError:
        start_date = timezone.now()
        end_date = start_date + timedelta(days=30)

    # Get cases and tryouts in date range
    cases = dentist.dentist_cases.filter(
        received_date_time__range=[start_date, end_date]
    ).select_related('patient')

    tryouts = Tryout.objects.filter(
        case__dentist=dentist,
        date_time__range=[start_date, end_date]
    ).select_related('case', 'case__patient')

    events = []

    for case in cases:
        events.append({
            'id': f'case_{case.case_number}',
            'title': f'Case #{case.case_number} - {case.patient}',
            'start': case.received_date_time.isoformat(),
            'end': (case.received_date_time + timedelta(hours=1)).isoformat(),
            'className': f'bg-{case.get_status_color()}',
            'url': reverse('case:case_detail', args=[case.case_number])
        })

    for tryout in tryouts:
        events.append({
            'id': f'tryout_{tryout.id}',
            'title': f'Tryout - Case #{tryout.case.case_number}',
            'start': tryout.date_time.isoformat(),
            'end': (tryout.date_time + timedelta(hours=1)).isoformat(),
            'className': 'bg-info',
            'url': reverse('case:tryout_detail', args=[tryout.id])
        })

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse(events, safe=False)

    context = {
        'dentist': dentist,
        'events': json.dumps(events),
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d')
    }

    return render(request, 'Dentists/dentist_calendar.html', context)
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.utils import timezone
from django.db.models import Count
from django.db.models.functions import TruncMonth, ExtractMonth, ExtractYear
from datetime import datetime, timedelta
from django.db.models import Q
import json
from Dentists.models import Dentist
from case.models import Case

class DentistDashboardView(LoginRequiredMixin, UserPassesTestMixin, TemplateView):
    template_name = 'dentists/dashboard.html'

    def test_func(self):
        return hasattr(self.request.user, 'dentist_profile')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        try:
            dentist = self.request.user.dentist_profile
        except Dentist.DoesNotExist:
            context['error'] = "Dentist profile not found"
            return context

        today = timezone.now()
        thirty_days_ago = today - timedelta(days=30)

        # Get all cases for this dentist
        all_cases = Case.objects.filter(dentist=dentist)

        # Basic Statistics
        context['total_cases'] = all_cases.count()
        context['active_cases'] = all_cases.filter(
            Q(status='pending') | Q(status='in_progress')
        ).count()
        context['completed_cases'] = all_cases.filter(status='completed').count()

        # Cases this month
        first_day_of_month = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        context['cases_this_month'] = all_cases.filter(
            received_date_time__gte=first_day_of_month
        ).count()

        # Last month comparison
        last_month = first_day_of_month - timedelta(days=1)
        last_month_start = last_month.replace(day=1)
        last_month_cases = all_cases.filter(
            received_date_time__gte=last_month_start,
            received_date_time__lt=first_day_of_month
        ).count()

        # Calculate month-over-month change
        if last_month_cases > 0:
            context['month_change_percentage'] = round(
                ((context['cases_this_month'] - last_month_cases) / last_month_cases) * 100,
                1
            )
        else:
            context['month_change_percentage'] = 100 if context['cases_this_month'] > 0 else 0

        # Recent Cases
        context['recent_cases'] = all_cases.select_related(
            'patient', 'current_stage'
        ).prefetch_related(
            'stagehistory_set'
        ).order_by('-received_date_time')[:10]

        # Process each recent case to add additional info
        for case in context['recent_cases']:
            # Calculate progress using stage history
            total_stages = case.stagehistory_set.count()
            if total_stages > 0:
                completed_stages = case.stagehistory_set.filter(
                    status='completed'
                ).count()
                case.progress = round((completed_stages / total_stages) * 100)
            else:
                case.progress = 0

            # Add status class for badges
            case.status_class = self.get_status_class(case.status)

            # Check if case is overdue
            if case.deadline:
                case.is_overdue = case.deadline < today

        # Status Distribution for Chart
        status_distribution = all_cases.values('status').annotate(
            count=Count('case_number')
        ).order_by('status')

        context['status_labels'] = json.dumps(
            [item['status'] for item in status_distribution]
        )
        context['status_data'] = json.dumps(
            [item['count'] for item in status_distribution]
        )

        # Priority Distribution
        context['priority_distribution'] = all_cases.values(
            'priority'
        ).annotate(
            count=Count('case_number')
        ).order_by('priority')

        # Stage Distribution
        context['stage_distribution'] = all_cases.values(
            'current_stage__name'
        ).annotate(
            count=Count('case_number')
        ).order_by('current_stage__name')

        # Cases by Month (for trend chart)
        six_months_ago = today - timedelta(days=180)

        # For SQLite, we'll group by year and month manually
        monthly_cases = (
            all_cases.filter(received_date_time__gte=six_months_ago)
            .annotate(
                year=ExtractYear('received_date_time'),
                month=ExtractMonth('received_date_time')
            )
            .values('year', 'month')
            .annotate(count=Count('case_number'))
            .order_by('year', 'month')
        )

        # Convert the year-month data to proper date objects for display
        monthly_data = []
        monthly_labels = []
        for item in monthly_cases:
            date_obj = datetime(year=item['year'], month=item['month'], day=1)
            monthly_labels.append(date_obj.strftime('%B %Y'))
            monthly_data.append(item['count'])

        context['monthly_labels'] = json.dumps(monthly_labels)
        context['monthly_data'] = json.dumps(monthly_data)

        return context

    @staticmethod
    def get_status_class(status):
        """Return Bootstrap class based on status"""
        status_classes = {
            'pending': 'warning',
            'in_progress': 'primary',
            'completed': 'success',
            'cancelled': 'danger',
            'on_hold': 'secondary'
        }
        return status_classes.get(status, 'info')



@login_required
def dentist_schedule(request, pk):
    """Detailed schedule view for dentist"""
    dentist = get_object_or_404(Dentist, pk=pk)
    date = request.GET.get('date', timezone.now().date().isoformat())

    try:
        selected_date = datetime.strptime(date, '%Y-%m-%d').date()
    except ValueError:
        selected_date = timezone.now().date()

    # Get all scheduled items for the day
    cases = Case.objects.filter(
        dentist=dentist,
        received_date_time__date=selected_date
    ).select_related('patient')

    tryouts = Tryout.objects.filter(
        case__dentist=dentist,
        date_time__date=selected_date
    ).select_related('case', 'case__patient')

    # Combine and sort all scheduled items
    schedule_items = sorted(
        list(cases) + list(tryouts),
        key=lambda x: x.received_date_time if hasattr(x, 'received_date_time') else x.date_time
    )

    context = {
        'dentist': dentist,
        'schedule_items': schedule_items,
        'selected_date': selected_date
    }

    if request.headers.get('HX-Request'):
        return render(request, 'Dentists/partials/schedule_items.html', context)
    return render(request, 'Dentists/dentist_schedule.html', context)


@login_required
@permission_required('Dentists.view_dentist')
def dentist_performance_report(request):
    """Generate performance report for dentists"""
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    dentist_id = request.GET.get('dentist_id')

    try:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date() if start_date else timezone.now().date() - timedelta(days=30)
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date() if end_date else timezone.now().date()
    except ValueError:
        start_date = timezone.now().date() - timedelta(days=30)
        end_date = timezone.now().date()

    queryset = Dentist.objects.annotate(
        total_cases=Count('cases', filter=Q(cases__received_date_time__range=[start_date, end_date])),
        completed_cases=Count('cases', filter=Q(
            cases__status='completed',
            cases__received_date_time__range=[start_date, end_date]
        )),
        avg_completion_time=Avg(
            F('cases__finished_date_time') - F('cases__received_date_time'),
            filter=Q(
                cases__status='completed',
                cases__received_date_time__range=[start_date, end_date]
            )
        )
    )

    if dentist_id:
        queryset = queryset.filter(id=dentist_id)

    context = {
        'dentists': queryset,
        'start_date': start_date,
        'end_date': end_date,
        'generated_at': timezone.now()
    }

    if request.GET.get('format') == 'pdf':
        template = get_template('Dentists/reports/performance_report_pdf.html')
        html = template.render(context)
        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="performance_report_{timezone.now().strftime("%Y%m%d")}.pdf"'
        pisa.CreatePDF(html, dest=response)
        return response

    return render(request, 'Dentists/reports/performance_report.html', context)
from django.db.models.functions import TruncDate

@login_required
@permission_required('Dentists.view_dentist')
def dentist_activity_report(request):
    """Generate activity report for dentists"""
    period = request.GET.get('period', 'month')
    dentist_id = request.GET.get('dentist_id')

    end_date = timezone.now().date()
    if period == 'week':
        start_date = end_date - timedelta(days=7)
    elif period == 'month':
        start_date = end_date - timedelta(days=30)
    else:  # year
        start_date = end_date - timedelta(days=365)

    queryset = Case.objects.filter(received_date_time__range=[start_date, end_date])
    if dentist_id:
        queryset = queryset.filter(dentist_id=dentist_id)

    activity_data = queryset.annotate(
        date=TruncDate('received_date_time')
    ).values('date').annotate(
        total=Count('case_number'),
        completed=Count('case_number', filter=Q(status='completed')),
        active=Count('case_number', filter=Q(status='in_progress'))
    ).order_by('date')

    context = {
        'activity_data': activity_data,
        'period': period,
        'start_date': start_date,
        'end_date': end_date,
        'dentist_id': dentist_id,
        'dentists': Dentist.objects.all()
    }

    return render(request, 'Dentists/reports/activity_report.html', context)
@login_required
def dentist_notifications(request, pk):
    """View and manage dentist notifications"""
    dentist = get_object_or_404(Dentist, pk=pk)

    notifications = Notification.objects.filter(
        user=dentist.user
    ).order_by('-created_at')

    if request.method == 'POST':
        notification_id = request.POST.get('notification_id')
        action = request.POST.get('action')

        if action == 'mark_read' and notification_id:
            notifications.filter(id=notification_id).update(is_read=True)
        elif action == 'mark_all_read':
            notifications.update(is_read=True)

        if request.headers.get('HX-Request'):
            context = {'notifications': notifications}
            return render(request, 'Dentists/partials/notifications_list.html', context)

    context = {
        'dentist': dentist,
        'notifications': notifications
    }

    return render(request, 'Dentists/dentist_notifications.html', context)



# views/auth.py
from django.contrib.auth import login, logout, update_session_auth_hash
from django.contrib.auth.forms import PasswordChangeForm, PasswordResetForm

@login_required
def dentist_login(request):
    """Custom login view for dentists"""
    if request.method == 'POST':
        form = DentistLoginForm(request.POST)
        if form.is_valid():
            user = form.get_user()
            if user.is_dentist():
                login(request, user)
                next_url = request.GET.get('next', reverse('Dentists:dentist_dashboard', args=[user.dentist_profile.id]))
                return redirect(next_url)
            else:
                messages.error(request, 'This account is not registered as a dentist.')
    else:
        form = DentistLoginForm()

    return render(request, 'Dentists/auth/login.html', {'form': form})

@login_required
def dentist_logout(request):
    """Logout view for dentists"""
    logout(request)
    messages.success(request, 'You have been successfully logged out.')
    return redirect('Dentists:login')

@login_required
def dentist_password_change(request):
    """Change password view for dentists"""
    if request.method == 'POST':
        form = PasswordChangeForm(request.user, request.POST)
        if form.is_valid():
            user = form.save()
            update_session_auth_hash(request, user)
            messages.success(request, 'Your password was successfully updated!')
            return redirect('Dentists:dentist_dashboard', pk=request.user.dentist_profile.id)
    else:
        form = PasswordChangeForm(request.user)

    return render(request, 'Dentists/auth/password_change.html', {'form': form})

def dentist_password_reset(request):
    """Password reset view for dentists"""
    if request.method == 'POST':
        form = PasswordResetForm(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']
            try:
                user = CustomUser.objects.get(email=email, user_type=2)
                form.save(
                    request=request,
                    email_template_name='Dentists/auth/password_reset_email.html',
                    subject_template_name='Dentists/auth/password_reset_subject.txt',
                    from_email=settings.DEFAULT_FROM_EMAIL
                )
                messages.success(request, 'Password reset instructions have been sent to your email.')
                return redirect('Dentists:login')
            except CustomUser.DoesNotExist:
                messages.error(request, 'No dentist account found with this email address.')
    else:
        form = PasswordResetForm()

    return render(request, 'Dentists/auth/password_reset.html', {'form': form})




# Detail Views

def get_dentist_statistics(dentist):
    """Calculate statistics for a dentist"""
    today = timezone.now().date()
    return {
        'total_cases': dentist.dentist_cases.count(),
        'active_cases': dentist.dentist_cases.filter(status='in_progress').count(),
        'total_patients': dentist.patients.count(),
        'recent_activity': dentist.dentist_cases.filter(
            received_date_time__date=today
        ).count(),
    }
@login_required
@permission_required('Dentists.view_dentist')
def dentist_detail(request, pk):
    """Detailed view of a dentist with related information"""
    recent_cases_query = Case.objects.order_by('-received_date_time')[:5]

    dentist = get_object_or_404(
        Dentist.objects.select_related('user')
        .prefetch_related(
            Prefetch('dentist_cases',
                    queryset=recent_cases_query,
                    to_attr='recent_cases_list'),
            Prefetch('patients',
                    queryset=Patient.objects.only('first_name', 'last_name')),
        ),
        pk=pk
    )

    context = {
        'dentist': dentist,
        'recent_cases': dentist.recent_cases_list,
        'statistics': get_dentist_statistics(dentist),
    }

    return render(request, 'Dentists/dentist_detail.html', context)


def dentist_detail_api(request, pk):
    dentist = Dentist.objects.select_related('user').prefetch_related(
        Prefetch('cases', queryset=Case.objects.order_by('-created_at')[:5]),
        Prefetch('patients', queryset=Patient.objects.only('first_name', 'last_name')),
    ).get(pk=pk)



# Create, Update, Delete Views
from django.contrib.auth.decorators import login_required, permission_required
from django.db import transaction
from django.http import JsonResponse
from django.shortcuts import render, redirect
from django.urls import reverse

@login_required
@permission_required('Dentists.add_dentist')
def dentist_create(request):
    if request.method == 'POST':
        # Krijojmë një objekt CustomUser me të dhënat e formës
        user_data = {
            'email': request.POST.get('email'),
            'first_name': request.POST.get('first_name'),
            'last_name': request.POST.get('last_name'),
            'password1': request.POST.get('password1'),
            'password2': request.POST.get('password2'),
        }

        # Krijojmë një objekt Dentist me të dhënat e formës
        # Tani kemi vetëm një set të fushave first_name dhe last_name
        dentist_data = {
            'first_name': request.POST.get('first_name'),
            'last_name': request.POST.get('last_name'),
            'clinic_name': request.POST.get('clinic_name'),
            'phone_number': request.POST.get('phone_number'),
        }

        profile_form = DentistProfileForm(user_data)
        dentist_form = DentistForm(dentist_data)

        if profile_form.is_valid() and dentist_form.is_valid():
            try:
                with transaction.atomic():
                    # Create user first
                    user = profile_form.save(commit=False)
                    user.set_password(profile_form.cleaned_data['password1'])
                    user.is_active = True
                    # user_type vendoset automatikisht në formë
                    user.save()

                    # Nuk duhet të krijojmë profil dentisti këtu, pasi krijohet automatikisht nga sinjali
                    # Marrim profilin e dentistit të krijuar nga sinjali
                    # Presim pak që sinjali të përfundojë punën e tij
                    import time
                    time.sleep(0.5)  # Presim 0.5 sekonda

                    # Kontrollojmë nëse përdoruesi ka një profil dentisti
                    if not hasattr(user, 'dentist_profile'):
                        # Nëse nuk ka, krijojmë një profil të ri
                        print(f"Creating dentist profile manually for user {user.email}")
                        dentist = Dentist.objects.create(
                            user=user,
                            first_name=dentist_form.cleaned_data['first_name'],
                            last_name=dentist_form.cleaned_data['last_name'],
                            clinic_name=dentist_form.cleaned_data['clinic_name'],
                            phone_number=dentist_form.cleaned_data['phone_number']
                        )
                    else:
                        # Nëse ka, marrim profilin ekzistues
                        print(f"Using existing dentist profile for user {user.email}")
                        dentist = user.dentist_profile

                    # Përditësojmë të dhënat e dentistit vetëm nëse nuk i kemi vendosur më sipër
                    if hasattr(user, 'dentist_profile'):
                        # Përditësojmë vetëm clinic_name dhe phone_number nëse nuk janë vendosur
                        if not dentist.clinic_name:
                            dentist.clinic_name = dentist_form.cleaned_data['clinic_name']
                        if not dentist.phone_number:
                            dentist.phone_number = dentist_form.cleaned_data['phone_number']
                        # Ruajmë gjithmonë për të qenë të sigurt
                        dentist.save()

                    messages.success(request, 'Dentist created successfully!')

                    # Nëse kërkesa është AJAX, kthejmë një përgjigje JSON
                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return JsonResponse({'success': True})

                    return redirect('Dentists:dentist_list')

            except Exception as e:
                # Shfaq gabimin në konsolë për debugging
                import traceback
                print(f"ERROR: {str(e)}")
                print(traceback.format_exc())

                messages.error(request, f'Error creating dentist: {str(e)}')
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({'success': False, 'error': str(e)})
        else:
            # Print errors for debugging purposes
            print("Profile Form Errors:", profile_form.errors)
            print("Dentist Form Errors:", dentist_form.errors)
            messages.error(request, 'Please correct the errors below.')

            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                errors = {}
                # Shto gabimet nga profile_form
                if hasattr(profile_form, 'errors') and profile_form.errors:
                    for field, error_list in profile_form.errors.items():
                        errors[field] = [str(error) for error in error_list]
                # Shto gabimet nga dentist_form
                if hasattr(dentist_form, 'errors') and dentist_form.errors:
                    for field, error_list in dentist_form.errors.items():
                        errors[field] = [str(error) for error in error_list]
                # Nëse nuk ka gabime specifike, shto një mesazh të përgjithshëm
                if not errors:
                    errors['__all__'] = 'Please check your form data and try again.'
                return JsonResponse({'success': False, 'errors': errors})
    else:
        profile_form = DentistProfileForm()
        dentist_form = DentistForm()

    context = {
        'profile_form': profile_form,
        'dentist_form': dentist_form,
    }

    return render(request, 'dentists/dentist_create.html', context)

@login_required
@permission_required('Dentists.change_dentist')
def dentist_update(request, pk):
    """Update existing dentist information"""
    dentist = get_object_or_404(Dentist, pk=pk)
    if request.method == 'POST':
        form = DentistForm(request.POST, instance=dentist)
        profile_form = DentistProfileForm(request.POST, instance=dentist.user)

        if form.is_valid() and profile_form.is_valid():
            profile_form.save()
            form.save()
            messages.success(request, 'Dentist updated successfully.')

            if request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={'HX-Redirect': reverse('Dentists:dentist_list')}
                )
            return redirect('Dentists:dentist_list')
    else:
        form = DentistForm(instance=dentist)
        profile_form = DentistProfileForm(instance=dentist.user)

    context = {
        'form': form,
        'profile_form': profile_form,
        'dentist': dentist,
        'title': 'Update Dentist'
    }

    return render(request, 'Dentists/dentist_form.html', context)

@login_required
@permission_required('Dentists.delete_dentist')
def dentist_delete(request, pk):
    """Delete a dentist and associated user account"""
    dentist = get_object_or_404(Dentist, pk=pk)

    if request.method == 'POST':
        try:
            user = dentist.user
            dentist.delete()
            if user:
                user.delete()
            messages.success(request, 'Dentist deleted successfully.')
            return redirect('Dentists:dentist_list')
        except Exception as e:
            messages.error(request, f'Error deleting dentist: {str(e)}')
            return redirect('Dentists:dentist_list')

    return render(request, 'Dentists/confirm_delete.html', {'dentist': dentist})

# Export Views
@login_required
@permission_required('Dentists.view_dentist')
def export_dentists_csv(request):
    """Export dentists list as CSV"""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="dentists_export_{datetime.now().strftime("%Y%m%d")}.csv"'

    writer = csv.writer(response)
    writer.writerow([
        'ID', 'First Name', 'Last Name', 'Clinic Name',
        'Phone Number', 'Total Cases', 'Active Cases', 'Total Patients'
    ])

    dentists = Dentist.objects.annotate(
        total_cases=Count('cases'),
        active_cases=Count('cases', filter=Q(cases__status='in_progress')),
        total_patients=Count('patients', distinct=True)
    )

    for dentist in dentists:
        writer.writerow([
            dentist.id,
            dentist.first_name,
            dentist.last_name,
            dentist.clinic_name,
            dentist.phone_number,
            dentist.total_cases,
            dentist.active_cases,
            dentist.total_patients
        ])

    return response

@login_required
@permission_required('Dentists.view_dentist')
def export_dentists_excel(request):
    """Export dentists list as Excel"""
    output = StringIO()
    workbook = xlsxwriter.Workbook(output)
    worksheet = workbook.add_worksheet()

    # Add header row
    headers = [
        'ID', 'First Name', 'Last Name', 'Clinic Name',
        'Phone Number', 'Total Cases', 'Active Cases', 'Total Patients'
    ]
    for col, header in enumerate(headers):
        worksheet.write(0, col, header)

    # Add data rows
    dentists = Dentist.objects.annotate(
        total_cases=Count('cases'),
        active_cases=Count('cases', filter=Q(cases__status='in_progress')),
        total_patients=Count('patients', distinct=True)
    )

    for row, dentist in enumerate(dentists, start=1):
        data = [
            dentist.id,
            dentist.first_name,
            dentist.last_name,
            dentist.clinic_name,
            dentist.phone_number,
            dentist.total_cases,
            dentist.active_cases,
            dentist.total_patients
        ]
        for col, value in enumerate(data):
            worksheet.write(row, col, value)

    workbook.close()
    output.seek(0)

    response = HttpResponse(
        output.getvalue(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="dentists_export_{datetime.now().strftime("%Y%m%d")}.xlsx"'

    return response

@login_required
@permission_required('Dentists.view_dentist')
def export_dentists_pdf(request):
    """Export dentists list as PDF"""
    template = get_template('Dentists/pdf/dentists_list.html')

    dentists = Dentist.objects.annotate(
        total_cases=Count('cases'),
        active_cases=Count('cases', filter=Q(cases__status='in_progress')),
        total_patients=Count('patients', distinct=True)
    )

    context = {
        'dentists': dentists,
        'generated_at': timezone.now(),
        'company_name': settings.COMPANY_NAME,
    }

    html = template.render(context)
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="dentists_report_{datetime.now().strftime("%Y%m%d")}.pdf"'

    pisa_status = pisa.CreatePDF(html, dest=response)
    if pisa_status.err:
        return HttpResponse('Error generating PDF', status=500)

    return response

# Utility Functions
def apply_sorting(queryset, sort_by):
    """Apply sorting to queryset based on sort parameter"""
    sort_options = {
        'name': ['first_name', 'last_name'],
        'name_desc': ['-first_name', '-last_name'],
        'clinic': ['clinic_name', 'first_name'],
        'clinic_desc': ['-clinic_name', 'first_name'],
        'patients': ['-patient_count'],
        'patients_asc': ['patient_count'],
        'cases': ['-case_count'],
        'cases_asc': ['case_count'],
    }

    return queryset.order_by(*(sort_options.get(sort_by, ['first_name', 'last_name'])))

# API Views for AJAX requests
@login_required
def get_dentist_stats(request, pk):
    """API endpoint to get dentist statistics"""
    try:
        dentist = get_object_or_404(Dentist, pk=pk)
        stats = get_dentist_statistics(dentist)
        return JsonResponse(stats)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=400)

@login_required
def toggle_dentist_status(request, pk):
    """AJAX endpoint to toggle dentist active status"""
    if request.method == 'POST':
        try:
            dentist = get_object_or_404(Dentist, pk=pk)
            dentist.user.is_active = not dentist.user.is_active
            dentist.user.save()
            return JsonResponse({
                'status': 'success',
                'is_active': dentist.user.is_active
            })
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message ' : str(e)
            }, status=400)
    else:
        return JsonResponse({'status': 'error', 'message': 'Invalid request method'}, status=400)
# views.py (vazhdim)

@login_required
def toggle_dentist_status(request, pk):
    """AJAX endpoint to toggle dentist active status"""
    if request.method == 'POST':
        try:
            dentist = get_object_or_404(Dentist, pk=pk)
            dentist.user.is_active = not dentist.user.is_active
            dentist.user.save()
            return JsonResponse({
                'status': 'success',
                'is_active': dentist.user.is_active
            })
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            }, status=400)
    return JsonResponse({'error': 'Invalid request method'}, status=400)

# Bulk Operations Views
@login_required
@permission_required('Dentists.change_dentist')
def bulk_update_status(request):
    """Update status for multiple dentists at once"""
    if request.method == 'POST':
        try:
            dentist_ids = request.POST.getlist('dentist_ids[]')
            new_status = request.POST.get('status')

            if not dentist_ids or not new_status:
                raise ValueError('Missing required parameters')

            updated = Dentist.objects.filter(id__in=dentist_ids).update(
                user__is_active=(new_status == 'active')
            )

            messages.success(request, f'Successfully updated {updated} dentists.')
            return JsonResponse({'status': 'success', 'updated_count': updated})

        except Exception as e:
            messages.error(request, f'Error updating dentists: {str(e)}')
            return JsonResponse({'status': 'error', 'message': str(e)}, status=400)

    return JsonResponse({'error': 'Invalid request method'}, status=400)

@login_required
@permission_required('Dentists.delete_dentist')
def bulk_delete(request):
    """Delete multiple dentists at once"""
    if request.method == 'POST':
        try:
            dentist_ids = request.POST.getlist('dentist_ids[]')
            if not dentist_ids:
                raise ValueError('No dentists selected')

            deleted = Dentist.objects.filter(id__in=dentist_ids).delete()

            messages.success(request, f'Successfully deleted {deleted[0]} dentists.')
            return JsonResponse({'status': 'success', 'deleted_count': deleted[0]})

        except Exception as e:
            messages.error(request, f'Error deleting dentists: {str(e)}')
            return JsonResponse({'status': 'error', 'message': str(e)}, status=400)

    return JsonResponse({'error': 'Invalid request method'}, status=400)

# Dashboard Views
@login_required
def dentist_dashboard(request, pk):
    """Dashboard view for individual dentist"""
    dentist = get_object_or_404(Dentist, pk=pk)
    today = timezone.now().date()

    # Get recent cases
    recent_cases = Case.objects.filter(dentist=dentist)\
        .select_related('patient')\
        .order_by('-created_at')[:5]

    # Get statistics
    statistics = {
        'total_cases': dentist.dentist_cases.count(),
        'active_cases': dentist.dentist_cases.filter(status='in_progress').count(),
        'completed_cases': dentist.dentist_cases.filter(status='completed').count(),
        'total_patients': dentist.dentist_cases.values('patient').distinct().count(),
        'cases_this_month': dentist.dentist_cases.filter(
            created_at__month=today.month,
            created_at__year=today.year
        ).count(),
    }

    context = {
        'dentist': dentist,
        'recent_cases': recent_cases,
        'statistics': statistics,
    }

    return render(request, 'Dentists/dentist_dashboard.html', context)

# Case Management Views
@login_required
def dentist_cases(request, pk):
    """View all cases for a specific dentist"""
    dentist = get_object_or_404(Dentist, pk=pk)
    status_filter = request.GET.get('status', '')
    search_query = request.GET.get('search', '').strip()
    sort_by = request.GET.get('sort', '-received_date_time')

    queryset = dentist.dentist_cases.select_related('patient').prefetch_related('items')

    if status_filter:
        queryset = queryset.filter(status=status_filter)

    if search_query:
        queryset = queryset.filter(
            Q(case_number__icontains=search_query) |
            Q(patient__first_name__icontains=search_query) |
            Q(patient__last_name__icontains=search_query)
        )

    if sort_by == 'patient':
        queryset = queryset.order_by('patient__first_name', 'patient__last_name')
    else:
        queryset = queryset.order_by(sort_by)

    paginator = Paginator(queryset, 10)
    page = request.GET.get('page', 1)
    try:
        cases = paginator.page(page)
    except PageNotAnInteger:
        cases = paginator.page(1)
    except EmptyPage:
        cases = paginator.page(paginator.num_pages)

    context = {
        'dentist': dentist,
        'cases': cases,
        'status_filter': status_filter,
        'search_query': search_query,
        'sort_by': sort_by,
    }

    if request.headers.get('HX-Request'):
        return HttpResponse(
            render_to_string('Dentists/includes/cases_table.html', context, request=request)
        )

    return render(request, 'Dentists/dentist_cases.html', context)

# Patient Management Views
@login_required
def dentist_patients(request, pk):
    """View all patients for a specific dentist"""
    dentist = get_object_or_404(Dentist, pk=pk)
    search_query = request.GET.get('search', '').strip()
    sort_by = request.GET.get('sort', 'name')

    queryset = dentist.patients.annotate(
        total_cases=Count('patient_cases'),
        last_case_date=Max('patient_cases__received_date_time')
    )

    if search_query:
        queryset = queryset.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(phone_number__icontains=search_query)
        )

    sort_options = {
        'name': ['first_name', 'last_name'],
        'name_desc': ['-first_name', '-last_name'],
        'cases': ['-total_cases'],
        'recent': ['-last_case_date']
    }
    queryset = queryset.order_by(*sort_options.get(sort_by, ['first_name']))

    paginator = Paginator(queryset, 10)
    page = request.GET.get('page', 1)
    try:
        patients = paginator.page(page)
    except PageNotAnInteger:
        patients = paginator.page(1)
    except EmptyPage:
        patients = paginator.page(paginator.num_pages)

    context = {
        'dentist': dentist,
        'patients': patients,
        'search_query': search_query,
        'sort_by': sort_by,
    }

    if request.headers.get('HX-Request'):
        return HttpResponse(
            render_to_string('Dentists/includes/patients_table.html', context, request=request)
        )

    return render(request, 'Dentists/dentist_patients.html', context)

def get_dentist_statistics(dentist):
    """Calculate statistics for a dentist"""
    today = timezone.now().date()
    return {
        'total_cases': dentist.dentist_cases.count(),
        'active_cases': dentist.dentist_cases.filter(status='in_progress').count(),
        'total_patients': dentist.patients.count(),
        'recent_activity': dentist.dentist_cases.filter(
            received_date_time__date=today
        ).count(),
    }
# Analytics Views
@login_required
def dentist_analytics(request, pk):
    """Analytics view for a dentist"""
    dentist = get_object_or_404(Dentist, pk=pk)
    period = request.GET.get('period', 'month')

    end_date = timezone.now().date()
    if period == 'week':
        start_date = end_date - timedelta(days=7)
    elif period == 'month':
        start_date = end_date - timedelta(days=30)
    else:  # year
        start_date = end_date - timedelta(days=365)

    cases = dentist.dentist_cases.filter(
        received_date_time__date__range=[start_date, end_date]
    )

    statistics = {
        'total_cases': cases.count(),
        'completed_cases': cases.filter(status='completed').count(),
        'active_cases': cases.filter(status='in_progress').count(),
        'delayed_cases': cases.filter(
            status='in_progress',
            deadline__lt=timezone.now()
        ).count(),
    }

    context = {
        'dentist': dentist,
        'statistics': statistics,
        'period': period,
        'start_date': start_date,
        'end_date': end_date,
    }

    return render(request, 'Dentists/dentist_analytics.html', context)

# Settings and Profile Views
@login_required
def dentist_settings(request, pk):
    """Settings view for a specific dentist"""
    dentist = get_object_or_404(Dentist, pk=pk)
    if request.method == 'POST':
        form = DentistSettingsForm(request.POST, instance=dentist)
        profile_form = DentistProfileForm(request.POST, instance=dentist.user)

        if form.is_valid() and profile_form.is_valid():
            form.save()
            profile_form.save()
            messages.success(request, 'Settings updated successfully.')
            return redirect('Dentists:dentist_settings', pk=pk)
    else:
        form = DentistSettingsForm(instance=dentist)
        profile_form = DentistProfileForm(instance=dentist.user)

    context = {
        'dentist': dentist,
        'form': form,
        'profile_form': profile_form,
    }

    return render(request, 'Dentists/dentist_settings.html', context)