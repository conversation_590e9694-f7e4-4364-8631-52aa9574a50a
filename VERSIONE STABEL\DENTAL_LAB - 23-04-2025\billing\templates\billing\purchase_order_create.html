{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Create Purchase Order" %} | {% trans "Billing" %}{% endblock %}

{% block extra_css %}
<style>
  :root {
    /* Primary Colors - Fresher, Lighter Palette */
    --primary: #4285F4; /* Google Blue */
    --primary-light: rgba(66, 133, 244, 0.1);
    --secondary: #8ab4f8;
    --success: #34A853; /* Google Green */
    --success-light: rgba(52, 168, 83, 0.1);
    --danger: #EA4335; /* Google Red */
    --danger-light: rgba(234, 67, 53, 0.1);
    --warning: #FBBC05; /* Google Yellow */
    --warning-light: rgba(251, 188, 5, 0.1);
    --info: #46bdc6;
    --info-light: rgba(70, 189, 198, 0.1);
    --dark: #3c4043;
    --light: #f8f9fa;
    --white: #ffffff;

    /* Background and Text Colors */
    --bg-main: #f8f9fa;
    --text-main: #202124;
    --card-bg: #ffffff;
    --border-color: rgba(0,0,0,0.08);

    /* UI Elements */
    --shadow-sm: 0 1px 2px rgba(0,0,0,0.05);
    --shadow: 0 4px 6px rgba(0,0,0,0.05);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.05);
    --border-radius: 8px;
    --transition: all 0.3s ease;
  }

  /* Dark Mode Colors - Softer Dark Theme */
  [data-theme="dark"] {
    --primary: #8ab4f8; /* Lighter blue for dark mode */
    --primary-light: rgba(138, 180, 248, 0.15);
    --success: #81c995; /* Lighter green for dark mode */
    --success-light: rgba(129, 201, 149, 0.15);
    --danger: #f28b82; /* Lighter red for dark mode */
    --danger-light: rgba(242, 139, 130, 0.15);
    --warning: #fdd663; /* Lighter yellow for dark mode */
    --warning-light: rgba(253, 214, 99, 0.15);
    --info: #78d9ec;
    --info-light: rgba(120, 217, 236, 0.15);

    --dark: #e8eaed;
    --light: #3c4043;
    --white: #202124;

    --bg-main: #202124;
    --text-main: #e8eaed;
    --card-bg: #292a2d;
    --border-color: rgba(255,255,255,0.08);
  }

  body {
    background-color: var(--bg-main);
    color: var(--text-main);
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Card styles */
  .card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    margin-bottom: 1.5rem;
  }

  .card:hover {
    box-shadow: var(--shadow);
  }

  .card-header {
    background-color: var(--primary-light);
    color: var(--primary);
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
  }

  /* Form styles */
  .form-control, .form-select {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-main);
    transition: var(--transition);
  }

  .form-control:focus, .form-select:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.25rem var(--primary-light);
  }

  .form-label {
    color: var(--text-main);
    font-weight: 500;
  }

  /* Table styles */
  .table {
    color: var(--text-main);
  }

  .table thead th {
    background-color: var(--primary-light);
    color: var(--primary);
    font-weight: 600;
    border-bottom: none;
  }

  /* Formset styles */
  .formset-row {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    transition: var(--transition);
  }

  .formset-row:hover {
    box-shadow: var(--shadow-sm);
  }

  .formset-row.empty-form {
    display: none;
  }

  .add-form-row {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  .delete-form-row {
    color: var(--danger);
    cursor: pointer;
    transition: var(--transition);
  }

  .delete-form-row:hover {
    color: var(--danger);
    transform: scale(1.1);
  }

  /* Totals section */
  .po-totals {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
  }

  .total-row {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
  }

  .total-row:last-child {
    border-bottom: none;
    font-weight: 700;
    font-size: 1.1rem;
  }

  .total-label {
    color: var(--text-muted);
  }

  /* Form actions */
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
  }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
  <!-- Page Header -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h1 class="mb-0">{% trans "Create Purchase Order" %}</h1>
      <p class="text-muted">{% trans "Create a new purchase order for raw materials" %}</p>
    </div>
    <div>
      <a href="{% url 'billing:purchase_order_list' %}" class="btn btn-outline-primary">
        <i class="fas fa-arrow-left me-1"></i> {% trans "Back to Purchase Orders" %}
      </a>
    </div>
  </div>

  <!-- Purchase Order Form -->
  <form method="post" id="poForm">
    {% csrf_token %}

    <!-- Purchase Order Details Card -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="mb-0">{% trans "Purchase Order Details" %}</h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <label for="{{ form.supplier.id_for_label }}" class="form-label">{% trans "Supplier" %}</label>
              {{ form.supplier }}
              {% if form.supplier.errors %}
                <div class="invalid-feedback d-block">{{ form.supplier.errors }}</div>
              {% endif %}
            </div>
          </div>

          <div class="col-md-6">
            <div class="mb-3">
              <label for="{{ form.status.id_for_label }}" class="form-label">{% trans "Status" %}</label>
              {{ form.status }}
              {% if form.status.errors %}
                <div class="invalid-feedback d-block">{{ form.status.errors }}</div>
              {% endif %}
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <label for="{{ form.order_date.id_for_label }}" class="form-label">{% trans "Order Date" %}</label>
              {{ form.order_date }}
              {% if form.order_date.errors %}
                <div class="invalid-feedback d-block">{{ form.order_date.errors }}</div>
              {% endif %}
            </div>
          </div>

          <div class="col-md-6">
            <div class="mb-3">
              <label for="{{ form.expected_delivery_date.id_for_label }}" class="form-label">{% trans "Expected Delivery Date" %}</label>
              {{ form.expected_delivery_date }}
              {% if form.expected_delivery_date.errors %}
                <div class="invalid-feedback d-block">{{ form.expected_delivery_date.errors }}</div>
              {% endif %}
            </div>
          </div>
        </div>

        <div class="mb-3">
          <label for="{{ form.notes.id_for_label }}" class="form-label">{% trans "Notes" %}</label>
          {{ form.notes }}
          {% if form.notes.errors %}
            <div class="invalid-feedback d-block">{{ form.notes.errors }}</div>
          {% endif %}
        </div>

        <!-- Hidden fields -->
        {{ form.total_amount }}
      </div>
    </div>

    <!-- Purchase Order Items Card -->
    <div class="card mb-4">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">{% trans "Purchase Order Items" %}</h5>
        <button type="button" class="btn btn-sm btn-primary add-form-row">
          <i class="fas fa-plus me-1"></i> {% trans "Add Item" %}
        </button>
      </div>
      <div class="card-body">
        {{ formset.management_form }}

        <div class="table-responsive">
          <table class="table" id="items-table">
            <thead>
              <tr>
                <th>{% trans "Raw Material" %}</th>
                <th>{% trans "Description" %}</th>
                <th>{% trans "Quantity" %}</th>
                <th>{% trans "Unit" %}</th>
                <th>{% trans "Unit Price" %}</th>
                <th>{% trans "Currency" %}</th>
                <th>{% trans "Total" %}</th>
                <th>{% trans "Actions" %}</th>
              </tr>
            </thead>
            <tbody id="item-formset">
              {% for form in formset.forms %}
                <tr class="formset-row {% if forloop.last and not formset.is_bound %}empty-form{% endif %}"
                    id="form-row-{{ forloop.counter0 }}">
                  <td>
                    {{ form.raw_material }}
                    {% if form.raw_material.errors %}
                      <div class="invalid-feedback d-block">{{ form.raw_material.errors }}</div>
                    {% endif %}
                    {{ form.id }}
                  </td>
                  <td>
                    {{ form.description }}
                    {% if form.description.errors %}
                      <div class="invalid-feedback d-block">{{ form.description.errors }}</div>
                    {% endif %}
                  </td>
                  <td>
                    {{ form.quantity }}
                    {% if form.quantity.errors %}
                      <div class="invalid-feedback d-block">{{ form.quantity.errors }}</div>
                    {% endif %}
                  </td>
                  <td>
                    {{ form.unit }}
                    {% if form.unit.errors %}
                      <div class="invalid-feedback d-block">{{ form.unit.errors }}</div>
                    {% endif %}
                  </td>
                  <td>
                    {{ form.price_per_unit }}
                    {% if form.price_per_unit.errors %}
                      <div class="invalid-feedback d-block">{{ form.price_per_unit.errors }}</div>
                    {% endif %}
                  </td>
                  <td>
                    {{ form.currency }}
                    {% if form.currency.errors %}
                      <div class="invalid-feedback d-block">{{ form.currency.errors }}</div>
                    {% endif %}
                  </td>
                  <td>
                    <div class="item-total">0.00</div>
                  </td>
                  <td>
                    {% if not forloop.last or formset.is_bound %}
                      <button type="button" class="btn btn-sm btn-link text-danger delete-form-row">
                        <i class="fas fa-trash"></i>
                      </button>
                    {% endif %}
                    {{ form.DELETE }}
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

        <!-- No Items Message -->
        <div id="no-items-message" class="text-center py-4" style="display: none;">
          <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
          <h5>{% trans "No Items Added" %}</h5>
          <p class="text-muted">{% trans "Add items to this purchase order using the button above" %}</p>
        </div>
      </div>
    </div>

    <!-- Purchase Order Totals -->
    <div class="row">
      <div class="col-md-6 ms-auto">
        <div class="po-totals">
          <div class="total-row">
            <div class="total-label">{% trans "Subtotal" %}:</div>
            <div class="subtotal">0.00</div>
          </div>
          <div class="total-row">
            <div class="total-label">{% trans "Tax" %}:</div>
            <div>0.00</div>
          </div>
          <div class="total-row">
            <div class="total-label">{% trans "Total" %}:</div>
            <div class="grand-total">0.00</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="form-actions">
      <a href="{% url 'billing:purchase_order_list' %}" class="btn btn-outline-secondary">
        {% trans "Cancel" %}
      </a>
      <button type="submit" class="btn btn-primary">
        <i class="fas fa-save me-1"></i> {% trans "Create Purchase Order" %}
      </button>
    </div>
  </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const formsetPrefix = '{{ formset.prefix }}';
    const totalField = document.getElementById('id_total_amount');
    const itemFormset = document.getElementById('item-formset');
    const noItemsMessage = document.getElementById('no-items-message');
    const addButton = document.querySelector('.add-form-row');
    const subtotalElement = document.querySelector('.subtotal');
    const grandTotalElement = document.querySelector('.grand-total');

    // Initialize form elements
    initFormElements();
    updateFormsetState();
    calculateTotals();

    // Add new form row
    addButton.addEventListener('click', function() {
      const forms = document.querySelectorAll('.formset-row:not(.empty-form)');
      const totalForms = document.getElementById(`id_${formsetPrefix}-TOTAL_FORMS`);

      // Clone the empty form
      const emptyForm = document.querySelector('.empty-form');
      const newForm = emptyForm.cloneNode(true);
      newForm.classList.remove('empty-form');
      newForm.setAttribute('id', `form-row-${forms.length}`);

      // Update form index
      newForm.innerHTML = newForm.innerHTML.replace(
        new RegExp(`${formsetPrefix}-__prefix__-`, 'g'),
        `${formsetPrefix}-${forms.length}-`
      );

      // Insert before the empty form
      emptyForm.parentNode.insertBefore(newForm, emptyForm);

      // Update total forms count
      totalForms.value = forms.length + 1;

      // Initialize the new form's elements
      initFormRow(newForm);
      updateFormsetState();
    });

    // Delete form row
    itemFormset.addEventListener('click', function(e) {
      if (e.target.classList.contains('delete-form-row') || e.target.closest('.delete-form-row')) {
        const row = e.target.closest('.formset-row');
        const deleteCheckbox = row.querySelector(`input[id$="-DELETE"]`);

        if (deleteCheckbox) {
          // Mark for deletion
          deleteCheckbox.checked = true;
          row.style.display = 'none';
        } else {
          // Remove the row if it doesn't have a DELETE checkbox
          row.remove();
        }

        updateFormsetState();
        calculateTotals();
      }
    });

    // Initialize all form elements
    function initFormElements() {
      document.querySelectorAll('.formset-row:not(.empty-form)').forEach(row => {
        initFormRow(row);
      });
    }

    // Initialize a single form row
    function initFormRow(row) {
      const quantityInput = row.querySelector(`input[id$="-quantity"]`);
      const priceInput = row.querySelector(`input[id$="-price_per_unit"]`);
      const materialSelect = row.querySelector(`select[id$="-raw_material"]`);
      const currencySelect = row.querySelector(`select[id$="-currency"]`);
      const deleteCheckbox = row.querySelector(`input[id$="-DELETE"]`);

      // Hide delete checkbox
      if (deleteCheckbox) {
        deleteCheckbox.style.display = 'none';
      }

      // Add event listeners for calculation
      [quantityInput, priceInput].forEach(input => {
        if (input) {
          input.addEventListener('input', function() {
            calculateRowTotal(row);
            calculateTotals();
          });
        }
      });

      // Material selection changes
      if (materialSelect) {
        materialSelect.addEventListener('change', function() {
          // Here you could fetch material details via AJAX and update price
          // For now, we'll just recalculate
          calculateRowTotal(row);
          calculateTotals();
        });
      }

      // Currency selection changes
      if (currencySelect) {
        currencySelect.addEventListener('change', function() {
          calculateRowTotal(row);
          calculateTotals();
        });
      }

      // Initial calculation
      calculateRowTotal(row);
    }

    // Calculate total for a single row
    function calculateRowTotal(row) {
      const quantityInput = row.querySelector(`input[id$="-quantity"]`);
      const priceInput = row.querySelector(`input[id$="-price_per_unit"]`);
      const totalElement = row.querySelector('.item-total');
      const currencySelect = row.querySelector(`select[id$="-currency"]`);

      if (quantityInput && priceInput && totalElement) {
        const quantity = parseFloat(quantityInput.value) || 0;
        const price = parseFloat(priceInput.value) || 0;
        const total = quantity * price;

        // Format with 2 decimal places
        totalElement.textContent = total.toFixed(2);

        // Add currency code if available
        if (currencySelect && currencySelect.selectedIndex >= 0) {
          const currencyCode = currencySelect.options[currencySelect.selectedIndex].text;
          totalElement.textContent += ` ${currencyCode}`;
        }
      }
    }

    // Calculate all totals
    function calculateTotals() {
      let subtotal = 0;

      // Sum all visible rows
      document.querySelectorAll('.formset-row:not(.empty-form)').forEach(row => {
        if (row.style.display !== 'none') {
          const quantityInput = row.querySelector(`input[id$="-quantity"]`);
          const priceInput = row.querySelector(`input[id$="-price_per_unit"]`);

          if (quantityInput && priceInput) {
            const quantity = parseFloat(quantityInput.value) || 0;
            const price = parseFloat(priceInput.value) || 0;
            subtotal += quantity * price;
          }
        }
      });

      // Update displayed totals
      subtotalElement.textContent = subtotal.toFixed(2);
      grandTotalElement.textContent = subtotal.toFixed(2);

      // Update hidden total field
      if (totalField) {
        totalField.value = subtotal.toFixed(2);
      }
    }

    // Update formset state (show/hide no items message)
    function updateFormsetState() {
      const visibleRows = Array.from(
        document.querySelectorAll('.formset-row:not(.empty-form)')
      ).filter(row => row.style.display !== 'none');

      if (visibleRows.length === 0) {
        noItemsMessage.style.display = 'block';
      } else {
        noItemsMessage.style.display = 'none';
      }
    }

    // Form submission
    document.getElementById('poForm').addEventListener('submit', function(e) {
      const visibleRows = Array.from(
        document.querySelectorAll('.formset-row:not(.empty-form)')
      ).filter(row => row.style.display !== 'none');

      if (visibleRows.length === 0) {
        e.preventDefault();
        alert('{% trans "Please add at least one item to the purchase order" %}');
      }
    });
  });
</script>
{% endblock %}
