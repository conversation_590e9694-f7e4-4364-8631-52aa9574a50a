<!-- case/partials/tryouts_table.html -->
{% if tryouts %}
<div class="tryouts-wrapper">
    <div class="table-responsive">
        <table class="table" id="tryoutsTable">
            <thead>
                <tr>
                    <th scope="col" class="sortable" data-sort="id">
                        <i class="bi bi-hash"></i> ID
                    </th>
                    <th scope="col" class="sortable" data-sort="date">
                        <i class="bi bi-calendar"></i> Date
                    </th>
                    <th scope="col" class="sortable" data-sort="location">
                        <i class="bi bi-geo-alt"></i> Location
                    </th>
                    <th scope="col" class="sortable" data-sort="status">
                        <i class="bi bi-check-circle"></i> Status
                    </th>
                    <th scope="col">
                        <i class="bi bi-card-text"></i> Notes
                    </th>
                    <th scope="col">
                        <i class="bi bi-gear"></i> Actions
                    </th>
                </tr>
            </thead>
            <tbody>
                {% for tryout in tryouts %}
                <tr class="tryout-row" data-tryout-id="{{ tryout.id }}">
                    <td>
                        <a href="{% url 'case:tryout_detail' tryout.id %}" 
                           class="text-primary fw-semibold text-decoration-none">
                            #{{ tryout.id }}
                        </a>
                    </td>
                    <td>
                        <div class="d-flex flex-column">
                            <span>{{ tryout.date_time|date:"M d, Y" }}</span>
                            <small class="text-muted">{{ tryout.date_time|date:"H:i" }}</small>
                        </div>
                    </td>
                    <td>
                        <div class="d-flex align-items-center gap-2">
                            <i class="bi bi-building text-muted"></i>
                            <span>{{ tryout.location.name }}</span>
                        </div>
                    </td>
                    <td>
                        <span class="badge bg-{{ tryout.status|lower }} status-badge">
                            {{ tryout.get_status_display }}
                        </span>
                        {% if tryout.status == 'failed' %}
                            <i class="bi bi-exclamation-circle-fill text-danger ms-1" 
                               data-bs-toggle="tooltip" 
                               title="Failed Tryout"></i>
                        {% endif %}
                    </td>
                    <td>
                        {% if tryout.notes %}
                            <div class="notes-preview"
                                 data-bs-toggle="popover"
                                 data-bs-trigger="hover"
                                 data-bs-content="{{ tryout.notes }}">
                                {{ tryout.notes|truncatechars:50 }}
                            </div>
                        {% else %}
                            <span class="text-muted">No notes</span>
                        {% endif %}
                    </td>
                    <td>
                        <div class="d-flex gap-2">
                            <a href="{% url 'case:tryout_update' tryout.id %}" 
                               class="btn btn-sm btn-outline-primary"
                               data-bs-toggle="tooltip" 
                               title="Edit Tryout">
                                <i class="bi bi-pencil"></i>
                            </a>
                            {% if tryout.attachments.exists %}
                                <button type="button" 
                                        class="btn btn-sm btn-outline-info view-attachments-btn"
                                        data-tryout-id="{{ tryout.id }}"
                                        data-bs-toggle="modal"
                                        data-bs-target="#attachmentsModal">
                                    <i class="bi bi-paperclip"></i>
                                </button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Attachments Modal -->
    <div class="modal fade" id="attachmentsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Tryout Attachments</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="attachmentsList" class="list-group">
                        <!-- Dynamically populated -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="empty-state text-center py-5">
    <i class="bi bi-clipboard-x display-4 text-muted"></i>
    <h5 class="mt-3">No Tryouts Yet</h5>
    <p class="text-muted">There are no tryouts recorded for this case.</p>
    <a href="{% url 'case:tryout_create' case.case_number %}" 
       class="btn btn-primary mt-2">
        <i class="bi bi-plus-circle me-2"></i>Schedule First Tryout
    </a>
</div>
{% endif %}