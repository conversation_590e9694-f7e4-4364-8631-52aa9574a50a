{% extends 'base.html' %}
{% load static %}

{% block title %}Case Gantt Chart{% endblock %}

{% block extra_css %}
<!-- dhtmlxGantt CSS -->
<link rel="stylesheet" href="https://cdn.dhtmlx.com/gantt/edge/dhtmlxgantt.css">
<style>
    .gantt-container {
        height: 700px;
        width: 100%;
    }

    /* Status-based coloring */
    .gantt_task_line.status-pending_acceptance { background-color: #ffc107; border-color: #e0a800; }
    .gantt_task_line.status-in_progress { background-color: #007bff; border-color: #0069d9; }
    .gantt_task_line.status-completed { background-color: #28a745; border-color: #218838; }
    .gantt_task_line.status-cancelled { background-color: #dc3545; border-color: #c82333; }
    .gantt_task_line.status-on_hold { background-color: #6c757d; border-color: #5a6268; }
    .gantt_task_line.status-quality_check { background-color: #17a2b8; border-color: #138496; }
    .gantt_task_line.status-revision_needed { background-color: #fd7e14; border-color: #e96b02; }
    .gantt_task_line.status-ready_to_ship { background-color: #20c997; border-color: #1ba87e; }
    .gantt_task_line.status-shipped { background-color: #6f42c1; border-color: #5e37a6; }
    .gantt_task_line.status-delivered { background-color: #6610f2; border-color: #560bd0; }

    /* Overdue tasks */
    .gantt_task_line.overdue {
        border: 2px dashed #dc3545 !important;
    }

    /* Stage tasks */
    .gantt_task_line.stage-task {
        background-color: rgba(41, 127, 185, 0.6);
        border-color: #297fb9;
        height: 14px;
        margin-top: 3px;
    }

    /* Filter panel */
    .filter-panel {
        background: #f8f9fa;
        padding: 15px;
        margin-bottom: 15px;
        border-radius: 5px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    /* Gantt tooltip */
    .gantt_tooltip {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        border-radius: 4px;
        font-size: 14px;
        color: #333;
        padding: 10px;
        background: #fff;
        max-width: 300px;
    }

    /* Analytics panel */
    .analytics-panel {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 15px;
    }

    .analytics-card {
        flex: 1;
        min-width: 150px;
        padding: 15px;
        border-radius: 5px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        text-align: center;
    }

    .analytics-card h5 {
        margin: 0;
        font-size: 14px;
        color: #6c757d;
    }

    .analytics-card .value {
        font-size: 24px;
        font-weight: bold;
        margin: 10px 0 0;
    }

    .total-cases { background-color: #f8f9fa; }
    .completed-cases { background-color: #d4edda; }
    .in-progress-cases { background-color: #cce5ff; }
    .delayed-cases { background-color: #f8d7da; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>Case Production Timeline</h1>
        <div class="btn-group">
            <button class="btn btn-outline-primary" id="view-timeline">Timeline View</button>
            <button class="btn btn-outline-primary" id="view-resources">Resource View</button>
            <button class="btn btn-outline-primary" id="view-critical-path">Critical Path</button>
        </div>
    </div>

    <!-- Quick Stats Row -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <div class="rounded-circle bg-primary bg-opacity-10 p-3 me-3">
                        <i class="fas fa-tasks fa-2x text-primary"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">Active Cases</h6>
                        <h3 class="mb-0" id="active-cases-count">{{ analytics.in_progress }}</h3>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <div class="rounded-circle bg-success bg-opacity-10 p-3 me-3">
                        <i class="fas fa-check-circle fa-2x text-success"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">Completed</h6>
                        <h3 class="mb-0" id="completed-cases-count">{{ analytics.completed_cases }}</h3>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <div class="rounded-circle bg-danger bg-opacity-10 p-3 me-3">
                        <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">Delayed</h6>
                        <h3 class="mb-0" id="delayed-cases-count">{{ analytics.delayed_cases }}</h3>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <div class="rounded-circle bg-info bg-opacity-10 p-3 me-3">
                        <i class="fas fa-chart-line fa-2x text-info"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">Efficiency</h6>
                        <h3 class="mb-0" id="efficiency-rate">{{ efficiency_rate|default:'--' }}%</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics Panel -->
    <div class="analytics-panel">
        <div class="analytics-card total-cases">
            <h5>Total Cases</h5>
            <p class="value">{{ analytics.total_cases }}</p>
        </div>
        <div class="analytics-card completed-cases">
            <h5>Completed</h5>
            <p class="value">{{ analytics.completed_cases }}</p>
        </div>
        <div class="analytics-card in-progress-cases">
            <h5>In Progress</h5>
            <p class="value">{{ analytics.in_progress }}</p>
        </div>
        <div class="analytics-card delayed-cases">
            <h5>Delayed</h5>
            <p class="value">{{ analytics.delayed_cases }}</p>
        </div>
    </div>

    <!-- Advanced Filter Panel -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-white">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
                <div>
                    <button class="btn btn-sm btn-outline-secondary" id="save-view" title="Save current view"><i class="fas fa-save me-1"></i>Save View</button>
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">Saved Views</button>
                    <ul class="dropdown-menu" id="saved-views-list">
                        <li><a class="dropdown-item" href="#">All Active Cases</a></li>
                        <li><a class="dropdown-item" href="#">Delayed Cases</a></li>
                        <li><a class="dropdown-item" href="#">High Priority</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="card-body">
            <form method="get" id="filter-form" class="row g-3">
                <div class="col-md-3">
                    <label for="date_range" class="form-label">Date Range</label>
                    <select class="form-select" id="date_range" name="date_range">
                        <option value="7" {% if date_range == 7 %}selected{% endif %}>Next 7 days</option>
                        <option value="14" {% if date_range == 14 %}selected{% endif %}>Next 14 days</option>
                        <option value="30" {% if date_range == 30 or not date_range %}selected{% endif %}>Next 30 days</option>
                        <option value="90" {% if date_range == 90 %}selected{% endif %}>Next 90 days</option>
                        <option value="custom">Custom range</option>
                    </select>
                </div>
                <div class="col-md-3" id="custom-date-container" style="display: none;">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date|date:'Y-m-d' }}">
                </div>
                <div class="col-md-3">
                    <label for="department" class="form-label">Department</label>
                    <select class="form-select" id="department" name="department">
                        <option value="">All Departments</option>
                        {% for dept in departments %}
                        <option value="{{ dept.id }}" {% if selected_department == dept.id|stringformat:"s" %}selected{% endif %}>{{ dept.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        {% for status_code, status_name in case_statuses %}
                        <option value="{{ status_code }}" {% if selected_status == status_code %}selected{% endif %}>{{ status_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="priority" class="form-label">Priority</label>
                    <select class="form-select" id="priority" name="priority">
                        <option value="">All Priorities</option>
                        {% for priority_code, priority_name in case_priorities %}
                        <option value="{{ priority_code }}" {% if selected_priority == priority_code|stringformat:"s" %}selected{% endif %}>{{ priority_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" placeholder="Case #, Patient, Dentist..." value="{{ search_term }}">
                </div>
                <div class="col-md-3">
                    <label for="page_size" class="form-label">Items Per Page</label>
                    <select class="form-select" id="page_size" name="page_size">
                        <option value="10" {% if page_size == 10 %}selected{% endif %}>10</option>
                        <option value="25" {% if page_size == 25 %}selected{% endif %}>25</option>
                        <option value="50" {% if page_size == 50 %}selected{% endif %}>50</option>
                        <option value="100" {% if page_size == 100 %}selected{% endif %}>100</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2"><i class="fas fa-search me-1"></i>Apply Filters</button>
                    <a href="{% url 'case:dhtmlx_gantt_view' %}" class="btn btn-outline-secondary"><i class="fas fa-redo me-1"></i>Reset</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Gantt Chart Controls -->
    <div class="card shadow-sm mb-4">
        <div class="card-body p-3">
            <div class="row">
                <div class="col-md-6">
                    <div class="btn-group" role="group">
                        <button id="zoom-in" class="btn btn-outline-secondary">
                            <i class="fas fa-search-plus"></i> Zoom In
                        </button>
                        <button id="zoom-out" class="btn btn-outline-secondary">
                            <i class="fas fa-search-minus"></i> Zoom Out
                        </button>
                        <button id="today" class="btn btn-outline-secondary">
                            <i class="fas fa-calendar-day"></i> Today
                        </button>
                        <div class="btn-group" role="group">
                            <button id="scale-dropdown" type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-clock"></i> Scale
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item scale-option" data-scale="hour" href="#">Hourly</a></li>
                                <li><a class="dropdown-item scale-option" data-scale="day" href="#">Daily</a></li>
                                <li><a class="dropdown-item scale-option" data-scale="week" href="#">Weekly</a></li>
                                <li><a class="dropdown-item scale-option" data-scale="month" href="#">Monthly</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <div class="btn-group" role="group">
                        <button id="toggle-critical-path" class="btn btn-outline-secondary">
                            <i class="fas fa-route"></i> Critical Path
                        </button>
                        <button id="toggle-resources" class="btn btn-outline-secondary">
                            <i class="fas fa-users"></i> Resources
                        </button>
                        <div class="btn-group" role="group">
                            <button id="export-dropdown" type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-download"></i> Export
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" id="export-pdf" href="#"><i class="fas fa-file-pdf me-2"></i>PDF</a></li>
                                <li><a class="dropdown-item" id="export-png" href="#"><i class="fas fa-file-image me-2"></i>PNG</a></li>
                                <li><a class="dropdown-item" id="export-excel" href="#"><i class="fas fa-file-excel me-2"></i>Excel</a></li>
                                <li><a class="dropdown-item" id="export-json" href="#"><i class="fas fa-file-code me-2"></i>JSON</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gantt Chart Container -->
    <div class="card shadow-sm mb-4">
        <div class="card-body p-0">
            <div id="gantt_chart" class="gantt-container"></div>
        </div>
    </div>

    <!-- Resource Utilization Panel (initially hidden) -->
    <div id="resource-panel" class="card shadow-sm mb-4" style="display: none;">
        <div class="card-header bg-white">
            <h5 class="mb-0"><i class="fas fa-users me-2"></i>Resource Utilization</h5>
        </div>
        <div class="card-body">
            <div id="resource_chart" style="height: 300px;"></div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- dhtmlxGantt JS with all extensions included -->
<script src="https://cdn.dhtmlx.com/gantt/edge/dhtmlxgantt_all.js"></script>

<!-- Load individual extensions as a backup -->
<script src="https://cdn.dhtmlx.com/gantt/edge/ext/dhtmlxgantt_critical_path.js"></script>
<script src="https://cdn.dhtmlx.com/gantt/edge/ext/dhtmlxgantt_marker.js"></script>
<script src="https://cdn.dhtmlx.com/gantt/edge/ext/dhtmlxgantt_smart_rendering.js"></script>
<script src="https://cdn.dhtmlx.com/gantt/edge/ext/dhtmlxgantt_tooltip.js"></script>
<script src="https://cdn.dhtmlx.com/gantt/edge/ext/dhtmlxgantt_undo.js"></script>
<script src="https://cdn.dhtmlx.com/gantt/edge/ext/dhtmlxgantt_auto_scheduling.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Check if gantt is defined
        if (typeof gantt === 'undefined') {
            console.error('dhtmlxGantt library not loaded properly. Attempting to reload...');
            // Try to load the basic library again
            var script = document.createElement('script');
            script.src = 'https://cdn.dhtmlx.com/gantt/edge/dhtmlxgantt.js';
            script.onload = function() {
                console.log('dhtmlxGantt library loaded successfully.');
                initGantt();
            };
            script.onerror = function() {
                console.error('Failed to load dhtmlxGantt library. Please refresh the page.');
                alert('Failed to load Gantt chart library. Please refresh the page.');
            };
            document.head.appendChild(script);
            return;
        }

        // Initialize the Gantt chart
        initGantt();

        // Main initialization function
        function initGantt() {
        // ===== GANTT CONFIGURATION =====
        // Basic configuration
        gantt.config.date_format = "%Y-%m-%d %H:%i";
        gantt.config.duration_unit = "day";
        gantt.config.row_height = 35;
        gantt.config.min_column_width = 80;
        gantt.config.fit_tasks = true;
        gantt.config.show_progress = true;
        gantt.config.auto_scheduling = true;
        gantt.config.auto_scheduling_strict = false;
        gantt.config.work_time = true;
        gantt.config.correct_work_time = true;
        gantt.config.drag_progress = true;
        gantt.config.drag_resize = true;
        gantt.config.drag_move = true;
        gantt.config.drag_links = true;
        gantt.config.smart_rendering = true;
        gantt.config.show_unscheduled = true;

        // Enable undo/redo
        gantt.config.undo = true;
        gantt.config.redo = true;

        // Use the new scale configuration format
        gantt.config.scales = [
            {unit: "day", step: 1, format: "%d %M"},
            {unit: "hour", step: 6, format: "%H:%i"}
        ];

        // Configure zoom levels
        const zoomConfig = {
            levels: [
                {
                    name: "hour",
                    scale_height: 60,
                    min_column_width: 30,
                    scales: [
                        {unit: "day", step: 1, format: "%d %M"},
                        {unit: "hour", step: 1, format: "%H:%i"}
                    ]
                },
                {
                    name: "day",
                    scale_height: 60,
                    min_column_width: 70,
                    scales: [
                        {unit: "day", step: 1, format: "%d %M"},
                        {unit: "hour", step: 6, format: "%H"}
                    ]
                },
                {
                    name: "week",
                    scale_height: 60,
                    min_column_width: 70,
                    scales: [
                        {unit: "week", step: 1, format: function(date) {
                            const dateToStr = gantt.date.date_to_str("%d %M");
                            const endDate = gantt.date.add(date, 6, "day");
                            const weekNum = gantt.date.date_to_str("%W")(date);
                            return "#" + weekNum + ": " + dateToStr(date) + " - " + dateToStr(endDate);
                        }},
                        {unit: "day", step: 1, format: "%j %D"}
                    ]
                },
                {
                    name: "month",
                    scale_height: 60,
                    min_column_width: 120,
                    scales: [
                        {unit: "month", step: 1, format: "%F %Y"},
                        {unit: "week", step: 1, format: function(date) {
                            const weekNum = gantt.date.date_to_str("%W")(date);
                            return "Week #" + weekNum;
                        }}
                    ]
                }
            ],
            currentLevel: 1  // Default zoom level (day)
        };

        // Apply the zoom configuration
        gantt.ext.zoom.init(zoomConfig);

        // ===== TEMPLATES =====
        // Task styling
        gantt.templates.task_class = function(start, end, task) {
            let classes = [];

            // Add status class
            if (task.status) {
                classes.push("status-" + task.status);
            }

            // Add overdue class
            if (task.is_overdue) {
                classes.push("overdue");
            }

            // Add stage class
            if (task.type === "stage") {
                classes.push("stage-task");
            }

            // Add critical path class
            if (task.critical) {
                classes.push("critical-task");
            }

            return classes.join(" ");
        };

        // Enhanced tooltip
        gantt.templates.tooltip_text = function(start, end, task) {
            let html = "<div class='gantt-tooltip'>";

            if (task.type === "case") {
                html += `<div class="tooltip-header status-${task.status}">Case #${task.case_number}</div>`;
                html += `<div class="tooltip-body">`;
                html += `<table class="tooltip-table">`;
                html += `<tr><td><strong>Status:</strong></td><td>${task.status_display || task.status}</td></tr>`;
                html += `<tr><td><strong>Priority:</strong></td><td>${task.priority_display || task.priority}</td></tr>`;
                html += `<tr><td><strong>Patient:</strong></td><td>${task.patient}</td></tr>`;
                html += `<tr><td><strong>Dentist:</strong></td><td>${task.dentist}</td></tr>`;
                html += `<tr><td><strong>Department:</strong></td><td>${task.department}</td></tr>`;
                html += `<tr><td><strong>Deadline:</strong></td><td>${task.deadline}</td></tr>`;
                if (task.is_overdue) {
                    html += `<tr><td colspan="2"><span class="badge bg-danger">OVERDUE</span></td></tr>`;
                }
                if (task.critical) {
                    html += `<tr><td colspan="2"><span class="badge bg-warning">CRITICAL PATH</span></td></tr>`;
                }
                html += `</table>`;
                html += `</div>`;
            } else if (task.type === "stage") {
                html += `<div class="tooltip-header">${task.text}</div>`;
                html += `<div class="tooltip-body">`;
                html += `<table class="tooltip-table">`;
                html += `<tr><td><strong>Case:</strong></td><td>#${task.case_number}</td></tr>`;
                html += `<tr><td><strong>Department:</strong></td><td>${task.department}</td></tr>`;
                html += `<tr><td><strong>Start:</strong></td><td>${gantt.templates.tooltip_date_format(start)}</td></tr>`;
                html += `<tr><td><strong>End:</strong></td><td>${gantt.templates.tooltip_date_format(end)}</td></tr>`;
                html += `<tr><td><strong>Duration:</strong></td><td>${Math.round((end - start) / (1000 * 60 * 60 * 24))} days</td></tr>`;
                if (task.critical) {
                    html += `<tr><td colspan="2"><span class="badge bg-warning">CRITICAL PATH</span></td></tr>`;
                }
                html += `</table>`;
                html += `</div>`;
            }

            html += "</div>";
            return html;
        };

        // ===== INITIALIZE GANTT =====
        gantt.init("gantt_chart");

        // Add today marker if the marker extension is available
        try {
            if (gantt.addMarker) {
                const todayMarker = gantt.addMarker({
                    start_date: new Date(),
                    css: "today-marker",
                    text: "Today",
                    title: "Today: " + gantt.date.date_to_str("%Y-%m-%d")(new Date())
                });
            } else {
                // Fallback if marker extension is not available
                console.log("Marker extension not available. Using CSS-based today marker.");
                // We'll add a CSS-based marker in the custom CSS section
            }
        } catch (e) {
            console.log("Error adding marker: " + e.message);
        }

        // Load data
        const ganttData = {{ gantt_data_json|safe }};
        gantt.parse(ganttData);

        // Add CSS-based today marker as fallback
        function addCssBasedTodayMarker() {
            const today = new Date();
            const todayStr = gantt.date.date_to_str("%Y-%m-%d")(today);

            // Position the today marker
            setTimeout(function() {
                try {
                    // Find the position of today in the timeline
                    const todayPos = gantt.posFromDate(today);
                    if (todayPos) {
                        // Add the marker using CSS
                        const container = document.querySelector('.gantt-container');
                        if (container && container.style) {
                            container.style.setProperty('--today-position', todayPos + 'px');
                        }
                    }

                    // Mark tasks that are active today
                    gantt.eachTask(function(task) {
                        const startDate = new Date(task.start_date);
                        const endDate = new Date(task.end_date);
                        if (startDate <= today && endDate >= today) {
                            const taskElement = gantt.getTaskNode(task.id);
                            if (taskElement) {
                                taskElement.classList.add('today');
                            }
                        }
                    });
                } catch (e) {
                    console.log("Error positioning today marker: " + e.message);
                }
            }, 500); // Delay to ensure the gantt is fully rendered
        }

        // Call the function to add the CSS-based today marker
        addCssBasedTodayMarker();

        // Update the marker position when the chart is scrolled or zoomed
        gantt.attachEvent("onGanttScroll", addCssBasedTodayMarker);
        gantt.attachEvent("onGanttRender", addCssBasedTodayMarker);

        // ===== EVENT HANDLERS =====
        // Date range selector
        document.getElementById("date_range").addEventListener("change", function() {
            const customDateContainer = document.getElementById("custom-date-container");
            if (this.value === "custom") {
                customDateContainer.style.display = "block";
            } else {
                customDateContainer.style.display = "none";
            }
        });

        // Initialize date range selector based on current value
        if (document.getElementById("date_range").value === "custom") {
            document.getElementById("custom-date-container").style.display = "block";
        }

        // Zoom controls
        document.getElementById("zoom-in").addEventListener("click", function() {
            gantt.ext.zoom.zoomIn();
        });

        document.getElementById("zoom-out").addEventListener("click", function() {
            gantt.ext.zoom.zoomOut();
        });

        document.getElementById("today").addEventListener("click", function() {
            gantt.showDate(new Date());
        });

        // Scale options
        document.querySelectorAll(".scale-option").forEach(function(option) {
            option.addEventListener("click", function(e) {
                e.preventDefault();
                const scale = this.getAttribute("data-scale");
                gantt.ext.zoom.setLevel(scale);
            });
        });

        // Toggle critical path
        let criticalPathEnabled = false;
        document.getElementById("toggle-critical-path").addEventListener("click", function() {
            criticalPathEnabled = !criticalPathEnabled;
            if (criticalPathEnabled) {
                gantt.config.highlight_critical_path = true;
                this.classList.add("active");
            } else {
                gantt.config.highlight_critical_path = false;
                this.classList.remove("active");
            }
            gantt.render();
        });

        // Toggle resource view
        let resourceViewEnabled = false;
        document.getElementById("toggle-resources").addEventListener("click", function() {
            resourceViewEnabled = !resourceViewEnabled;
            const resourcePanel = document.getElementById("resource-panel");

            if (resourceViewEnabled) {
                this.classList.add("active");
                resourcePanel.style.display = "block";

                // Create resource chart data
                const departments = {};
                gantt.getTaskByTime().forEach(function(task) {
                    if (task.department && task.type === "stage") {
                        if (!departments[task.department]) {
                            departments[task.department] = {
                                name: task.department,
                                tasks: 0,
                                hours: 0
                            };
                        }

                        departments[task.department].tasks++;
                        const duration = (new Date(task.end_date) - new Date(task.start_date)) / (1000 * 60 * 60);
                        departments[task.department].hours += duration;
                    }
                });

                // Create resource chart
                const resourceData = Object.values(departments);

                // Simple bar chart using HTML/CSS
                const resourceChart = document.getElementById("resource_chart");
                resourceChart.innerHTML = "";

                if (resourceData.length === 0) {
                    resourceChart.innerHTML = "<div class='text-center p-5'>No resource data available</div>";
                } else {
                    // Find max hours for scaling
                    const maxHours = Math.max(...resourceData.map(d => d.hours));

                    // Create chart HTML
                    let chartHtml = "<div class='resource-chart'>";
                    resourceData.forEach(function(dept) {
                        const percentage = Math.round((dept.hours / maxHours) * 100);
                        chartHtml += `
                            <div class='resource-bar-container'>
                                <div class='resource-label'>${dept.name} (${dept.tasks} tasks)</div>
                                <div class='resource-bar-wrapper'>
                                    <div class='resource-bar' style='width: ${percentage}%'></div>
                                    <div class='resource-value'>${Math.round(dept.hours)} hours</div>
                                </div>
                            </div>
                        `;
                    });
                    chartHtml += "</div>";

                    resourceChart.innerHTML = chartHtml;
                }
            } else {
                this.classList.remove("active");
                resourcePanel.style.display = "none";
            }
        });

        // View mode buttons
        document.getElementById("view-timeline").addEventListener("click", function() {
            gantt.config.layout = { css: "gantt_container", rows: [ {cols: [ {view: "grid"}, {resizer: true}, {view: "timeline"} ]} ]};
            gantt.resetLayout();
        });

        document.getElementById("view-resources").addEventListener("click", function() {
            gantt.config.layout = { css: "gantt_container", rows: [ {cols: [ {view: "grid"}, {resizer: true}, {view: "timeline"} ]}, {resizer: true, height: 1}, {view: "resourceGrid", height: 200} ]};
            gantt.resetLayout();
        });

        document.getElementById("view-critical-path").addEventListener("click", function() {
            gantt.config.highlight_critical_path = true;
            gantt.render();
        });

        // Export controls
        document.getElementById("export-pdf").addEventListener("click", function(e) {
            e.preventDefault();
            gantt.exportToPDF({
                name: "case_gantt_chart.pdf",
                header: "<h1>Case Production Timeline</h1>"
            });
        });

        document.getElementById("export-png").addEventListener("click", function(e) {
            e.preventDefault();
            gantt.exportToPNG({
                name: "case_gantt_chart.png",
                header: "<h1>Case Production Timeline</h1>"
            });
        });

        document.getElementById("export-excel").addEventListener("click", function(e) {
            e.preventDefault();
            gantt.exportToExcel({
                name: "case_gantt_chart.xlsx"
            });
        });

        document.getElementById("export-json").addEventListener("click", function(e) {
            e.preventDefault();
            const data = gantt.serialize();
            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(data, null, 2));
            const downloadAnchorNode = document.createElement('a');
            downloadAnchorNode.setAttribute("href", dataStr);
            downloadAnchorNode.setAttribute("download", "case_gantt_chart.json");
            document.body.appendChild(downloadAnchorNode);
            downloadAnchorNode.click();
            downloadAnchorNode.remove();
        });

        // Save view button
        document.getElementById("save-view").addEventListener("click", function() {
            const viewName = prompt("Enter a name for this view:");
            if (viewName) {
                // Get current filter values
                const filters = {
                    date_range: document.getElementById("date_range").value,
                    start_date: document.getElementById("start_date").value,
                    department: document.getElementById("department").value,
                    status: document.getElementById("status").value,
                    priority: document.getElementById("priority").value,
                    search: document.getElementById("search").value,
                    page_size: document.getElementById("page_size").value
                };

                // Save to localStorage
                const savedViews = JSON.parse(localStorage.getItem("ganttSavedViews") || "[]");
                savedViews.push({
                    name: viewName,
                    filters: filters,
                    date: new Date().toISOString()
                });
                localStorage.setItem("ganttSavedViews", JSON.stringify(savedViews));

                // Update saved views dropdown
                updateSavedViewsList();

                alert("View saved successfully!");
            }
        });

        // Function to update saved views list
        function updateSavedViewsList() {
            const savedViewsList = document.getElementById("saved-views-list");
            const savedViews = JSON.parse(localStorage.getItem("ganttSavedViews") || "[]");

            // Clear existing items except the default ones
            const defaultItems = Array.from(savedViewsList.querySelectorAll(".dropdown-item")).slice(0, 3);
            savedViewsList.innerHTML = "";
            defaultItems.forEach(item => savedViewsList.appendChild(item));

            // Add saved views
            if (savedViews.length > 0) {
                savedViewsList.appendChild(document.createElement("hr"));

                savedViews.forEach(function(view, index) {
                    const li = document.createElement("li");
                    const a = document.createElement("a");
                    a.className = "dropdown-item";
                    a.href = "#";
                    a.textContent = view.name;
                    a.dataset.index = index;
                    a.addEventListener("click", function(e) {
                        e.preventDefault();
                        const index = parseInt(this.dataset.index);
                        const view = savedViews[index];

                        // Apply filters
                        Object.keys(view.filters).forEach(function(key) {
                            const element = document.getElementById(key);
                            if (element) {
                                element.value = view.filters[key];
                            }
                        });

                        // Submit form
                        document.getElementById("filter-form").submit();
                    });
                    li.appendChild(a);
                    savedViewsList.appendChild(li);
                });
            }
        }

        // Initialize saved views list
        updateSavedViewsList();

        // Add custom CSS for tooltips and other elements
        const customCSS = `
            .gantt-tooltip {
                background: #fff;
                box-shadow: 0 5px 15px rgba(0,0,0,0.15);
                border-radius: 5px;
                padding: 0;
                max-width: 350px;
                overflow: hidden;
            }
            .tooltip-header {
                padding: 8px 12px;
                background: #f8f9fa;
                border-bottom: 1px solid #dee2e6;
                font-weight: bold;
                font-size: 14px;
            }
            .tooltip-body {
                padding: 10px;
            }
            .tooltip-table {
                width: 100%;
                border-collapse: collapse;
            }
            .tooltip-table td {
                padding: 4px 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            .tooltip-table tr:last-child td {
                border-bottom: none;
            }
            .today-marker {
                background-color: rgba(255, 0, 0, 0.5);
                width: 1px;
            }

            /* CSS-based today marker (fallback) */
            .gantt_task_line.today {
                border-left: 2px dashed #ff5252;
            }

            /* Add a pseudo-element for the today line */
            .gantt-container {
                position: relative;
                --today-position: 0px;
            }

            .gantt-container::before {
                content: '';
                position: absolute;
                top: 0;
                bottom: 0;
                left: var(--today-position);
                width: 2px;
                background-color: rgba(255, 0, 0, 0.5);
                z-index: 1;
                pointer-events: none;
            }
            .critical-task {
                border: 2px solid #ff5252 !important;
                box-shadow: 0 0 5px rgba(255, 82, 82, 0.5);
            }
            .resource-chart {
                width: 100%;
            }
            .resource-bar-container {
                margin-bottom: 15px;
            }
            .resource-label {
                margin-bottom: 5px;
                font-weight: bold;
            }
            .resource-bar-wrapper {
                height: 25px;
                background-color: #f0f0f0;
                border-radius: 4px;
                position: relative;
            }
            .resource-bar {
                height: 100%;
                background-color: #4285f4;
                border-radius: 4px;
            }
            .resource-value {
                position: absolute;
                right: 10px;
                top: 0;
                line-height: 25px;
                color: #333;
                font-weight: bold;
            }
        `;

        const styleElement = document.createElement('style');
        styleElement.textContent = customCSS;
        document.head.appendChild(styleElement);
        } // End of initGantt function
    });
</script>
{% endblock %}
