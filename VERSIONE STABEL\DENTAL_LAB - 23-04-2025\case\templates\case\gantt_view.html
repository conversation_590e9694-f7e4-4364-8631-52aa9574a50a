{% extends "base.html" %}
{% load static %}

{% block title %}Production Timeline - Dental Lab{% endblock %}

{% block extra_head %}
<!-- Use CDN links for all required libraries -->
<link href="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.12/dist/sweetalert2.min.css" rel="stylesheet">

<!-- Custom CSS -->
<link href="{% static 'css/production-timeline.css' %}" rel="stylesheet">
<link href="{% static 'css/gantt-chart.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay hidden">
        <div class="loading-spinner"></div>
        <p class="loading-text">Loading...</p>
    </div>

    <!-- Header -->
    <div class="dashboard-card flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-dark mb-2">Production Timeline</h1>
            <p class="text-sm text-gray-600">Track and manage dental lab cases</p>
        </div>
        <div class="flex space-x-4">
            <button id="exportPDF" class="btn btn-primary">
                <i class="fas fa-file-pdf mr-2" aria-hidden="true"></i>Export PDF
            </button>
            <button id="refreshData" class="btn btn-primary">
                <i class="fas fa-sync-alt mr-2" aria-hidden="true"></i>Refresh
            </button>
        </div>
    </div>

    <!-- Statistics Grid -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-clipboard-list" aria-hidden="true"></i>
            </div>
            <div class="stat-value" aria-label="Total Cases">{{ analytics.total_cases }}</div>
            <div class="stat-label">Total Cases</div>
            <div class="trend mt-2 text-sm">
                <i class="fas fa-arrow-up text-success" aria-hidden="true"></i>
                <span class="text-success">12%</span> vs last month
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-clock" aria-hidden="true"></i>
            </div>
            <div class="stat-value" aria-label="In Progress Cases">{{ analytics.in_progress }}</div>
            <div class="stat-label">In Progress</div>
            <div class="trend mt-2 text-sm">
                <span class="text-gray-600">Active cases</span>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-check-circle" aria-hidden="true"></i>
            </div>
            <div class="stat-value" aria-label="Completed Cases">{{ analytics.completed_cases }}</div>
            <div class="stat-label">Completed</div>
            <div class="trend mt-2 text-sm">
                <i class="fas fa-arrow-up text-success" aria-hidden="true"></i>
                <span class="text-success">8%</span> completion rate
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-exclamation-triangle" aria-hidden="true"></i>
            </div>
            <div class="stat-value" aria-label="Delayed Cases">{{ analytics.delayed_cases }}</div>
            <div class="stat-label">Delayed Cases</div>
            <div class="trend mt-2 text-sm">
                <span class="text-danger">Requires attention</span>
            </div>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div class="dashboard-card mb-6">
        <form id="filterForm" class="filters-section">
            <div class="filter-group">
                <label for="start_date" class="filter-label">Date Range</label>
                <input type="text"
                       id="start_date"
                       name="start_date"
                       class="filter-input flatpickr"
                       value="{{ start_date|date:'Y-m-d' }}"
                       placeholder="Select date range"
                       aria-label="Start date">
            </div>

            <div class="filter-group">
                <label for="department" class="filter-label">Department</label>
                <select id="department" name="department" class="filter-input" aria-label="Select department">
                    <option value="">All Departments</option>
                    {% for dept in departments %}
                    <option value="{{ dept.id }}" {% if selected_department == dept.id %}selected{% endif %}>
                        {{ dept.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>

            <div class="filter-group">
                <label for="status" class="filter-label">Status</label>
                <select id="status" name="status" class="filter-input" aria-label="Select status">
                    <option value="">All Statuses</option>
                    {% for status_code, status_name in case_statuses %}
                    <option value="{{ status_code }}" {% if selected_status == status_code %}selected{% endif %}>
                        {{ status_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>

            <div class="filter-group">
                <label for="priority" class="filter-label">Priority</label>
                <select id="priority" name="priority" class="filter-input" aria-label="Select priority">
                    <option value="">All Priorities</option>
                    {% for priority_code, priority_name in case_priorities %}
                    <option value="{{ priority_code }}" {% if selected_priority == priority_code %}selected{% endif %}>
                        {{ priority_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>

            <div class="filter-group">
                <label for="page_size" class="filter-label">Cases per Page</label>
                <select id="page_size" name="page_size" class="filter-input" aria-label="Select cases per page">
                    <option value="25" {% if page_size == 25 %}selected{% endif %}>25 cases</option>
                    <option value="50" {% if page_size == 50 %}selected{% endif %}>50 cases</option>
                    <option value="100" {% if page_size == 100 %}selected{% endif %}>100 cases</option>
                </select>
            </div>

            <div class="filter-group flex items-end">
                <button type="submit" class="btn btn-primary w-full">
                    <i class="fas fa-search mr-2" aria-hidden="true"></i>Apply Filters
                </button>
            </div>
        </form>
    </div>

    <!-- Gantt Chart -->
    <div class="gantt-container">
        <div class="gantt-header">
            <h2 class="gantt-title">Production Timeline</h2>
            <div class="gantt-controls">
                <div class="gantt-view-mode" role="group" aria-label="Timeline view modes">
                    <button class="view-mode-button active" data-view="Day">Day</button>
                    <button class="view-mode-button" data-view="Week">Week</button>
                    <button class="view-mode-button" data-view="Month">Month</button>
                </div>
                <button id="gantt-today" class="btn btn-outline-primary" aria-label="Go to today">
                    <i class="fas fa-calendar-day" aria-hidden="true"></i><span class="btn-text"> Today</span>
                </button>
                <button id="gantt-zoom-in" class="btn btn-outline-secondary" aria-label="Zoom in">
                    <i class="fas fa-search-plus" aria-hidden="true"></i>
                </button>
                <button id="gantt-zoom-out" class="btn btn-outline-secondary" aria-label="Zoom out">
                    <i class="fas fa-search-minus" aria-hidden="true"></i>
                </button>
            </div>
        </div>

        {% if cases %}
        <div id="gantt-chart" class="gantt-chart" aria-label="Production timeline gantt chart" role="region" tabindex="0"></div>

        <div class="gantt-legend" aria-label="Chart legend">
            <div class="legend-item">
                <span class="legend-color" style="background-color: var(--primary);"></span>
                <span>In Progress</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: var(--success);"></span>
                <span>Completed</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: var(--warning);"></span>
                <span>Pending Acceptance</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: var(--secondary);"></span>
                <span>On Hold</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: var(--danger);"></span>
                <span>Cancelled</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: var(--info);"></span>
                <span>Ready to Ship</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: #20c997;"></span>
                <span>Delivered</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: #6f42c1;"></span>
                <span>Stage</span>
            </div>
        </div>
        {% else %}
        <div class="gantt-empty-state">
            <i class="fas fa-calendar-alt" aria-hidden="true"></i>
            <h3>No cases found</h3>
            <p>There are no cases matching your filter criteria. Try adjusting your filters or adding new cases.</p>
            <a href="{% url 'case:case_create' %}" class="btn btn-primary">
                <i class="fas fa-plus mr-1" aria-hidden="true"></i> Create New Case
            </a>
        </div>
        {% endif %}
    </div>

    <!-- Cases Table -->
    <div class="dashboard-card">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold">Case Details</h2>
            <div class="text-sm text-gray-600">
                Showing {{ cases.count }} cases
            </div>
        </div>

        <div class="table-responsive">
            <table class="cases-table" aria-label="Cases table">
                <thead>
                    <tr>
                        <th scope="col" data-sort="case-number">Case #</th>
                        <th scope="col" data-sort="patient">Patient</th>
                        <th scope="col" data-sort="received-date">Received Date</th>
                        <th scope="col" data-sort="deadline">Deadline</th>
                        <th scope="col" data-sort="status">Status</th>
                        <th scope="col" data-sort="stage">Stage</th>
                        <th scope="col" data-sort="priority">Priority</th>
                        <th scope="col">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for case in cases %}
                    <tr class="case-row" data-case-number="{{ case.case_number }}">
                        <td class="font-medium">#{{ case.case_number }}</td>
                        <td>{{ case.patient }}</td>
                        <td>{{ case.received_date_time|date:"Y-m-d H:i" }}</td>
                        <td>
                            {% if case.deadline %}
                                <span class="{% if case.is_overdue %}text-danger{% endif %}">
                                    {{ case.deadline|date:"Y-m-d" }}
                                </span>
                            {% else %}
                                <span class="text-gray-400">Not set</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="status-badge status-{{ case.status|lower }}">
                                {{ case.get_status_display }}
                            </span>
                        </td>
                        <td>
                            {% if case.current_stage %}
                                <div class="text-sm">
                                    <div>{{ case.current_stage.name }}</div>
                                    <div class="text-gray-500 text-xs">
                                        {{ case.current_stage.department.name }}
                                    </div>
                                </div>
                            {% else %}
                                <span class="text-gray-400">Not assigned</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="priority-badge priority-{{ case.priority }}">
                                {{ case.get_priority_display }}
                            </span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-icon view-button" aria-label="View case details for #{{ case.case_number }}">
                                    <i class="fas fa-eye" aria-hidden="true"></i>
                                </button>
                                <button class="btn-icon edit-button" aria-label="Edit case #{{ case.case_number }}">
                                    <i class="fas fa-edit" aria-hidden="true"></i>
                                </button>
                                <button class="btn-icon history-button" aria-label="View history for case #{{ case.case_number }}">
                                    <i class="fas fa-history" aria-hidden="true"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="pagination-controls">
            <span class="page-info">Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
            <div class="page-buttons">
                {% if page_obj.has_previous %}
                <a href="?page=1{{ request.GET.urlencode }}" class="page-button" aria-label="First page">
                    <i class="fas fa-angle-double-left" aria-hidden="true"></i>
                </a>
                <a href="?page={{ page_obj.previous_page_number }}{{ request.GET.urlencode }}" class="page-button" aria-label="Previous page">
                    <i class="fas fa-angle-left" aria-hidden="true"></i>
                </a>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <span class="page-button active">{{ num }}</span>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <a href="?page={{ num }}{{ request.GET.urlencode }}" class="page-button">{{ num }}</a>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}{{ request.GET.urlencode }}" class="page-button" aria-label="Next page">
                    <i class="fas fa-angle-right" aria-hidden="true"></i>
                </a>
                <a href="?page={{ page_obj.paginator.num_pages }}{{ request.GET.urlencode }}" class="page-button" aria-label="Last page">
                    <i class="fas fa-angle-double-right" aria-hidden="true"></i>
                </a>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="toast-container" aria-live="polite"></div>
</div>

<!-- Case Detail Modal Template (hidden) -->
<template id="caseDetailModalTemplate">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title">Case Details</h2>
            <button type="button" class="close-modal" aria-label="Close">×</button>
        </div>
        <div class="modal-body">
            <div class="case-header">
                <span class="case-number"></span>
                <span class="case-status"></span>
            </div>
            <div class="case-details-grid">
                <div class="detail-group">
                    <label>Patient</label>
                    <div class="patient-name"></div>
                </div>
                <div class="detail-group">
                    <label>Dentist</label>
                    <div class="dentist-name"></div>
                </div>
                <div class="detail-group">
                    <label>Received Date</label>
                    <div class="received-date"></div>
                </div>
                <div class="detail-group">
                    <label>Deadline</label>
                    <div class="deadline"></div>
                </div>
                <div class="detail-group">
                    <label>Priority</label>
                    <div class="priority"></div>
                </div>
                <div class="detail-group">
                    <label>Department</label>
                    <div class="department"></div>
                </div>
                <div class="detail-group full-width">
                    <label>Current Stage</label>
                    <div class="current-stage"></div>
                </div>
                <div class="detail-group full-width">
                    <label>Notes</label>
                    <div class="case-notes"></div>
                </div>
            </div>
            <div class="timeline-section">
                <h3>Case Timeline</h3>
                <div class="case-timeline"></div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary close-modal">Close</button>
            <button type="button" class="btn btn-primary edit-case">Edit Case</button>
        </div>
    </div>
</template>
{% endblock %}

{% block extra_js %}
<!-- Load all required libraries from CDN -->
<script src="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/frappe-gantt@0.6.1/dist/frappe-gantt.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.12/dist/sweetalert2.all.min.js"></script>

<!-- Initialize Gantt data -->
<script>
    // Pass Gantt tasks from Django to JS
    window.ganttTasks = {{ gantt_tasks_json|safe }};
</script>

<!-- Custom scripts -->
<script src="{% static 'js/production-timeline/utils.js' %}"></script>
<script src="{% static 'js/production-timeline/toast.js' %}"></script>
<script src="{% static 'js/production-timeline/gantt-chart-improved.js' %}"></script>
<script src="{% static 'js/production-timeline/case-management.js' %}"></script>
<script src="{% static 'js/production-timeline/pdf-export.js' %}"></script>
<script src="{% static 'js/production-timeline/app.js' %}"></script>
{% endblock %}
