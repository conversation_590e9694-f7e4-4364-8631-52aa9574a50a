{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Profile" %} - {{ user.get_full_name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Profile Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <!-- Profile Image -->
                        <div class="position-relative me-4">
                            {% if user.profile_image %}
                                <img src="{{ user.profile_image.url }}" 
                                     alt="Profile" 
                                     class="rounded-circle"
                                     style="width: 100px; height: 100px; object-fit: cover;">
                            {% else %}
                                <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center text-white"
                                     style="width: 100px; height: 100px; font-size: 2.5rem;">
                                    {{ user.get_initials }}
                                </div>
                            {% endif %}
                            <button class="btn btn-sm btn-light position-absolute bottom-0 end-0 rounded-circle shadow-sm"
                                    data-bs-toggle="modal" 
                                    data-bs-target="#changeProfileImageModal">
                                <i class="bi bi-camera"></i>
                            </button>
                        </div>

                        <!-- User Info -->
                        <div>
                            <h1 class="h3 mb-1">{{ user.get_full_name }}</h1>
                            <p class="text-muted mb-2">
                                <i class="bi bi-envelope"></i> {{ user.email }}
                                {% if user.email_verified %}
                                    <span class="badge bg-success ms-2">
                                        <i class="bi bi-check-circle"></i> {% trans "Verified" %}
                                    </span>
                                {% endif %}
                            </p>
                            <p class="mb-0">
                                <span class="badge bg-primary">{{ user.get_user_type_display }}</span>
                                {% if user.specialization %}
                                    <span class="badge bg-info">{{ user.specialization }}</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Left Column -->
        <div class="col-12 col-lg-4 mb-4">
            <!-- Personal Information -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-transparent">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">{% trans "Personal Information" %}</h5>
                        <button class="btn btn-sm btn-outline-primary" 
                                data-bs-toggle="modal" 
                                data-bs-target="#editProfileModal">
                            <i class="bi bi-pencil"></i> {% trans "Edit" %}
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        {% if user.phone_number %}
                        <li class="mb-3">
                            <small class="text-muted d-block">{% trans "Phone" %}</small>
                            <div><i class="bi bi-telephone"></i> {{ user.phone_number }}</div>
                        </li>
                        {% endif %}

                        {% if user.date_of_birth %}
                        <li class="mb-3">
                            <small class="text-muted d-block">{% trans "Date of Birth" %}</small>
                            <div><i class="bi bi-calendar"></i> {{ user.date_of_birth }}</div>
                        </li>
                        {% endif %}

                        {% if user.address_line1 %}
                        <li class="mb-3">
                            <small class="text-muted d-block">{% trans "Address" %}</small>
                            <div>
                                <i class="bi bi-geo-alt"></i>
                                {{ user.address_line1 }}
                                {% if user.address_line2 %}
                                    <br>{{ user.address_line2 }}
                                {% endif %}
                                <br>
                                {{ user.city }}{% if user.state %}, {{ user.state }}{% endif %}
                                {% if user.postal_code %} {{ user.postal_code }}{% endif %}
                                {% if user.country %}<br>{{ user.country }}{% endif %}
                            </div>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </div>

            <!-- Account Information -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">{% trans "Account Information" %}</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-3">
                            <small class="text-muted d-block">{% trans "Member Since" %}</small>
                            <div><i class="bi bi-clock-history"></i> {{ user.date_joined|date }}</div>
                        </li>
                        <li class="mb-3">
                            <small class="text-muted d-block">{% trans "Last Login" %}</small>
                            <div><i class="bi bi-clock"></i> {{ user.last_login|date:"d M Y H:i" }}</div>
                        </li>
                        <li>
                            <small class="text-muted d-block">{% trans "Account Status" %}</small>
                            <div>
                                {% if user.is_active %}
                                    <span class="badge bg-success">{% trans "Active" %}</span>
                                {% else %}
                                    <span class="badge bg-danger">{% trans "Inactive" %}</span>
                                {% endif %}
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="card-footer bg-transparent">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" 
                                data-bs-toggle="modal" 
                                data-bs-target="#changePasswordModal">
                            <i class="bi bi-key"></i> {% trans "Change Password" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="col-12 col-lg-8">
            <!-- Tabs Navigation -->
            <ul class="nav nav-tabs mb-4" id="profileTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="qualifications-tab" data-bs-toggle="tab"
                            data-bs-target="#qualifications" type="button" role="tab">
                        <i class="bi bi-award"></i> {% trans "Qualifications" %}
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="activity-tab" data-bs-toggle="tab"
                            data-bs-target="#activity" type="button" role="tab">
                        <i class="bi bi-activity"></i> {% trans "Activity" %}
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="settings-tab" data-bs-toggle="tab"
                            data-bs-target="#settings" type="button" role="tab">
                        <i class="bi bi-gear"></i> {% trans "Settings" %}
                    </button>
                </li>
            </ul>

            <!-- Tabs Content -->
            <div class="tab-content" id="profileTabsContent">
                <!-- Qualifications Tab -->
                <div class="tab-pane fade show active" id="qualifications" role="tabpanel">
                    <div class="card shadow-sm">
                        <div class="card-header bg-transparent">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">{% trans "Professional Qualifications" %}</h5>
                                <button class="btn btn-sm btn-primary" 
                                        data-bs-toggle="modal" 
                                        data-bs-target="#addQualificationModal">
                                    <i class="bi bi-plus"></i> {% trans "Add" %}
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            {% if qualifications %}
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Title" %}</th>
                                                <th>{% trans "Institution" %}</th>
                                                <th>{% trans "Date" %}</th>
                                                <th>{% trans "Status" %}</th>
                                                <th>{% trans "Actions" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for qual in qualifications %}
                                            <tr>
                                                <td>{{ qual.title }}</td>
                                                <td>{{ qual.institution }}</td>
                                                <td>{{ qual.date_obtained|date }}</td>
                                                <td>
                                                    {% if qual.is_verified %}
                                                        <span class="badge bg-success">
                                                            <i class="bi bi-check-circle"></i> {% trans "Verified" %}
                                                        </span>
                                                    {% else %}
                                                        <span class="badge bg-warning">
                                                            <i class="bi bi-clock"></i> {% trans "Pending" %}
                                                        </span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary" 
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#viewQualificationModal-{{ qual.id }}">
                                                            <i class="bi bi-eye"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger"
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#deleteQualificationModal-{{ qual.id }}">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="text-center py-4">
                                    <div class="mb-3">
                                        <i class="bi bi-mortarboard text-muted" style="font-size: 3rem;"></i>
                                    </div>
                                    <h6>{% trans "No Qualifications Added" %}</h6>
                                    <p class="text-muted">
                                        {% trans "Add your professional qualifications to complete your profile." %}
                                    </p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Activity Tab -->
                <div class="tab-pane fade" id="activity" role="tabpanel">
                    <div class="card shadow-sm">
                        <div class="card-header bg-transparent">
                            <h5 class="mb-0">{% trans "Recent Activity" %}</h5>
                        </div>
                        <div class="card-body">
                            {% if recent_activity %}
                                <div class="timeline">
                                    {% for activity in recent_activity %}
                                    <div class="timeline-item">
                                        <div class="timeline-marker"></div>
                                        <div class="timeline-content">
                                            <h6 class="mb-1">{{ activity.get_action_display }}</h6>
                                            <p class="text-muted small mb-0">
                                                {{ activity.timestamp|date:"d M Y H:i" }}
                                                {% if activity.ip_address %}
                                                    <span class="ms-2">
                                                        <i class="bi bi-globe"></i> {{ activity.ip_address }}
                                                    </span>
                                                {% endif %}
                                            </p>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                                <div class="text-center mt-4">
                                    <a href="{% url 'accounts:activity_log' %}" class="btn btn-outline-primary">
                                        {% trans "View Full Activity Log" %}
                                    </a>
                                </div>
                            {% else %}
                                <div class="text-center py-4">
                                    <div class="mb-3">
                                        <i class="bi bi-activity text-muted" style="font-size: 3rem;"></i>
                                    </div>
                                    <h6>{% trans "No Recent Activity" %}</h6>
                                    <p class="text-muted">
                                        {% trans "Your account activity will appear here." %}
                                    </p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Settings Tab -->
                <div class="tab-pane fade" id="settings" role="tabpanel">
                    <div class="card shadow-sm">
                        <div class="card-header bg-transparent">
                            <h5 class="mb-0">{% trans "Account Settings" %}</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" action="{% url 'accounts:settings_update' %}">
                                {% csrf_token %}

                                <!-- Notification Settings -->
                                <h6 class="mb-3">{% trans "Notifications" %}</h6>
                                <div class="mb-4">
                                    <div class="form-check form-switch mb-2">
                                        <input type="checkbox" class="form-check-input" 
                                               id="emailNotifications" 
                                               name="email_notifications"
                                               {% if user_settings.email_notifications %}checked{% endif %}>
                                        <label class="form-check-label" for="emailNotifications">
                                            {% trans "Email Notifications" %}
                                        </label>
                                    </div>
                                    <div class="form-check form-switch mb-2">
                                        <input type="checkbox" class="form-check-input" 
                                               id="smsNotifications" 
                                               name="sms_notifications"
                                               {% if user_settings.sms_notifications %}checked{% endif %}>
                                        <label class="form-check-label" for="smsNotifications">
                                            {% trans "SMS Notifications" %}
                                        </label>
                                    </div>
                                </div>

                                <!-- Display Settings -->
                                <h6 class="mb-3">{% trans "Display" %}</h6>
                                <div class="mb-4">
                                    <div class="form-group mb-3">
                                        <label class="form-label" for="theme">{% trans "Theme" %}</label>
                                        <select class="form-select" id="theme" name="theme">
                                            <option value="light" {% if user_settings.theme == 'light' %}selected{% endif %}>
                                                {% trans "Light" %}
                                            </option>
                                            <option value="dark" {% if user_settings.theme == 'dark' %}selected{% endif %}>
                                                {% trans "Dark" %}
                                            </option>
                                            <option value="system" {% if user_settings.theme == 'system' %}selected{% endif %}>
                                                {% trans "System Default" %}
                                            </option>
                                        </select>
                                    </div>

                                    <div class="form-group mb-3">
                                        <label class="form-label" for="language">{% trans "Language" %}</label>
                                        <select class="form-select" id="language" name="preferred_language">
                                            {% for code, name in LANGUAGE_CHOICES %}
                                                <option value="{{ code }}" 
                                                        {% if user_settings.preferred_language == code %}selected{% endif %}>
                                                    {{ name }}
                                                </option>
                                            {% endfor %}
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label" for="itemsPerPage">{% trans "Items per page" %}</label>
                                        <select class="form-select" id="itemsPerPage" name="items_per_page">
                                            <option value="10" {% if user_settings.items_per_page == 10 %}selected{% endif %}>10</option>
                                            <option value="25" {% if user_settings.items_per_page == 25 %}selected{% endif %}>25</option>
                                            <option value="50" {% if user_settings.items_per_page == 50 %}selected{% endif %}>50</option>
                                            <option value="100" {% if user_settings.items_per_page == 100 %}selected{% endif %}>100</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Security Settings -->
                                <h6 class="mb-3">{% trans "Security" %}</h6>
                                <div class="mb-4">
                                    <div class="form-check form-switch mb-2">
                                        <input type="checkbox" class="form-check-input" 
                                               id="twoFactorAuth" 
                                               name="two_factor_enabled"
                                               {% if user.two_factor_enabled %}checked{% endif %}>
                                        <label class="form-check-label" for="twoFactorAuth">
                                            {% trans "Two-Factor Authentication" %}
                                        </label>
                                    </div>
                                    {% if not user.two_factor_enabled %}
                                        <small class="text-muted d-block mb-3">
                                            {% trans "Enable two-factor authentication to add an extra layer of security to your account." %}
                                            <a href="{% url 'accounts:setup_2fa' %}" class="text-decoration-none">
                                                {% trans "Set up now" %}
                                            </a>
                                        </small>
                                    {% endif %}
                                </div>

                                <!-- Submit Button -->
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        {% trans "Save Settings" %}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% comment %} <!-- Modals -->
{% include "accounts/components/modals/change_profile_image.html" %}
{% include "accounts/components/modals/edit_profile.html" %}
{% include "accounts/components/modals/change_password.html" %}
{% include "accounts/components/modals/add_qualification.html" %} {% endcomment %}

{% for qual in qualifications %}
    {% include "accounts/components/modals/view_qualification.html" with qualification=qual %}
    {% include "accounts/components/modals/delete_qualification.html" with qualification=qual %}
{% endfor %}

{% endblock %}

{% block extra_css %}
<style>
    /* Profile image styles */
    .profile-image {
        width: 100px;
        height: 100px;
        object-fit: cover;
    }

    /* Timeline styles */
    .timeline {
        position: relative;
        padding-left: 3rem;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 0.85rem;
        top: 0;
        height: 100%;
        width: 2px;
        background: var(--bs-border-color);
    }

    .timeline-item {
        position: relative;
        padding-bottom: 1.5rem;
    }

    .timeline-marker {
        position: absolute;
        left: -3rem;
        width: 1rem;
        height: 1rem;
        border-radius: 50%;
        background: var(--bs-primary);
        border: 2px solid #fff;
    }

    .timeline-content {
        background: #fff;
        border-radius: 0.25rem;
        padding: 1rem;
        border: 1px solid var(--bs-border-color);
    }

    /* Tab styles */
    .nav-tabs .nav-link {
        border: none;
        color: var(--bs-body-color);
        padding: 1rem 1.5rem;
    }

    .nav-tabs .nav-link.active {
        color: var(--bs-primary);
        border-bottom: 2px solid var(--bs-primary);
    }

    .nav-tabs .nav-link:hover {
        border-color: transparent;
        color: var(--bs-primary);
    }

    /* Card styles */
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .card-header {
        background-color: transparent;
        border-bottom: 1px solid var(--bs-border-color);
    }

    /* Form styles */
    .form-check-input:checked {
        background-color: var(--bs-primary);
        border-color: var(--bs-primary);
    }

    .form-select:focus {
        border-color: var(--bs-primary);
        box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Theme switcher
        const themeSelect = document.getElementById('theme');
        themeSelect.addEventListener('change', function() {
            document.documentElement.setAttribute('data-bs-theme', this.value);
        });

        // Profile image upload preview
        const profileImageInput = document.getElementById('profileImageInput');
        const profileImagePreview = document.getElementById('profileImagePreview');

        if (profileImageInput && profileImagePreview) {
            profileImageInput.addEventListener('change', function() {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        profileImagePreview.src = e.target.result;
                    }
                    reader.readAsDataURL(file);
                }
            });
        }

        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Form validation
        const forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach(function (form) {
            form.addEventListener('submit', function (event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    });
</script>
{% endblock %}