{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Set New Password" %}{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-12 col-md-8 col-lg-6">
            <div class="card shadow-sm">
                <div class="card-body p-4 p-md-5">
                    <!-- Header -->
                    <div class="text-center mb-4">
                        <h1 class="h3">
                            {% if validlink %}
                                {% trans "Set New Password" %}
                            {% else %}
                                {% trans "Invalid Password Reset Link" %}
                            {% endif %}
                        </h1>
                    </div>

                    {% if validlink %}
                        <!-- New Password Form -->
                        <form method="post" class="needs-validation" novalidate>
                            {% csrf_token %}

                            <!-- Alert Messages -->
                            {% if messages %}
                                {% for message in messages %}
                                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}

                            <!-- New Password Field -->
                            <div class="form-floating mb-3">
                                {{ form.new_password1 }}
                                <label for="{{ form.new_password1.id_for_label }}">{% trans "New Password" %}</label>
                                {% if form.new_password1.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.new_password1.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.new_password1.help_text %}
                                    <div class="form-text small">
                                        {{ form.new_password1.help_text|safe }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Confirm Password Field -->
                            <div class="form-floating mb-4">
                                {{ form.new_password2 }}
                                <label for="{{ form.new_password2.id_for_label }}">{% trans "Confirm New Password" %}</label>
                                {% if form.new_password2.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.new_password2.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Password Requirements -->
                            <div class="alert alert-info mb-4" role="alert">
                                <h6 class="alert-heading">{% trans "Password Requirements:" %}</h6>
                                <ul class="mb-0 small">
                                    <li>{% trans "At least 8 characters long" %}</li>
                                    <li>{% trans "Contains both uppercase and lowercase letters" %}</li>
                                    <li>{% trans "Includes at least one number" %}</li>
                                    <li>{% trans "Contains at least one special character" %}</li>
                                </ul>
                            </div>

                            <!-- Submit Button -->
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    {% trans "Set New Password" %}
                                </button>
                            </div>
                        </form>

                    {% else %}
                        <!-- Invalid Link Message -->
                        <div class="alert alert-danger" role="alert">
                            <h5 class="alert-heading">{% trans "Invalid or Expired Link" %}</h5>
                            <p>{% trans "The password reset link is invalid or has expired. Please request a new password reset link." %}</p>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="{% url 'accounts:password_reset' %}" class="btn btn-primary">
                                {% trans "Request New Reset Link" %}
                            </a>
                        </div>
                    {% endif %}

                    <!-- Back to Login Link -->
                    <div class="text-center mt-4">
                        <a href="{% url 'accounts:login' %}" class="text-decoration-none">
                            <i class="bi bi-arrow-left"></i> {% trans "Back to Login" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Password visibility toggle
    function togglePasswordVisibility(inputId) {
        const input = document.getElementById(inputId);
        const type = input.type === 'password' ? 'text' : 'password';
        input.type = type;
    }

    // Form validation
    (function () {
        'use strict'
        const forms = document.querySelectorAll('.needs-validation')
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault()
                    event.stopPropagation()
                }
                form.classList.add('was-validated')
            }, false)
        })
    })()
</script>
{% endblock %}

{% block extra_css %}
<style>
    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label {
        color: #6c757d;
        opacity: .65;
    }
    
    .card {
        border: none;
        border-radius: 1rem;
    }
    
    .btn-primary {
        padding: 0.75rem;
        font-weight: 500;
        border-radius: 0.5rem;
    }
    
    .alert ul {
        padding-left: 1.2rem;
        margin-bottom: 0;
    }
    
    .form-text ul {
        padding-left: 1.2rem;
        margin-top: 0.5rem;
    }
</style>
{% endblock %}