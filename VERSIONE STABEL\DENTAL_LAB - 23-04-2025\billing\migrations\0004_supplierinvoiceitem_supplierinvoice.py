# Generated by Django 5.2 on 2025-04-19 00:10

import django.db.models.deletion
import django.utils.timezone
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("billing", "0003_auto_20250417_2344"),
        ("items", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="SupplierInvoiceItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "description",
                    models.CharField(
                        blank=True,
                        help_text="Optional description, defaults to raw material name if blank.",
                        max_length=255,
                        verbose_name="Description",
                    ),
                ),
                (
                    "quantity",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("1.0"),
                        max_digits=10,
                        verbose_name="Quantity",
                    ),
                ),
                (
                    "price_per_unit",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="The price per unit for this item.",
                        max_digits=10,
                        verbose_name="Unit Price",
                    ),
                ),
            ],
            options={
                "verbose_name": "Supplier Invoice Item",
                "verbose_name_plural": "Supplier Invoice Items",
                "ordering": ["id"],
            },
        ),
        migrations.CreateModel(
            name="SupplierInvoice",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "supplier_name",
                    models.CharField(
                        help_text="Name of the supplier who issued the invoice.",
                        max_length=255,
                        verbose_name="Supplier Name",
                    ),
                ),
                (
                    "invoice_number",
                    models.CharField(
                        help_text="The invoice number as provided by the supplier.",
                        max_length=100,
                        verbose_name="Invoice Number",
                    ),
                ),
                (
                    "date",
                    models.DateField(
                        default=django.utils.timezone.now,
                        help_text="The date the invoice was issued by the supplier.",
                        verbose_name="Invoice Date",
                    ),
                ),
                (
                    "due_date",
                    models.DateField(
                        blank=True,
                        help_text="Optional: The date by which the payment is due.",
                        null=True,
                        verbose_name="Due Date",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("unpaid", "Unpaid"),
                            ("partial", "Partially Paid"),
                            ("paid", "Paid"),
                            ("cancelled", "Cancelled"),
                        ],
                        db_index=True,
                        default="unpaid",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="The total amount of the invoice in the specified currency.",
                        max_digits=12,
                        verbose_name="Total Amount",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Optional internal notes for the invoice.",
                        verbose_name="Notes",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "currency",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="supplier_invoices",
                        to="items.currency",
                    ),
                ),
            ],
            options={
                "verbose_name": "Supplier Invoice",
                "verbose_name_plural": "Supplier Invoices",
                "ordering": ["-date", "-id"],
            },
        ),
    ]
