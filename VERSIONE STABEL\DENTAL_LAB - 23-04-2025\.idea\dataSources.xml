<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="db" uuid="9b2ffccf-58e4-4b46-9d2e-7df51d232832">
      <driver-ref>sqlite.xerial</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>org.sqlite.JDBC</jdbc-driver>
      <jdbc-url>**************************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="Django default" uuid="3e87a36c-8061-4f76-9bcb-55b5e4a4639e">
      <driver-ref>sqlite.xerial</driver-ref>
      <synchronize>true</synchronize>
      <imported>true</imported>
      <remarks>$PROJECT_DIR$/LAB/settings.py</remarks>
      <jdbc-driver>org.sqlite.JDBC</jdbc-driver>
      <jdbc-url>**************************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>