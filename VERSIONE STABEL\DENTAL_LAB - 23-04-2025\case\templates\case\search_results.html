{% extends 'base.html' %}

{% block content %}
    <div class="container mt-4">
        <h1 class="mb-3">Search Results</h1>

        <!-- Search Input -->
        <div class="input-group mb-3">
            <input type="text" id="caseSearch" class="form-control" placeholder="Search by case number or patient name...">
            <div class="input-group-append">
                <button class="btn btn-outline-secondary" type="button" onclick="performSearch()">
                    <i class="fa fa-search"></i>
                </button>
            </div>
        </div>

        <!-- Search Results Container -->
        <div id="searchResults">
            <!-- Search results will be dynamically loaded here -->
            {% if results %}
                <!-- If the template is rendered directly, show these results -->
                {% comment %} {% include '/search_results.html' %} {% endcomment %}
            {% else %}
                <p>Enter a query to search for cases.</p>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_js %}
<script>
function performSearch() {
    var searchTerm = document.getElementById('caseSearch').value;
    if (!searchTerm.trim()) {
        alert("Please enter a search term.");
        return;
    }

    // Perform AJAX request
    $.ajax({
        url: '/case/search/',
        type: 'GET',
        data: { 'q': searchTerm },
        headers: { 'X-Requested-With': 'XMLHttpRequest' },
        success: function(data) {
            $('#searchResults').html(data.html);
        },
        error: function(xhr, status, error) {
            console.error("Error occurred while searching: ", status, error);
            $('#searchResults').html('<p>An error occurred while searching.</p>');
        }
    });
}
</script>
{% endblock %}
