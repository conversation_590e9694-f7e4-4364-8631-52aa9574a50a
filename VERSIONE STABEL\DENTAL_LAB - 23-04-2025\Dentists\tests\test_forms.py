from django.test import TestCase
from Dentists.forms import DentistForm # Assuming you have a DentistForm

# Create your tests here.

# Example:
# class DentistFormTest(TestCase):
#     def test_valid_form(self):
#         data = {'name': '<PERSON><PERSON> <PERSON>', 'specialization': 'Pediatric', ...}
#         form = DentistForm(data=data)
#         self.assertTrue(form.is_valid())

#     def test_invalid_form(self):
#         data = {'name': '', 'specialization': 'General', ...} # Missing name
#         form = DentistForm(data=data)
#         self.assertFalse(form.is_valid())
#         self.assertIn('name', form.errors)
