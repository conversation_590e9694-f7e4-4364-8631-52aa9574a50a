# accounts/utils.py

from PIL import Image
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
import os

def validate_image_file(image):
    """Validate uploaded image files"""
    # Check file size
    max_size = 5 * 1024 * 1024  # 5MB
    if image.size > max_size:
        raise ValidationError(_('Image file too large ( > 5MB )'))

    # Check file extension
    valid_extensions = ['.jpg', '.jpeg', '.png']
    ext = os.path.splitext(image.name)[1].lower()
    if ext not in valid_extensions:
        raise ValidationError(_('Unsupported file extension.'))

    # Check if it's a valid image
    try:
        img = Image.open(image)
        img.verify()
    except:
        raise ValidationError(_('Invalid image file'))

def process_profile_image(image):
    """Process profile image for optimal storage and display"""
    try:
        img = Image.open(image)
        
        # Convert to RGB if necessary
        if img.mode != 'RGB':
            img = img.convert('RGB')
        
        # Resize image while maintaining aspect ratio
        target_size = (300, 300)
        img.thumbnail(target_size, Image.Resampling.LANCZOS)
        
        # Center crop to square if necessary
        width, height = img.size
        if width != height:
            new_size = min(width, height)
            left = (width - new_size) // 2
            top = (height - new_size) // 2
            right = left + new_size
            bottom = top + new_size
            img = img.crop((left, top, right, bottom))
        
        return img
    except Exception as e:
        raise ValidationError(_('Error processing image: {}').format(str(e)))