{% extends 'base.html' %}
{% load static %}
{% block content %}
<!DOCTYPE html>
<html>
<head>
    <title>Tooth Selection</title>

    
    <style>
      .tooth {
        cursor: pointer;
      }
      .selected {
        fill: red;
      }
      .deselected {
        fill: #76f5f1;
      }
      .button-container {
        display: flex;
        justify-content: center;
        margin-top: 20px;
        gap: 10px;
        flex-wrap: wrap;
      }
      .button-container button {
        padding: 10px 20px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
      }
      .button-container button:hover {
        background-color: #0056b3;
      }
    </style>
</head>
<body>

  <div class="button-container">
    <button id="selectAllUpper">Select All teeth Upper</button>
    <button id="selectAllLower">Select All teeth Lower</button>
    <button id="clearAll">Clear All</button>
  </div>

  <svg xmlns:svg="http://www.w3.org/2000/svg" xmlns="http://www.w3.org/2000/svg" version="1.1" width="350.61084" height="370.54398">      
    <title id="title3476">Human Dental Arches</title>
    <metadata id="metadata8">
      <rdf:RDF>
        <cc:Work 
        rdf:about="">
          <dc:format>image/svg+xml</dc:format>
          <dc:type
             rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
          <dc:title>Human Dental Arches</dc:title>
        </cc:Work>
      </rdf:RDF>
    </metadata>
    <defs>
      <marker refX="0" refY="0" orient="auto" overflow="visible">
        <circle r="0.8" cy="0" cx="3"/>
        <circle r="0.8" cy="0" cx="6.5"/>
        <circle r="0.8" cy="0" cx="10"/>
      </marker>
      <marker orient="auto" refY="0" refX="0" overflow="visible">
        <path d="m5.77 0-8.65 5 0-10 8.65 5z" transform="scale(-0.2,-0.2)" style="fill-rule:evenodd;marker-start:none;stroke-width:3pt;stroke:#000"/>
      </marker>
      <marker orient="auto" refY="0" refX="0" overflow="visible">
        <path d="M0 0 5-5-12.5 0 5 5 0 0z" transform="matrix(0.2,0,0,0.2,1.2,0)" style="fill-rule:evenodd;marker-start:none;stroke-width:3pt;stroke:#000"/>
      </marker>
      <marker orient="auto" refY="0" refX="0" overflow="visible">
        <path d="M0 0 5-5-12.5 0 5 5 0 0z" transform="matrix(0.8,0,0,0.8,10,0)" style="fill-rule:evenodd;marker-start:none;stroke-width:3pt;stroke:#000"/>
      </marker>
    </defs>
    <path class="tooth-11 tooth-11-parent"
  d="m 113.894,31.723601 c 0.0561,0.43476 3.08165,4.91178 3.84449,6.93412 1.03137,2.18327 2.67371,4.15697 7.0469,5.19412 3.57083,-0.36803 7.19248,-0.4467 10.19825,-4.03315 l 7.38989,-9.40518 1.34756,-2.99193 c 0.97308,-2.16029 -1.13419,-4.14679 -3.10702,-4.99829 l -5.34936,-1.19716 c -3.12438,-0.16807 -5.19809,-0.93656 -11.30278,0.59905 l -5.72815,1.04816 c -2.08382,0.77109 -4.86648,0.46927 -4.92056,4.35665 0.10953,1.48595 -0.58405,2.8577 0.58078,4.49361 z"
  style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>

    <path
      class="tooth-12 tooth-12-parent"
      d="m 91.428666,35.605041 c 11.503464,-6.33738 9.146764,-4.4876 14.070254,-5.89646 1.71617,-0.51474 3.14074,-0.59168 3.86485,0.38286 l 2.6696,2.25199 c 1.81413,1.91332 1.6934,2.3195 1.92366,2.99912 0.8546,5.9162 -0.13307,5.84195 -0.32349,8.35998 -1.31549,2.1432 -2.9041,4.05602 -5.59189,5.04156 -1.65863,0.98199 -3.95557,0.88559 -6.39559,0.54752 l -4.012326,-0.81993 c -1.573083,0.19851 -2.928476,-0.68202 -4.307691,-1.44457 -2.910666,-1.71458 -3.662865,-4.14821 -4.663646,-6.49914 -0.201289,-1.52053 0.314192,-2.86745 1.499619,-4.05225 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    	
    <path
      class="tooth-13 tooth-13-parent"
      d="m 76.924949,61.279161 c -4.661053,-1.305 -6.843883,-3.69247 -7.339272,-6.81701 -0.575848,-3.05499 0.06037,-6.03463 2.258302,-8.91722 1.922291,-4.48919 3.829829,-4.24058 5.739016,-4.5421 1.703054,0.18022 3.25096,0.0983 4.758501,-0.0522 4.556612,-0.16942 6.253977,1.56471 7.352032,2.69905 3.845015,4.32077 3.420426,6.83837 4.35558,9.93011 0.481064,3.41383 0.268826,6.33289 -1.809063,7.91994 -6.322272,3.96823 -7.396961,2.02387 -10.042838,1.84972 -4.927107,-1.74143 -3.659851,-1.42841 -5.272258,-2.07053 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    
    <path
      class="tooth-14 tooth-14-parent"
      d="m 64.287549,60.689891 c -7.036983,2.05655 -7.499595,4.89403 -7.533489,7.78258 -0.357912,12.705 12.493542,12.48996 14.982456,11.51324 3.915814,0.40697 6.635348,0.029 8.775402,-0.72941 3.996026,-0.2573 5.920727,-2.26187 6.559363,-5.35139 0.584996,-1.65849 0.784388,-3.47976 -0.204908,-5.80303 -0.723248,-1.2977 -0.231398,-2.54169 -4.671496,-4.00347 -4.681827,-0.43301 -6.163843,-1.42956 -8.096137,-2.51347 -2.779381,-2.5312 -6.236813,-1.97896 -9.811191,-0.89505 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-15 tooth-15-parent"
      d="m 57.473765,80.464991 c -8.431027,1.00936 -12.429637,4.65891 -9.877252,12.21083 2.688393,2.77158 5.132545,5.74701 9.695968,6.95317 1.616986,-0.0283 3.036904,0.10824 4.006631,0.620389 1.399996,0.32137 3.003957,0.31919 4.73703,0.11232 3.263724,-1.454589 7.652073,-0.2444 9.490541,-5.075989 1.517631,-3.86591 1.258553,-7.27018 -2.398877,-9.79138 -7.228529,-5.07305 -11.201614,-4.64639 -15.654041,-5.02934 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-16 tooth-16-parent"
      d="m 40.400929,101.93638 c 8.540214,-6.220469 14.83636,-2.627509 21.132851,0.9639 1.70039,1.7707 3.363687,3.5413 5.692529,5.31326 7.131417,5.75158 5.79007,9.65482 1.660196,12.94987 -2.573952,2.39643 -5.039142,4.74748 -6.337117,6.61203 -1.48762,1.28541 -2.855361,2.27152 -4.017065,2.7435 -5.497444,2.07161 -7.596361,-0.81763 -10.682339,-2.26609 -11.087339,-4.90405 -15.057835,-11.73539 -12.204887,-20.4145 0.31436,-3.34607 2.189645,-4.99871 4.755832,-5.90197 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-17 tooth-17-parent"
      d="m 28.730841,143.70545 c -1.738504,-1.99931 -1.511164,-4.90954 -0.338594,-8.2577 4.474246,-8.60052 12.512518,-10.45413 25.03487,-3.81872 3.92789,1.33064 7.041725,3.88921 9.09019,6.4421 2.015003,2.51132 3.885891,3.72014 2.889861,8.1614 -2.299784,7.48128 -6.272087,13.34988 -17.529844,12.19412 -4.473038,-0.45662 -8.42318,0.5263 -14.080605,-3.19104 -2.190077,-3.04198 -6.410162,-2.83939 -5.065878,-11.53016 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
    class="tooth-18 tooth-18-parent"
      d="m 41.96617,158.28623 c 4.957642,0.3802 9.428351,1.37009 12.439384,3.64608 4.298567,1.86448 7.041035,3.81871 6.814214,5.94445 1.375849,3.24006 0.304958,5.59378 -0.500905,8.0435 l -2.290119,4.0215 c -1.448553,2.34064 -4.442078,3.89867 -9.124602,4.60116 -5.51245,0.76681 -11.025416,1.68656 -16.527257,-0.94524 -6.263892,-1.96088 -6.561951,-4.74265 -7.163588,-7.48272 -1.848724,-2.81074 -3.086495,-6.19523 -2.353337,-11.43077 0.649676,-2.39317 1.475289,-5.43564 5.517882,-6.82619 4.04251,-1.39056 7.66734,-0.66913 13.188328,0.42823 z"
    style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    


    <path
      class="tooth-21 tooth-21-parent"
      d="m 175.14525,31.757761 c -0.0561,0.43475 -3.08166,4.91178 -3.84449,6.93411 -1.0314,2.18329 -2.67373,4.15698 -7.0469,5.19413 -3.57085,-0.36803 -7.1925,-0.4467 -10.19825,-4.03314 l -7.38988,-9.40519 -1.34757,-2.99194 c -0.9731,-2.16026 1.13418,-4.14677 3.10702,-4.99829 l 5.34936,-1.19716 c 3.12437,-0.16804 5.19808,-0.93654 11.30286,0.59906 l 5.72806,1.04815 c 2.08381,0.77109 4.86648,0.46928 4.92055,4.35667 -0.10952,1.48594 0.58404,2.85768 -0.58076,4.4936 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-22 tooth-22-parent"
      d="m 197.61055,35.639201 c -11.50344,-6.33737 -9.14675,-4.48759 -14.07014,-5.89645 -1.71628,-0.51475 -3.14085,-0.59168 -3.86493,0.38286 l -2.6696,2.25197 c -1.81414,1.91334 -1.69341,2.3195 -1.92368,2.99912 -0.8546,5.91623 0.13307,5.84197 0.32351,8.35999 1.31548,2.14319 2.90408,4.05601 5.59188,5.04157 1.65864,0.98198 3.95557,0.88558 6.39568,0.54751 l 4.01223,-0.81994 c 1.57309,0.19854 2.92847,-0.682 4.30771,-1.44456 2.91064,-1.71458 3.66285,-4.14822 4.66364,-6.49912 0.20138,-1.52056 -0.3142,-2.86746 -1.49962,-4.05227 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-23 tooth-23-parent"
      d="m 212.11427,61.313331 c 4.66114,-1.30502 6.84388,-3.69249 7.33927,-6.81702 0.57587,-3.05499 -0.0603,-6.03464 -2.25828,-8.91722 -1.92229,-4.48919 -3.82983,-4.24058 -5.73902,-4.54209 -1.70306,0.18022 -3.25096,0.0983 -4.75842,-0.0522 -4.5566,-0.16945 -6.25405,1.56469 -7.35212,2.69903 -3.84499,4.32077 -3.42041,6.83837 -4.35558,9.93012 -0.48096,3.41382 -0.26881,6.33281 1.80909,7.91996 6.32226,3.96822 7.39694,2.02383 10.04283,1.84969 4.9271,-1.74142 3.65983,-1.4284 5.27223,-2.07051 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-24 tooth-24-parent"
      d="m 224.75169,60.724041 c 7.03697,2.05657 7.49958,4.89405 7.53348,7.78258 0.35791,12.70499 -12.49354,12.48997 -14.98244,11.51326 -3.91582,0.40698 -6.63535,0.029 -8.77541,-0.72941 -3.99602,-0.25729 -5.92072,-2.2619 -6.55937,-5.35139 -0.585,-1.65851 -0.78439,-3.47976 0.20492,-5.80302 0.72324,-1.2977 0.23147,-2.54171 4.67149,-4.00347 4.68183,-0.43303 6.16383,-1.42957 8.09613,-2.51347 2.77947,-2.5312 6.23681,-1.97897 9.8112,-0.89508 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-25 tooth-25-parent"
      d="m 231.56547,80.499151 c 8.43103,1.00936 12.42965,4.65893 9.87724,12.21084 -2.68839,2.77156 -5.13256,5.747 -9.69595,6.95315 -1.617,-0.0283 -3.03691,0.10824 -4.00664,0.620389 -1.39999,0.32136 -3.00395,0.31919 -4.73702,0.11232 -3.26373,-1.454619 -7.65208,-0.24442 -9.49055,-5.075989 -1.51762,-3.86591 -1.25854,-7.27018 2.39888,-9.7914 7.22853,-5.07305 11.20162,-4.64639 15.65404,-5.02933 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-26 tooth-26-parent"
      d="m 248.63831,101.97056 c -8.54022,-6.220499 -14.83636,-2.627539 -21.13285,0.9639 -1.70031,1.77067 -3.36369,3.54129 -5.69255,5.31326 -7.1314,5.75156 -5.79006,9.65479 -1.66018,12.94984 2.57395,2.39645 5.03916,4.74751 6.3371,6.61207 1.48763,1.28539 2.85536,2.27151 4.01709,2.74345 5.49743,2.07163 7.59635,-0.81761 10.68233,-2.26607 11.08734,-4.90404 15.05783,-11.73539 12.20489,-20.41449 -0.31437,-3.34609 -2.18965,-4.99871 -4.75583,-5.90196 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
   <path
      class="tooth-27 tooth-27-parent"
      d="m 260.30839,143.73962 c 1.73849,-1.9993 1.51117,-4.90954 0.3386,-8.25772 -4.47424,-8.60051 -12.51244,-10.45412 -25.03488,-3.81871 -3.92788,1.33064 -7.04171,3.8892 -9.09018,6.44212 -2.01502,2.5113 -3.88589,3.72012 -2.88985,8.1614 2.29978,7.48127 6.27207,13.34987 17.52981,12.19409 4.47305,-0.45661 8.42319,0.52634 14.08064,-3.19106 2.19006,-3.04194 6.41016,-2.83937 5.06586,-11.53012 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
   <path
      class="tooth-28 tooth-28-parent"
      d="m 247.07307,158.32041 c -4.95764,0.38019 -9.42836,1.37006 -12.4394,3.64607 -4.29856,1.86448 -7.04104,3.81869 -6.81422,5.94438 -1.37583,3.24013 -0.30494,5.59385 0.50092,8.04356 l 2.29013,4.0215 c 1.44855,2.34064 4.44207,3.89867 9.12459,4.60115 5.51245,0.76682 11.02542,1.68658 16.52726,-0.94521 6.2639,-1.9609 6.56194,-4.74266 7.16367,-7.48275 1.84863,-2.8107 3.08643,-6.19523 2.35325,-11.43075 -0.64967,-2.39318 -1.47528,-5.43565 -5.51788,-6.82621 -4.04251,-1.39056 -7.66733,-0.66912 -13.18832,0.42826 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    
    
      <path
      class="tooth-31 tooth-31-parent"
      d="m 160.54977,334.14743 c 1.12048,2.06569 1.91435,4.35173 3.95858,5.79383 0.91713,0.77123 1.70668,1.75807 0.6872,5.80105 -1.05812,1.68449 -2.86976,2.66996 -5.71641,2.69552 -2.296,-0.36226 -4.27243,-0.87858 -7.88277,-0.60666 -1.73834,-0.35247 -3.32273,0.12017 -5.43441,-2.23416 -0.69745,-1.11199 -1.78371,-1.82013 -0.0172,-5.4906 l 5.40852,-6.06401 c 1.46736,-0.91714 1.62785,-2.32935 5.04786,-2.50687 2.67434,0.0688 3.17576,1.4203 3.94858,2.6119 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-32 tooth-32-parent"
      d="m 169.15853,329.13705 c 1.41294,-3.31132 4.86314,-3.34925 7.20638,-1.76285 l 3.3567,3.54903 3.95041,3.17338 c 0.57437,1.4853 2.11968,1.39374 0.3184,6.73777 -1.08323,2.89062 -3.26184,4.18223 -5.93305,4.75488 -3.4257,0.82565 -6.35877,0.29492 -8.99938,-1.04092 -1.80009,-1.55629 -4.64941,-2.29071 -3.88444,-5.85637 0.76352,-1.60354 0.79984,-1.97848 2.43778,-5.05922 0.51573,-1.49196 1.03389,-2.22737 1.5472,-4.4957 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-33 tooth-33-parent"
      d="m 195.66982,319.15822 c -4.97497,-0.5866 -9.32935,-0.60585 -10.17763,2.58052 l -2.50841,6.24485 c -1.80328,4.75974 0.0902,3.79635 0.38826,5.30256 l 3.72894,3.67301 c 1.425,1.21054 2.96041,2.35591 6.32711,2.41944 4.93625,-0.15321 9.93081,-0.21879 10.98427,-6.20109 l 0.42423,-6.98897 c -1.02363,-3.33951 -2.14401,-6.63159 -9.16677,-7.03032 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-34 tooth-34-parent"
      d="m 219.94541,307.49978 c 2.89434,3.34934 3.58895,5.752 3.29547,7.72953 -0.86582,3.44067 -1.31395,7.1332 -5.62398,8.49779 -6.87838,1.92323 -8.35892,0.32715 -11.15358,-0.41218 -3.19782,-1.30479 -5.71244,-2.93919 -7.52451,-4.91258 -1.39354,-0.80801 -1.28944,-1.61934 -1.34584,-2.43039 0.21527,-1.50665 -0.17103,-2.52372 -0.30908,-3.7429 -0.21045,-2.3646 1.00583,-4.01506 1.69148,-5.93107 2.02812,-1.38579 3.51403,-3.28884 6.82931,-3.44662 l 5.72445,-0.13735 c 3.52615,-0.15511 5.93142,2.4119 8.41628,4.78577 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-35 tooth-35-parent"
      d="m 218.68832,285.85208 c -7.29884,3.04436 -7.19248,4.68134 -7.41301,7.00432 0.55256,2.78228 0.40498,3.81733 0.38791,5.17785 -0.0987,2.86 2.02408,3.61177 3.36163,5.10879 4.28916,3.60151 6.57409,2.43755 9.6004,3.03624 2.78292,-0.35969 4.47873,-0.71702 6.09323,-1.07413 4.53831,-0.92446 4.14168,-2.35554 5.86233,-3.5693 2.20613,-2.48431 2.39999,-5.6587 1.23494,-9.29928 -0.95543,-2.16577 -2.89446,-3.78548 -4.92369,-5.35502 l -4.9879,-2.11041 c -3.82923,-1.32869 -8.21619,0.49536 -9.21584,1.08094 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-36 tooth-36-parent"
      d="m 231.8963,253.69055 c -1.73624,-0.45309 -3.64477,0.42587 -5.57161,1.44731 l -2.59439,2.50145 c -1.82794,1.45981 -2.59429,2.91733 -2.43984,4.37288 0.1661,2.53691 -0.63862,5.076 -2.65278,7.61777 -1.28839,3.1189 -2.12961,6.13255 0.10067,8.42289 1.36196,2.0793 3.01732,4.05941 6.33668,5.47669 3.4707,1.20137 6.53054,2.83605 10.71641,3.28325 2.73599,0.17769 4.95797,0.93364 9.36546,-0.76927 l 6.01172,-3.38242 c 1.01216,-0.62186 1.9762,-0.96998 3.55368,-4.81212 0.5161,-3.56981 2.49737,-6.9892 0.11489,-10.85668 l -3.81476,-7.35397 c -1.80371,-1.89024 -3.00101,-3.96699 -6.85638,-5.22598 -2.24707,-0.60845 -3.75557,-1.55716 -8.03343,-1.23024 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-37 tooth-37-parent"
      d="m 249.6427,222.51738 c 2.77196,1.08131 5.60145,1.76836 8.19109,4.09996 2.14446,1.41719 3.73999,3.9688 4.11738,9.03794 0.18,3.56427 1.20938,7.0376 -1.60156,10.92233 -2.41345,1.70733 -0.93497,2.28843 -7.49304,5.19512 -3.37842,1.18776 -5.80361,3.80654 -10.99644,2.27029 -2.5121,-0.59702 -3.36048,-0.36138 -7.66372,-1.85492 -1.82316,-0.34856 -3.3334,-1.22592 -4.17359,-3.23528 -0.90486,-1.30016 -1.66098,-3.10521 -2.17395,-5.73544 0.1761,-2.2723 0.58422,-3.20718 0.95283,-4.36961 0.58948,-3.05528 0.36843,-4.32807 -0.0165,-5.24095 -0.0396,-1.539 0.32565,-3.0788 1.47253,-4.62034 0.60241,-1.66602 0.99898,-3.24142 4.51943,-5.06378 3.20569,-2.02233 5.28616,-3.54323 14.86535,-1.40532 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-38 tooth-38-parent"
      d="m 260.02613,214.38342 c 0.46121,-2.70997 2.52529,-2.96924 0.86432,-8.9241 -0.86527,-4.24874 -3.34697,-7.75805 -8.95254,-9.8385 -3.39318,-0.40535 -6.4858,-2.43565 -10.40869,0.0232 -1.14334,0.83863 -1.99526,1.28569 -2.74582,1.62823 -1.55929,1.36243 -4.59026,1.44291 -6.07587,6.62705 0,1.97558 0.57541,2.74027 -0.49868,6.98911 -0.34041,1.63729 -1.33111,2.66403 -0.20527,5.67825 1.21319,1.39411 1.10358,2.87043 4.84545,4.10733 1.02689,0.79598 9.18618,1.54712 11.82602,1.47148 2.63974,-0.0757 2.5084,0.68084 6.53478,-2.51012 1.59688,-1.69166 3.04717,-2.37808 4.8163,-5.25161 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-41 tooth-41-parent"
      d="m 128.48946,334.11326 c -1.12046,2.06568 -1.91426,4.35175 -3.95858,5.79383 -0.91711,0.77124 -1.70668,1.75809 -0.68719,5.80108 1.05812,1.68447 2.86975,2.66995 5.7164,2.69548 2.296,-0.36222 4.27245,-0.87856 7.88278,-0.60663 1.73833,-0.35249 3.32274,0.12017 5.43442,-2.23419 0.69744,-1.11199 1.78368,-1.82009 0.0172,-5.49057 l -5.40851,-6.06402 c -1.46736,-0.91714 -1.62785,-2.32934 -5.04785,-2.50688 -2.67435,0.0688 -3.17577,1.42031 -3.9486,2.6119 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-42 tooth-42-parent"
      d="m 119.88069,329.10289 c -1.41293,-3.31134 -4.86313,-3.34927 -7.20636,-1.76285 l -3.3567,3.54902 -3.9504,3.17338 c -0.57438,1.48532 -2.1197,1.39375 -0.31842,6.73777 1.08323,2.89062 3.26184,4.18226 5.93307,4.75488 3.42568,0.82568 6.35876,0.29492 8.99938,-1.0409 1.80007,-1.55631 4.64941,-2.29071 3.88442,-5.85637 -0.76352,-1.60357 -0.79984,-1.97848 -2.43777,-5.05922 -0.51574,-1.49195 -1.03389,-2.22737 -1.54722,-4.49571 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-43 tooth-43-parent"
      d="m 93.369417,319.12408 c 4.974972,-0.58661 9.329343,-0.60586 10.177633,2.58048 l 2.50842,6.24488 c 1.80327,4.75973 -0.0901,3.79632 -0.38828,5.30255 l -3.72892,3.673 c -1.42502,1.21056 -2.960415,2.35592 -6.327124,2.41947 -4.936255,-0.15322 -9.930806,-0.21881 -10.984278,-6.20112 l -0.424223,-6.98897 c 1.023625,-3.33948 2.144013,-6.63158 9.166772,-7.03029 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-44 tooth-44-parent"
      d="m 69.09383,307.46563 c -2.89434,3.34933 -3.588951,5.752 -3.295461,7.72953 0.865799,3.44067 1.31392,7.1332 5.623962,8.49777 6.878388,1.92325 8.358928,0.32715 11.153582,-0.41217 3.197831,-1.3048 5.712444,-2.93918 7.524526,-4.91259 1.393515,-0.80799 1.289422,-1.61932 1.345827,-2.43038 -0.215274,-1.50663 0.171026,-2.52374 0.309092,-3.74292 0.210435,-2.36457 -1.005771,-4.01505 -1.691493,-5.93104 -2.028117,-1.3858 -3.514021,-3.28884 -6.829308,-3.44662 l -5.724439,-0.13737 c -3.526171,-0.15509 -5.931423,2.41194 -8.416288,4.78579 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-45 tooth-45-parent"
      d="m 70.35092,285.81793 c 7.298827,3.04433 7.192491,4.68134 7.413014,7.00432 -0.552564,2.78227 -0.405004,3.81731 -0.387926,5.17784 0.09866,2.86007 -2.024062,3.61177 -3.361614,5.10879 -4.289083,3.60149 -6.574116,2.43754 -9.600409,3.03625 -2.78292,-0.35972 -4.478735,-0.71703 -6.093218,-1.07413 -4.538325,-0.92446 -4.14169,-2.35554 -5.862341,-3.5693 -2.20612,-2.4843 -2.399994,-5.65872 -1.234926,-9.2993 0.955408,-2.16576 2.89443,-3.78547 4.923662,-5.35501 l 4.987913,-2.1104 c 3.829233,-1.32867 8.216194,0.49533 9.215845,1.08094 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-46 tooth-46-parent"
      d="m 57.142934,253.65637 c 1.73626,-0.45306 3.644753,0.42587 5.571612,1.44732 l 2.594385,2.50144 c 1.827944,1.45984 2.594303,2.91743 2.439847,4.3729 -0.166108,2.53691 0.638632,5.07601 2.65277,7.61778 1.288396,3.11889 2.129618,6.13255 -0.100645,8.42287 -1.361969,2.0793 -3.017332,4.05943 -6.336686,5.4767 -3.470711,1.20137 -6.530559,2.83604 -10.716404,3.28325 -2.736,0.17769 -4.957988,0.93365 -9.365481,-0.76927 l -6.011715,-3.38242 c -1.012157,-0.62185 -1.976191,-0.96997 -3.553678,-4.81213 -0.516083,-3.56978 -2.497364,-6.9892 -0.114875,-10.85668 l 3.81474,-7.35397 c 1.803703,-1.89024 3.001028,-3.96698 6.856386,-5.22596 2.247086,-0.60847 3.755576,-1.55718 8.033445,-1.23024 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-47 tooth-47-parent"
      d="m 39.396534,222.48322 c -2.771965,1.08129 -5.601454,1.76836 -8.191098,4.09996 -2.144369,1.41718 -3.739966,3.9688 -4.117368,9.03793 -0.179992,3.5643 -1.209399,7.03762 1.601548,10.92232 2.413449,1.70734 0.93497,2.28845 7.493041,5.19514 3.378429,1.18776 5.803607,3.80655 10.996439,2.27029 2.512107,-0.59703 3.36049,-0.36137 7.663715,-1.85493 1.823194,-0.34855 3.33341,-1.2259 4.173599,-3.23526 0.904871,-1.30017 1.660969,-3.10523 2.173948,-5.73544 -0.17611,-2.27231 -0.584216,-3.20719 -0.952822,-4.36964 -0.589477,-3.05526 -0.368433,-4.32807 0.01639,-5.24094 0.03967,-1.53898 -0.325572,-3.07879 -1.472528,-4.62033 -0.602413,-1.66601 -0.998962,-3.24142 -4.519438,-5.06377 -3.205686,-2.02234 -5.286144,-3.54323 -14.865336,-1.40533 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    <path
      class="tooth-48 tooth-48-parent"
      d="m 29.013116,214.34926 c -0.461232,-2.70998 -2.525307,-2.96923 -0.864336,-8.9241 0.865286,-4.24874 3.346954,-7.75805 8.952544,-9.83848 3.39318,-0.40537 6.485799,-2.43566 10.408687,0.0232 1.143334,0.83862 1.995253,1.28569 2.745832,1.62824 1.55929,1.36239 4.590243,1.44287 6.075881,6.62702 0,1.97557 -0.575419,2.74031 0.49866,6.98912 0.340405,1.63729 1.331089,2.66403 0.205262,5.67825 -1.213193,1.39411 -1.103576,2.87044 -4.845438,4.10736 -1.026905,0.79597 -9.186179,1.54709 -11.82602,1.47145 -2.639752,-0.0757 -2.508402,0.68083 -6.534782,-2.51014 -1.596893,-1.69163 -3.047169,-2.37805 -4.81629,-5.2516 z"
      style="fill:#76f5f1;stroke:#000000;stroke-width:2;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none"/>
    
    </text>
    <text xml:space="preserve" x="120.37785" y="17.284296" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="120.37785" y="17.284296">11</tspan>
    </text>
    <text xml:space="preserve" x="85.630409" y="28.142862" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="85.630409" y="28.142862">12</tspan>
    </text>
    <text xml:space="preserve" x="56.67421" y="43.344887" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="56.67421" y="43.344887">13</tspan>
    </text>
    <text xml:space="preserve" x="42.92001" y="64.338097" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="42.92001" y="64.338097">14</tspan>
    </text>
    <text xml:space="preserve" x="32.785339" y="88.226982" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="32.785339" y="88.226982">15</tspan>
    </text>
    <text xml:space="preserve" x="19.031139" y="110.66804" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="19.031139" y="110.66804">16</tspan>
    </text>
    <text xml:space="preserve" x="12.515995" y="141.07207" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="12.515995" y="141.07207">17</tspan>
    </text>
    <text xml:space="preserve" x="8.8964691" y="172.2" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="8.8964691" y="172.2">18</tspan>
    </text>
    <text xml:space="preserve" x="155.84921" y="17.284296" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="155.84921" y="17.284296">21</tspan>
    </text>
    <text xml:space="preserve" x="192.76837" y="29.590677" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="192.76837" y="29.590677">22</tspan>
    </text>
    <text xml:space="preserve" x="220.27673" y="44.068779" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="220.27673" y="44.068779">23</tspan>
    </text>
    <text xml:space="preserve" x="236.20268" y="64.338097" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="236.20268" y="64.338097">24</tspan>
    </text>
    <text xml:space="preserve" x="247.06125" y="86.779167" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="247.06125" y="86.779167">25</tspan>
    </text>
    <text xml:space="preserve" x="258.64374" y="109.22027" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="258.64374" y="109.22027">26</tspan>
    </text>
    <text xml:space="preserve" x="264.43497" y="139.62425" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="264.43497" y="139.62425">27</tspan>
    </text>
    <text xml:space="preserve" x="268.77841" y="169.3044" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="268.77841" y="169.3044">28</tspan>
    </text>
    <text xml:space="preserve" x="151.50578" y="360.41531" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="151.50578" y="360.41531">31</tspan>
    </text>
    <text xml:space="preserve" x="176.84245" y="355.34799" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="176.84245" y="355.34799">32</tspan>
    </text>
    <text xml:space="preserve" x="202.17912" y="346.6611" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="202.17912" y="346.6611">33</tspan>
    </text>
    <text xml:space="preserve" x="222.44849" y="330.01129" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="222.44849" y="330.01129">34</tspan>
    </text>
    <text xml:space="preserve" x="240.5461" y="309.01804" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="240.5461" y="309.01804">35</tspan>
    </text>
    <text xml:space="preserve" x="257.91986" y="282.95749" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="257.91986" y="282.95749">36</tspan>
    </text>
    <text xml:space="preserve" x="265.15891" y="245.31441" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="265.15891" y="245.31441">37</tspan>
    </text>
    <text xml:space="preserve" x="265.15891" y="214.18651" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="265.15891" y="214.18651">38</tspan>
    </text>
    <text xml:space="preserve" x="128.34082" y="360.41531" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="128.34082" y="360.41531">41</tspan>
    </text>
    <text xml:space="preserve" x="100.10851" y="356.79581" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="100.10851" y="356.79581">42</tspan>
    </text>
    <text xml:space="preserve" x="76.94355" y="347.38501" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="76.94355" y="347.38501">43</tspan>
    </text>
    <text xml:space="preserve" x="55.950298" y="330.01129" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="55.950298" y="330.01129">44</tspan>
    </text>
    <text xml:space="preserve" x="38.57658" y="310.46585" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="38.57658" y="310.46585">45</tspan>
    </text>
    <text xml:space="preserve" x="20.478951" y="287.30087" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="20.478951" y="287.30087">46</tspan>
    </text>
    <text xml:space="preserve" x="12.515995" y="244.5905" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="12.515995" y="244.5905">47</tspan>
    </text>
    <text xml:space="preserve" x="12.515995" y="214.18651" style="font-size:10.13467216px;font-style:normal;font-weight:normal;line-height:125%;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none;font-family:Sans">
      <tspan x="12.515995" y="214.18651">48</tspan>
    </text>

  </svg>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      var selectedTeeth = [];

      // Load saved selections from localStorage
      if (localStorage.getItem('selectedTeeth')) {
        selectedTeeth = localStorage.getItem('selectedTeeth').split(',');
        selectedTeeth.forEach(function(teethId) {
          $(`.tooth-${teethId}-parent`).css('fill', 'red');
          $(`.tooth-${teethId}-status`).text('Clicked');
        });
      }

      // Function to handle tooth selection and deselection
      function selectTooth(teethId, isSelected) {
        var index = selectedTeeth.indexOf(teethId.toString());
        if (isSelected) {
          if (index === -1) selectedTeeth.push(teethId.toString());
        } else {
          if (index > -1) selectedTeeth.splice(index, 1);
        }
        document.getElementById('selectedTeeth').value = selectedTeeth.join(',');
        localStorage.setItem('selectedTeeth', selectedTeeth.join(','));
        console.log("Selected teeth:", selectedTeeth);
        console.log("Hidden input value:", document.getElementById('selectedTeeth').value);
      }

      // Function to handle tooth click and double click
      function handleTooth(i) {
        $(`.tooth-${i}`).click(function() {
          $(`.tooth-${i}-parent`).css('fill', 'red');
          $(`.tooth-${i}-status`).text('Clicked');
          selectTooth(i, true); // Call selectTooth function here with true (selected)
        });

        $(`.tooth-${i}`).dblclick(function() {
          $(`.tooth-${i}-parent`).css('fill', '#76f5f1');
          $(`.tooth-${i}-status`).text('Normal');
          selectTooth(i, false); // Call selectTooth function here with false (deselected)
        });
      }

      // Teeth ranges for upper and lower teeth
      var teethRanges = {
        upper: [{ start: 11, end: 18 }, { start: 21, end: 28 }],
        lower: [{ start: 31, end: 38 }, { start: 41, end: 48 }]
      };

      // Initialize all teeth
      Object.values(teethRanges).flat().forEach(function(range) {
        for (let i = range.start; i <= range.end; i++) {
          handleTooth(i);
        }
      });

      // Function to select all teeth in a range
      function selectAllTeeth(ranges) {
        ranges.forEach(function(range) {
          for (let i = range.start; i <= range.end; i++) {
            $(`.tooth-${i}-parent`).css('fill', 'red');
            $(`.tooth-${i}-status`).text('Clicked');
            selectTooth(i, true);
          }
        });
      }

      // Function to clear all selections
      function clearAllTeeth() {
        selectedTeeth.forEach(function(teethId) {
          $(`.tooth-${teethId}-parent`).css('fill', '#76f5f1');
          $(`.tooth-${teethId}-status`).text('Normal');
        });
        selectedTeeth = [];
        document.getElementById('selectedTeeth').value = '';
        localStorage.setItem('selectedTeeth', '');
      }

      // Event listeners for select/deselect buttons
      document.getElementById('selectAllUpper').addEventListener('click', function() {
        selectAllTeeth(teethRanges.upper);
      });

      document.getElementById('selectAllLower').addEventListener('click', function() {
        selectAllTeeth(teethRanges.lower);
      });

      document.getElementById('clearAll').addEventListener('click', function() {
        clearAllTeeth();
      });
    });
  </script>
    </body>
    </html>
    {% endblock %}
    