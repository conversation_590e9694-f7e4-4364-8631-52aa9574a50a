{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <div class="card">
        <div class="card-header">
            <h4><PERSON><PERSON><PERSON>st të Ri</h4>
        </div>
        <div class="card-body">
            <form method="post" id="caseForm">
                {% csrf_token %}
                
                <div class="row">
                    <!-- Informacioni bazë -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label>Dentisti</label>
                            {{ form.dentist }}
                        </div>
                        <div class="mb-3">
                            <label>Pacienti</label>
                            {{ form.patient }}
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label>Departamenti</label>
                            {{ form.responsible_department }}
                        </div>
                        <div class="mb-3">
                            <label>Prioriteti</label>
                            {{ form.priority }}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label>Data e Pranimit</label>
                            {{ form.received_date_time }}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label>Afati</label>
                            {{ form.deadline }}
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label>Shënime</label>
                    {{ form.notes }}
                </div>

                <!-- Artikujt -->
                <h5 class="mt-4">Artikujt</h5>
                <div id="items-formset">
                    {{ formset.management_form }}
                    <div class="items-container">
                        {% for item_form in formset %}
                        <div class="item-form mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <label>Artikulli</label>
                                    {{ item_form.item }}
                                </div>
                                <div class="col-md-3">
                                    <label>Sasia</label>
                                    {{ item_form.quantity }}
                                </div>
                                <div class="col-md-3">
                                    <label>Njësia</label>
                                    {{ item_form.unit }}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <button type="button" class="btn btn-outline-primary" id="add-item">
                        Shto Artikull
                    </button>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">Ruaj Rastin</button>
                    <a href="{% url 'case:case_list' %}" class="btn btn-secondary">Anulo</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize select2 for dropdown fields
    $('.form-select').select2();

    // Add item functionality
    $('#add-item').click(function() {
        var form_idx = $('#id_caseitem_set-TOTAL_FORMS').val();
        var newForm = $('.item-form:first').clone();
        newForm.find('input, select').each(function() {
            $(this).val('').attr('name', $(this).attr('name').replace('-0-', '-' + form_idx + '-'));
            $(this).attr('id', $(this).attr('id').replace('-0-', '-' + form_idx + '-'));
        });
        $('.items-container').append(newForm);
        $('#id_caseitem_set-TOTAL_FORMS').val(parseInt(form_idx) + 1);
        newForm.find('.form-select').select2();
    });
});
</script>
{% endblock %}