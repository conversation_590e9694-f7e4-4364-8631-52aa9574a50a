{% extends 'base.html' %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Header Section with Stats -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="bi bi-list-task"></i> Tasks Management</h2>
                <a href="{% url 'case:task_create_general' %}" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> Create New Task
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">Pending Tasks</h5>
                    <h2>{{ pending_count }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <h5 class="card-title">In Progress</h5>
                    <h2>{{ in_progress_count }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">Completed</h5>
                    <h2>{{ completed_count }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <h5 class="card-title">Delayed</h5>
                    <h2>{{ delayed_count }}</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" name="search" class="form-control" 
                               placeholder="Search tasks..." value="{{ search_query }}">
                    </div>
                </div>
                <div class="col-md-2">
                    <select name="status" class="form-select">
                        <option value="">All Statuses</option>
                        {% for status_value, status_label in status_choices %}
                        <option value="{{ status_value }}" {% if status_filter == status_value %}selected{% endif %}>
                            {{ status_label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="priority" class="form-select">
                        <option value="">All Priorities</option>
                        {% for priority_value, priority_label in priority_choices %}
                        <option value="{{ priority_value }}" {% if priority_filter == priority_value %}selected{% endif %}>
                            {{ priority_label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <div class="input-group">
                        <span class="input-group-text">Due Date</span>
                        <input type="date" name="due_date" class="form-control" 
                               value="{{ due_date_filter }}">
                    </div>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-filter"></i> Apply Filters
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Tasks Table -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th>Case #</th>
                            <th>Title</th>
                            <th>Stage</th>
                            <th>Assigned To</th>
                            <th>Timeline</th>
                            <th>Priority</th>
                            <th>Status</th>
                            <th>Progress</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for task in tasks %}
                        <tr>
                            <td>
                                <a href="{% url 'case:case_detail' task.case.case_number %}" 
                                   class="text-decoration-none">
                                    #{{ task.case.case_number }}
                                </a>
                            </td>
                            <td>
                                <div class="d-flex flex-column">
                                    <strong>{{ task.title }}</strong>
                                    <small class="text-muted">{{ task.description|truncatechars:50 }}</small>
                                </div>
                            </td>
                            <td>{{ task.workflow_stage.name }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-person-circle me-2"></i>
                                    {{ task.assigned_to.email }}
                                </div>
                            </td>
                            <td>
                                <small class="text-muted d-block">
                                    <i class="bi bi-calendar-event"></i>
                                    Start: {{ task.scheduled_start_time|date:"d/m/Y H:i" }}
                                </small>
                                <small class="text-muted d-block">
                                    <i class="bi bi-calendar-check"></i>
                                    End: {{ task.scheduled_end_time|date:"d/m/Y H:i" }}
                                </small>
                            </td>
                            <td>
                                <span class="badge rounded-pill 
                                    {% if task.priority == 4 %}bg-danger
                                    {% elif task.priority == 3 %}bg-warning
                                    {% elif task.priority == 2 %}bg-info
                                    {% else %}bg-secondary{% endif %}">
                                    {{ task.get_priority_display }}
                                </span>
                            </td>
                            <td>
                                <span class="badge rounded-pill 
                                    {% if task.status == 'completed' %}bg-success
                                    {% elif task.status == 'in_progress' %}bg-warning
                                    {% elif task.status == 'delayed' %}bg-danger
                                    {% else %}bg-primary{% endif %}">
                                    {{ task.get_status_display }}
                                </span>
                            </td>
                            <td>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar 
                                        {% if task.status == 'delayed' %}bg-danger
                                        {% elif task.status == 'completed' %}bg-success
                                        {% else %}bg-primary{% endif %}" 
                                         role="progressbar" 
                                         style="width: {{ task.get_progress_percentage }}%"
                                         aria-valuenow="{{ task.get_progress_percentage }}" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100">
                                        {{ task.get_progress_percentage }}%
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{% url 'case:task_update' task.id %}" 
                                       class="btn btn-sm btn-outline-primary"
                                       title="Edit Task">
                                        <i class="bi bi-pencil">edit</i>
                                    </a>
                                    <a href="{% url 'case:task_detail' task.id %}" 
                                       class="btn btn-sm btn-outline-info"
                                       title="View Details">
                                        <i class="bi bi-eye">view</i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-danger" 
                                            onclick="confirmDelete('{% url 'case:task_delete' task.id %}')"
                                            title="Delete Task">
                                        <i class="bi bi-trash">delete</i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="9" class="text-center py-5">
                                <div class="text-muted">
                                    <i class="bi bi-inbox display-4 d-block mb-3"></i>
                                    <h5>No tasks found</h5>
                                    <p>Try adjusting your search or filter criteria</p>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if tasks.has_other_pages %}
            <nav aria-label="Task navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if tasks.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1&search={{ search_query }}&status={{ status_filter }}&priority={{ priority_filter }}&due_date={{ due_date_filter }}">
                            <i class="bi bi-chevron-double-left"></i> First
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ tasks.previous_page_number }}&search={{ search_query }}&status={{ status_filter }}&priority={{ priority_filter }}&due_date={{ due_date_filter }}">
                            Previous
                        </a>
                    </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">
                            Page {{ tasks.number }} of {{ tasks.paginator.num_pages }}
                        </span>
                    </li>

                    {% if tasks.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ tasks.next_page_number }}&search={{ search_query }}&status={{ status_filter }}&priority={{ priority_filter }}&due_date={{ due_date_filter }}">
                            Next
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ tasks.paginator.num_pages }}&search={{ search_query }}&status={{ status_filter }}&priority={{ priority_filter }}&due_date={{ due_date_filter }}">
                            Last <i class="bi bi-chevron-double-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle-fill text-danger"></i>
                    Confirm Delete
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this task? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> Cancel
                </button>
                <a href="" class="btn btn-danger" id="deleteConfirm">
                    <i class="bi bi-trash"></i> Delete
                </a>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(url) {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    document.getElementById('deleteConfirm').href = url;
    modal.show();
}
</script>
{% endblock %}