{% extends "base.html" %}
{% load static %}

{% block title %}Production Timeline - Dental Lab{% endblock %}

{% block extra_head %}
<link href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
{% endblock %}

{% block extra_css %}
<style>
/* ===================== */
/* ===== VARIABLES ===== */
/* ===================== */
:root {
    /* Brand Colors */
    --primary: #4f46e5;
    --primary-hover: #3730a3;
    --secondary: #64748b;

    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --info: #3b82f6;

    /* Background Colors */
    --bg-white: #ffffff;
    --bg-gray-50: #f9fafb;
    --bg-gray-100: #f3f4f6;
    --bg-gray-200: #e5e7eb;

    /* Text Colors */
    --text-dark: #1f2937;
    --text-medium: #4b5563;
    --text-light: #6b7280;

    /* Border Colors */
    --border-light: #e5e7eb;
    --border-medium: #d1d5db;

    /* Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
}

/* ===================== */
/* ===== BASE STYLES ===== */
/* ===================== */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    background-color: var(--bg-gray-50);
    color: var(--text-dark);
}

.dashboard-container {
    min-height: 100vh;
    padding: var(--spacing-8);
}

/* ===================== */
/* ===== CARDS ===== */
/* ===================== */
.dashboard-card {
    background-color: var(--bg-white);
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    padding: var(--spacing-6);
    margin-bottom: var(--spacing-6);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dashboard-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

/* ===================== */
/* ===== STATISTICS ===== */
/* ===================== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.stat-card {
    background: var(--bg-white);
    border-radius: 12px;
    padding: var(--spacing-6);
    text-align: center;
    border-bottom: 4px solid var(--primary);
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--bg-gray-50);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem auto;
    color: var(--primary);
    font-size: 1.5rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-medium);
    font-size: 0.875rem;
    font-weight: 500;
}

/* ===================== */
/* ===== FILTERS ===== */
/* ===================== */
.filters-section {
    background: var(--bg-white);
    border-radius: 12px;
    padding: var(--spacing-6);
    box-shadow: var(--shadow-md);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-medium);
}

.filter-input {
    padding: 0.625rem 1rem;
    border: 1px solid var(--border-light);
    border-radius: 8px;
    font-size: 0.875rem;
    color: var(--text-dark);
    background: var(--bg-white);
    transition: all 0.2s ease;
}

.filter-input:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    outline: none;
}

.filter-input:hover {
    border-color: var(--border-medium);
}

/* ===================== */
/* ===== BUTTONS ===== */
/* ===================== */
.btn {
    padding: 0.625rem 1.25rem;
    border-radius: 8px;
    font-weight: 500;
    font-size: 0.875rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    cursor: pointer;
    border: none;
}

.btn-primary {
    background: var(--primary);
    color: var(--bg-white);
}

.btn-primary:hover {
    background: var(--primary-hover);
}

.btn-secondary {
    background: var(--bg-white);
    border: 1px solid var(--border-medium);
    color: var(--text-medium);
}

.btn-secondary:hover {
    background: var(--bg-gray-50);
    color: var(--text-dark);
}

/* ===================== */
/* ===== TABLE ===== */
/* ===================== */
.table-wrapper {
    background: var(--bg-white);
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    overflow: hidden;
    margin: 1.5rem 0;
}

.cases-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    white-space: nowrap;
}

.cases-table thead {
    background: var(--bg-gray-50);
}

.cases-table th {
    padding: 1rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-medium);
    text-align: left;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 2px solid var(--border-light);
}

.cases-table td {
    padding: 1rem 1.5rem;
    font-size: 0.875rem;
    color: var(--text-dark);
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
}

.cases-table tbody tr {
    transition: background 0.2s ease;
}

.cases-table tbody tr:hover {
    background: var(--bg-gray-50);
}

/* Status Badges */
.status-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: inline-flex;
    align-items: center;
}

.status-badge i {
    margin-right: 0.375rem;
}

.status-on_hold {
    background: #fff7ed;
    color: #9a3412;
    border: 1px solid #fdba74;
}

.status-in_progress {
    background: #eff6ff;
    color: #1e40af;
    border: 1px solid #93c5fd;
}

.status-completed {
    background: #ecfdf5;
    color: #065f46;
    border: 1px solid #6ee7b7;
}

.status-cancelled {
    background: #fef2f2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

/* ===================== */
/* ===== GANTT CHART ===== */
/* ===================== */
.gantt-container {
    background: var(--bg-white);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow-md);
    margin: 1.5rem 0;
}

.gantt-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-light);
}

.gantt-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
}

.gantt-legend {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-medium);
}

.legend-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--primary);
}

/* Mermaid Gantt Specific Styles */
.mermaid {
    font-family: inherit;
}

.mermaid .grid .tick {
    stroke: var(--border-light);
}

.mermaid .today {
    stroke: var(--primary);
    stroke-width: 2px;
}

/* Gantt Task Colors */
.task-pending rect { fill: #fcd34d !important; }
.task-progress rect { fill: #60a5fa !important; }
.task-completed rect { fill: #34d399 !important; }
.task-cancelled rect { fill: #f87171 !important; }

/* ===================== */
/* ===== LOADING OVERLAY ===== */
/* ===================== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
    backdrop-filter: blur(4px);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--bg-gray-100);
    border-radius: 50%;
    border-top-color: var(--primary);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* ===================== */
/* ===== RESPONSIVE ===== */
/* ===================== */
@media (max-width: 1024px) {
    .dashboard-container {
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .filters-section {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .filters-section {
        grid-template-columns: 1fr;
    }

    .gantt-legend {
        flex-wrap: wrap;
    }

    .gantt-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-4);
    }
}
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Header -->
    <div class="dashboard-card flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-dark mb-2">Production Timeline</h1>
            <p class="text-sm text-gray-600">Track and manage dental lab cases</p>
        </div>
        <div class="flex space-x-4">
            <button id="exportPDF" class="action-button btn-primary">
                <i class="fas fa-file-pdf mr-2"></i>Export PDF
            </button>
            <button id="refreshData" class="action-button btn-primary">
                <i class="fas fa-sync-alt mr-2"></i>Refresh
            </button>
        </div>
    </div>

    <!-- Statistics Grid -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="icon-wrapper mb-3">
                <i class="fas fa-clipboard-list text-3xl text-primary"></i>
            </div>
            <div class="stat-value">{{ analytics.total_cases }}</div>
            <div class="stat-label">Total Cases</div>
            <div class="trend mt-2 text-sm">
                <i class="fas fa-arrow-up text-success"></i>
                <span class="text-success">12%</span> vs last month
            </div>
        </div>

        <div class="stat-card">
            <div class="icon-wrapper mb-3">
                <i class="fas fa-clock text-3xl text-warning"></i>
            </div>
            <div class="stat-value">{{ analytics.in_progress }}</div>
            <div class="stat-label">In Progress</div>
            <div class="trend mt-2 text-sm">
                <span class="text-gray-600">Active cases</span>
            </div>
        </div>

        <div class="stat-card">
            <div class="icon-wrapper mb-3">
                <i class="fas fa-check-circle text-3xl text-success"></i>
            </div>
            <div class="stat-value">{{ analytics.completed_cases }}</div>
            <div class="stat-label">Completed</div>
            <div class="trend mt-2 text-sm">
                <i class="fas fa-arrow-up text-success"></i>
                <span class="text-success">8%</span> completion rate
            </div>
        </div>

        <div class="stat-card">
            <div class="icon-wrapper mb-3">
                <i class="fas fa-exclamation-triangle text-3xl text-danger"></i>
            </div>
            <div class="stat-value">{{ analytics.delayed_cases }}</div>
            <div class="stat-label">Delayed Cases</div>
            <div class="trend mt-2 text-sm">
                <span class="text-danger">Requires attention</span>
            </div>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div class="dashboard-card mb-6">
        <form id="filterForm" class="filters-section">
            <div class="filter-group">
                <label class="filter-label">Date Range</label>
                <input type="text"
                       id="start_date"
                       name="start_date"
                       class="filter-input flatpickr"
                       value="{{ start_date|date:'Y-m-d' }}"
                       placeholder="Select date range">
            </div>

            <div class="filter-group">
                <label class="filter-label">Department</label>
                <select name="department" class="filter-input">
                    <option value="">All Departments</option>
                    {% for dept in departments %}
                    <option value="{{ dept.id }}" {% if selected_department == dept.id %}selected{% endif %}>
                        {{ dept.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Status</label>
                <select name="status" class="filter-input">
                    <option value="">All Statuses</option>
                    {% for status_code, status_name in case_statuses %}
                    <option value="{{ status_code }}" {% if selected_status == status_code %}selected{% endif %}>
                        {{ status_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Priority</label>
                <select name="priority" class="filter-input">
                    <option value="">All Priorities</option>
                    {% for priority_code, priority_name in case_priorities %}
                    <option value="{{ priority_code }}" {% if selected_priority == priority_code %}selected{% endif %}>
                        {{ priority_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Cases per Page</label>
                <select name="page_size" class="filter-input">
                    <option value="25" {% if page_size == 25 %}selected{% endif %}>25 cases</option>
                    <option value="50" {% if page_size == 50 %}selected{% endif %}>50 cases</option>
                    <option value="100" {% if page_size == 100 %}selected{% endif %}>100 cases</option>
                </select>
            </div>

            <div class="filter-group flex items-end">
                <button type="submit" class="action-button btn-primary w-full">
                    <i class="fas fa-search mr-2"></i>Apply Filters
                </button>
            </div>
        </form>
    </div>

    <!-- Gantt Chart -->
    <div class="gantt-container">
        <div class="gantt-header">
            <h2 class="text-xl font-semibold">Production Timeline</h2>
            <div class="gantt-controls">
                <div class="gantt-view-mode">
                    <button class="view-mode-button active" data-view="Day">Day</button>
                    <button class="view-mode-button" data-view="Week">Week</button>
                    <button class="view-mode-button" data-view="Month">Month</button>
                </div>
                <button id="gantt-today" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-calendar-day mr-1"></i> Today
                </button>
                <button id="gantt-zoom-in" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-search-plus"></i>
                </button>
                <button id="gantt-zoom-out" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-search-minus"></i>
                </button>
            </div>
        </div>

        {% if cases %}
        <div id="gantt-chart" style="height: 500px; width: 100%;"></div>

        <div class="gantt-legend">
            <div class="legend-item">
                <span class="legend-color" style="background-color: var(--primary);"></span>
                <span>In Progress</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: var(--success);"></span>
                <span>Completed</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: var(--warning);"></span>
                <span>Pending Acceptance</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: var(--secondary);"></span>
                <span>On Hold</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: var(--danger);"></span>
                <span>Cancelled</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: var(--info);"></span>
                <span>Ready to Ship</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: #20c997;"></span>
                <span>Delivered</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: #6f42c1;"></span>
                <span>Stage</span>
            </div>
        </div>
        {% else %}
        <div class="gantt-empty-state">
            <i class="fas fa-calendar-alt"></i>
            <h3>No cases found</h3>
            <p>There are no cases matching your filter criteria. Try adjusting your filters or adding new cases.</p>
            <a href="{% url 'case:case_create' %}" class="btn btn-primary">
                <i class="fas fa-plus mr-1"></i> Create New Case
            </a>
        </div>
        {% endif %}
    </div>

    <!-- Cases Table -->
    <div class="dashboard-card">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold">Case Details</h2>
            <div class="text-sm text-gray-600">
                Showing {{ cases.count }} cases
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="cases-table">
                <thead>
                    <tr>
                        <th>Case #</th>
                        <th>Patient</th>
                        <th>Received Date</th>
                        <th>Deadline</th>
                        <th>Status</th>
                        <th>Stage</th>
                        <th>Priority</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for case in cases %}
                    <tr class="hover:bg-gray-50 transition-colors duration-200">
                        <td class="font-medium">#{{ case.case_number }}</td>
                        <td>{{ case.patient }}</td>
                        <td>{{ case.received_date_time|date:"Y-m-d H:i" }}</td>
                        <td>
                            {% if case.deadline %}
                                <span class="{% if case.is_overdue %}text-danger{% endif %}">
                                    {{ case.deadline|date:"Y-m-d" }}
                                </span>
                            {% else %}
                                <span class="text-gray-400">Not set</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="status-badge status-{{ case.status|lower }}">
                                {{ case.get_status_display }}
                            </span>
                        </td>
                        <td>
                            {% if case.current_stage %}
                                <div class="text-sm">
                                    <div>{{ case.current_stage.name }}</div>
                                    <div class="text-gray-500 text-xs">
                                        {{ case.current_stage.department.name }}
                                    </div>
                                </div>
                            {% else %}
                                <span class="text-gray-400">Not assigned</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                       {% if case.priority == 1 %}bg-red-100 text-red-800
                                       {% elif case.priority == 2 %}bg-yellow-100 text-yellow-800
                                       {% else %}bg-green-100 text-green-800{% endif %}">
                                {{ case.get_priority_display }}
                            </span>
                        </td>
                        <td>
                            <div class="flex space-x-2">
                                <button class="text-primary hover:text-primary-dark"
                                        onclick="viewCaseDetails({{ case.case_number }})">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="text-success hover:text-success-dark"
                                        onclick="editCase({{ case.case_number }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="text-warning hover:text-warning-dark"
                                        onclick="showCaseHistory({{ case.case_number }})">
                                    <i class="fas fa-history"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>


{% endblock %}



{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/frappe-gantt@0.6.1/dist/frappe-gantt.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>

// Initialize Flatpickr
const datePickerConfig = {
    dateFormat: "Y-m-d",
    defaultDate: "{{ start_date|date:'Y-m-d' }}",
};
const datePicker = flatpickr("#start_date", datePickerConfig);

// Prepare Gantt Chart Data
const ganttTasks = [];

{% for case in cases %}
    // Main case timeline
    ganttTasks.push({
        id: 'case-{{ case.case_number }}',
        name: 'Case #{{ case.case_number }}{% if case.patient %} - {{ case.patient }}{% endif %}',
        start: '{{ case.received_date_time|date:"Y-m-d" }}' || '{{ start_date|date:"Y-m-d" }}',
        end: '{{ case.ship_date_time|date:"Y-m-d" }}' || '{{ end_date|date:"Y-m-d" }}',
        progress: {% if case.status == 'completed' %}100{% elif case.status == 'in_progress' %}50{% else %}25{% endif %},
        custom_class: 'gantt-task-{{ case.status }}',
        dependencies: [],
        details: {
            type: 'case',
            case_number: '{{ case.case_number }}',
            patient: '{% if case.patient %}{{ case.patient }}{% else %}N/A{% endif %}',
            dentist: '{% if case.dentist %}{{ case.dentist }}{% else %}N/A{% endif %}',
            status: '{{ case.get_status_display }}',
            priority: '{{ case.get_priority_display }}',
            department: '{% if case.responsible_department %}{{ case.responsible_department.name }}{% else %}Not assigned{% endif %}',
            received_date: '{{ case.received_date_time|date:"Y-m-d" }}',
            deadline: '{% if case.deadline %}{{ case.deadline|date:"Y-m-d" }}{% else %}Not set{% endif %}',
            is_overdue: {% if case.is_overdue %}true{% else %}false{% endif %}
        }
    });

    // Add stages as subtasks
    {% for stage in case.stagehistory_set.all %}
        {% if stage.start_date_time %}
        ganttTasks.push({
            id: 'stage-{{ case.case_number }}-{{ forloop.counter }}',
            name: '{{ stage.stage.name|default:"Unknown Stage" }}',
            start: '{{ stage.start_date_time|date:"Y-m-d" }}',
            end: '{% if stage.end_date_time %}{{ stage.end_date_time|date:"Y-m-d" }}{% else %}{{ end_date|date:"Y-m-d" }}{% endif %}',
            parent: 'case-{{ case.case_number }}',
            custom_class: 'gantt-task-stage',
            details: {
                type: 'stage',
                stage_name: '{{ stage.stage.name|default:"Unknown Stage" }}',
                department: '{% if stage.stage.department %}{{ stage.stage.department.name }}{% else %}Not assigned{% endif %}',
                start_date: '{{ stage.start_date_time|date:"Y-m-d" }}',
                end_date: '{% if stage.end_date_time %}{{ stage.end_date_time|date:"Y-m-d" }}{% else %}In progress{% endif %}',
                case_number: '{{ case.case_number }}'
            }
        });
        {% endif %}
    {% endfor %}
{% endfor %}

// Initialize Gantt Chart
let ganttChart;

if (ganttTasks.length > 0) {
    ganttChart = new Gantt('#gantt-chart', ganttTasks, {
        header_height: 50,
        column_width: 30,
        step: 24,
        view_modes: ['Day', 'Week', 'Month'],
        bar_height: 20,
        bar_corner_radius: 3,
        arrow_curve: 5,
        padding: 18,
        view_mode: 'Week',
        date_format: 'YYYY-MM-DD',
        popup_trigger: 'click',
        on_click: (task) => {
            if (task.details && task.details.type === 'case') {
                viewCaseDetails(task.details.case_number);
            }
        },
        custom_popup_html: (task) => {
            // Different popup content based on task type
            if (task.details && task.details.type === 'case') {
                return `
                    <div class="gantt-popup">
                        <div class="gantt-popup-title">Case #${task.details.case_number}</div>
                        <div class="gantt-popup-content">
                            <div class="gantt-popup-item">
                                <div class="gantt-popup-label">Patient:</div>
                                <div class="gantt-popup-value">${task.details.patient}</div>
                            </div>
                            <div class="gantt-popup-item">
                                <div class="gantt-popup-label">Status:</div>
                                <div class="gantt-popup-value">${task.details.status}</div>
                            </div>
                            <div class="gantt-popup-item">
                                <div class="gantt-popup-label">Priority:</div>
                                <div class="gantt-popup-value">${task.details.priority}</div>
                            </div>
                            <div class="gantt-popup-item">
                                <div class="gantt-popup-label">Department:</div>
                                <div class="gantt-popup-value">${task.details.department}</div>
                            </div>
                            <div class="gantt-popup-item">
                                <div class="gantt-popup-label">Deadline:</div>
                                <div class="gantt-popup-value ${task.details.is_overdue ? 'text-danger' : ''}">
                                    ${task.details.deadline} ${task.details.is_overdue ? '(Overdue)' : ''}
                                </div>
                            </div>
                            <div class="mt-2 text-center">
                                <button class="btn btn-sm btn-primary" onclick="viewCaseDetails('${task.details.case_number}')">
                                    <i class="fas fa-eye mr-1"></i> View Details
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            } else if (task.details && task.details.type === 'stage') {
                return `
                    <div class="gantt-popup">
                        <div class="gantt-popup-title">${task.details.stage_name}</div>
                        <div class="gantt-popup-content">
                            <div class="gantt-popup-item">
                                <div class="gantt-popup-label">Case:</div>
                                <div class="gantt-popup-value">#${task.details.case_number}</div>
                            </div>
                            <div class="gantt-popup-item">
                                <div class="gantt-popup-label">Department:</div>
                                <div class="gantt-popup-value">${task.details.department}</div>
                            </div>
                            <div class="gantt-popup-item">
                                <div class="gantt-popup-label">Start Date:</div>
                                <div class="gantt-popup-value">${task.details.start_date}</div>
                            </div>
                            <div class="gantt-popup-item">
                                <div class="gantt-popup-label">End Date:</div>
                                <div class="gantt-popup-value">${task.details.end_date}</div>
                            </div>
                        </div>
                    </div>
                `;
            }
            return '';
        }
    });

    // View mode buttons
    document.querySelectorAll('.view-mode-button').forEach(button => {
        button.addEventListener('click', () => {
            const viewMode = button.dataset.view;
            document.querySelectorAll('.view-mode-button').forEach(btn => {
                btn.classList.remove('active');
            });
            button.classList.add('active');
            ganttChart.change_view_mode(viewMode);
        });
    });

    // Today button
    document.getElementById('gantt-today').addEventListener('click', () => {
        ganttChart.scroll_to_today();
    });

    // Zoom buttons
    document.getElementById('gantt-zoom-in').addEventListener('click', () => {
        ganttChart.change_view_mode('Day');
    });

    document.getElementById('gantt-zoom-out').addEventListener('click', () => {
        ganttChart.change_view_mode('Month');
    });
}

// Loading State Management
const loadingOverlay = document.getElementById('loadingOverlay');
const showLoading = () => loadingOverlay && loadingOverlay.classList.remove('hidden');
const hideLoading = () => loadingOverlay && loadingOverlay.classList.add('hidden');

// Form Submission Handler
document.getElementById('filterForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    showLoading();
    try {
        const formData = new FormData(e.target);
        const queryString = new URLSearchParams(formData).toString();
        const response = await fetch(`?${queryString}`);
        if (!response.ok) throw new Error('Network response was not ok');
        window.location.href = `?${queryString}`;
    } catch (error) {
        console.error('Error:', error);
        showError('Failed to apply filters. Please try again.');
    }
});

// Export to PDF functionality
document.getElementById('exportPDF').addEventListener('click', async () => {
    try {
        showLoading();
        // Create a clone of the gantt chart for PDF export
        const ganttContainer = document.querySelector('.gantt-container');
        const ganttClone = ganttContainer.cloneNode(true);

        // Take a screenshot of the gantt chart using html2canvas
        const ganttElement = document.getElementById('gantt-chart');
        const canvas = await html2canvas(ganttElement, {
            scale: 2,
            logging: false,
            useCORS: true,
            allowTaint: true
        });

        // Replace the gantt chart div with the canvas in the clone
        const ganttChartDiv = ganttClone.querySelector('#gantt-chart');
        if (ganttChartDiv) {
            const imgContainer = document.createElement('div');
            imgContainer.style.textAlign = 'center';
            imgContainer.style.marginTop = '20px';

            const img = document.createElement('img');
            img.src = canvas.toDataURL('image/png');
            img.style.maxWidth = '100%';
            img.style.boxShadow = '0 4px 6px rgba(0,0,0,0.1)';

            imgContainer.appendChild(img);
            ganttChartDiv.parentNode.replaceChild(imgContainer, ganttChartDiv);
        }

        // Create a temporary div to hold the content for PDF generation
        const tempDiv = document.createElement('div');
        tempDiv.style.padding = '20px';

        // Add a title
        const title = document.createElement('h1');
        title.textContent = 'Production Timeline Report';
        title.style.textAlign = 'center';
        title.style.marginBottom = '20px';
        title.style.color = '#4285F4';
        tempDiv.appendChild(title);

        // Add date range
        const dateRange = document.createElement('p');
        dateRange.textContent = `Date Range: {{ start_date|date:"F j, Y" }} to {{ end_date|date:"F j, Y" }}`;
        dateRange.style.textAlign = 'center';
        dateRange.style.marginBottom = '30px';
        dateRange.style.fontSize = '14px';
        tempDiv.appendChild(dateRange);

        // Add the gantt chart clone
        tempDiv.appendChild(ganttClone);

        // Add cases table
        const casesTable = document.createElement('div');
        casesTable.innerHTML = `
            <h2 style="margin-top: 40px; margin-bottom: 20px; color: #4285F4;">Case Summary</h2>
            <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
                <thead>
                    <tr style="background-color: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                        <th style="padding: 12px; text-align: left; border: 1px solid #dee2e6;">Case #</th>
                        <th style="padding: 12px; text-align: left; border: 1px solid #dee2e6;">Patient</th>
                        <th style="padding: 12px; text-align: left; border: 1px solid #dee2e6;">Status</th>
                        <th style="padding: 12px; text-align: left; border: 1px solid #dee2e6;">Received Date</th>
                        <th style="padding: 12px; text-align: left; border: 1px solid #dee2e6;">Deadline</th>
                        <th style="padding: 12px; text-align: left; border: 1px solid #dee2e6;">Department</th>
                    </tr>
                </thead>
                <tbody>
                    {% for case in cases %}
                    <tr style="border-bottom: 1px solid #dee2e6;">
                        <td style="padding: 12px; border: 1px solid #dee2e6;">#{{ case.case_number }}</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">{{ case.patient|default:"N/A" }}</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">{{ case.get_status_display }}</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">{{ case.received_date_time|date:"Y-m-d" }}</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">{{ case.deadline|date:"Y-m-d"|default:"Not set" }}</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">{{ case.responsible_department.name|default:"Not assigned" }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        `;
        tempDiv.appendChild(casesTable);

        // Add footer
        const footer = document.createElement('div');
        footer.style.marginTop = '40px';
        footer.style.borderTop = '1px solid #dee2e6';
        footer.style.paddingTop = '20px';
        footer.style.textAlign = 'center';
        footer.style.fontSize = '12px';
        footer.style.color = '#6c757d';
        footer.innerHTML = `
            <p>Generated on ${new Date().toLocaleString()}</p>
            <p>Dental Lab Management System</p>
        `;
        tempDiv.appendChild(footer);

        // Generate PDF
        const opt = {
            margin: 10,
            filename: `Production_Timeline_${new Date().toISOString().split('T')[0]}.pdf`,
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: { scale: 2 },
            jsPDF: { unit: 'mm', format: 'a4', orientation: 'landscape' }
        };

        await html2pdf().set(opt).from(tempDiv).save();

        Swal.fire({
            icon: 'success',
            title: 'PDF Generated Successfully',
            text: 'Your production timeline report has been downloaded.',
            timer: 2000,
            showConfirmButton: false
        });
    } catch (error) {
        console.error('PDF generation failed:', error);
        showError('Failed to generate PDF. Please try again.');
    } finally {
        hideLoading();
    }
});

// Case Actions
async function viewCaseDetails(caseNumber) {
    showLoading();
    try {
        const response = await fetch(`/api/cases/${caseNumber}/`);
        if (!response.ok) throw new Error('Failed to fetch case details');
        const caseData = await response.json();

        Swal.fire({
            title: `Case #${caseNumber} Details`,
            html: generateCaseDetailsHTML(caseData),
            width: '80%',
            showCloseButton: true,
            showConfirmButton: false
        });
    } catch (error) {
        showError('Failed to load case details. Please try again.');
    } finally {
        hideLoading();
    }
}

function generateCaseDetailsHTML(caseData) {
    return `
        <div class="case-details">
            <div class="grid grid-cols-2 gap-4">
                <div class="detail-group">
                    <label>Patient</label>
                    <div>${caseData.patient}</div>
                </div>
                <div class="detail-group">
                    <label>Dentist</label>
                    <div>${caseData.dentist}</div>
                </div>
                <div class="detail-group">
                <label>Status</label>
                    <div>${caseData.status}</div>
                </div>
                <div class="detail-group">
                    <label>Received Date</label>
                    <div>${caseData.received_date}</div>
                </div>
                <div class="detail-group">
                    <label>Deadline</label>
                    <div>${caseData.deadline || 'Not set'}</div>
                </div>
                <div class="detail-group">
                    <label>Current Stage</label>
                    <div>${caseData.current_stage || 'Not assigned'}</div>
                </div>
            </div>
        </div>
    `;
}

async function editCase(caseNumber) {
    window.location.href = `/cases/${caseNumber}/edit/`;
}

async function showCaseHistory(caseNumber) {
    showLoading();
    try {
        const response = await fetch(`/api/cases/${caseNumber}/history/`);
        if (!response.ok) throw new Error('Failed to fetch case history');
        const history = await response.json();

        Swal.fire({
            title: `Case #${caseNumber} History`,
            html: generateHistoryHTML(history),
            width: '60%',
            showCloseButton: true,
            showConfirmButton: false
        });
    } catch (error) {
        showError('Failed to load case history. Please try again.');
    } finally {
        hideLoading();
    }
}

function generateHistoryHTML(history) {
    return `
        <div class="history-timeline">
            ${history.map(event => `
                <div class="timeline-item border-l-2 border-primary pl-4 mb-4">
                    <div class="time text-sm text-gray-500">
                        ${new Date(event.start_date).toLocaleString()}
                    </div>
                    <div class="event font-medium">
                        ${event.stage}
                    </div>
                    <div class="duration text-sm">
                        Duration: ${event.duration || 'In progress'}
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

// Refresh Data Handler
document.getElementById('refreshData').addEventListener('click', async () => {
    showLoading();
    try {
        await window.location.reload(true);
    } catch (error) {
        showError('Failed to refresh data. Please try again.');
    } finally {
        hideLoading();
    }
});

// Error Handler
function showError(message) {
    Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: message,
        timer: 3000,
        showConfirmButton: false
    });
}

// Status Update Handler
async function updateCaseStatus(caseNumber, newStatus) {
    showLoading();
    try {
        const response = await fetch(`/api/cases/${caseNumber}/status/`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({ status: newStatus })
        });

        if (!response.ok) throw new Error('Failed to update status');

        const data = await response.json();
        Swal.fire({
            icon: 'success',
            title: 'Status Updated',
            timer: 1500,
            showConfirmButton: false
        });

        setTimeout(() => window.location.reload(), 1500);
    } catch (error) {
        showError('Failed to update case status. Please try again.');
    } finally {
        hideLoading();
    }
}

// CSRF Token Handler
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Window Resize Handler for Mermaid
let resizeTimeout;
window.addEventListener('resize', () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
        mermaid.init(undefined, document.querySelector(".mermaid"));
    }, 250);
});

// Table Sorting
document.querySelectorAll('th[data-sort]').forEach(headerCell => {
    headerCell.addEventListener('click', () => {
        const tableElement = headerCell.closest('table');
        const headerIndex = Array.prototype.indexOf.call(headerCell.parentElement.children, headerCell);
        const currentIsAscending = headerCell.classList.contains('th-sort-asc');

        sortTableByColumn(tableElement, headerIndex, !currentIsAscending);
    });
});

function sortTableByColumn(table, column, asc = true) {
    const dirModifier = asc ? 1 : -1;
    const tBody = table.tBodies[0];
    const rows = Array.from(tBody.querySelectorAll('tr'));

    const sortedRows = rows.sort((a, b) => {
        const aColText = a.querySelector(`td:nth-child(${column + 1})`).textContent.trim();
        const bColText = b.querySelector(`td:nth-child(${column + 1})`).textContent.trim();

        return aColText > bColText ? (1 * dirModifier) : (-1 * dirModifier);
    });

    while (tBody.firstChild) {
        tBody.removeChild(tBody.firstChild);
    }

    tBody.append(...sortedRows);

    // Update sorting indicators
    table.querySelectorAll('th').forEach(th => th.classList.remove('th-sort-asc', 'th-sort-desc'));
    table.querySelector(`th:nth-child(${column + 1})`).classList.toggle('th-sort-asc', asc);
    table.querySelector(`th:nth-child(${column + 1})`).classList.toggle('th-sort-desc', !asc);
}

// Initialize tooltips if Bootstrap is used
if (typeof bootstrap !== 'undefined') {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}
</script>
{% endblock %}