{% extends 'base.html' %}

{% block title %}Create New Task{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
/* Modern Color Scheme */
:root {
    --primary: #4f46e5;
    --primary-light: #6366f1;
    --secondary: #64748b;
    --success: #22c55e;
    --danger: #ef4444;
    --warning: #f59e0b;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-700: #334155;
    --gray-800: #1e293b;
}

/* Card and Layout Styles */
.task-create-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.task-form-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    overflow: hidden;
}

.form-header {
    background: var(--gray-50);
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.form-header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-content {
    padding: 2rem;
}

/* Section Styles */
.form-section {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--gray-200);
}

.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-700);
    margin: 0;
}

/* Grid Layout */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

/* Form Controls */
.form-group {
    margin-bottom: 1.25rem;
}

.form-group label {
    display: block;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.form-control, .select2-container--default .select2-selection--single {
    width: 100%;
    padding: 0.625rem 0.875rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.5rem;
    background-color: white;
    transition: all 0.2s;
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    outline: none;
}

textarea.form-control {
    min-height: 100px;
    resize: vertical;
}

/* Form Actions */
.form-actions {
    padding: 1.5rem;
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.btn {
    padding: 0.625rem 1.25rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--primary);
    color: white;
    border: none;
}

.btn-primary:hover {
    background: var(--primary-light);
    transform: translateY(-1px);
}

.btn-secondary {
    background: white;
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn-secondary:hover {
    background: var(--gray-50);
    transform: translateY(-1px);
}

/* Error States */
.field-error {
    color: var(--danger);
    font-size: 0.875rem;
    margin-top: 0.375rem;
}

/* Required Field Indicator */
.required-field::after {
    content: "*";
    color: var(--danger);
    margin-left: 0.25rem;
}

/* Select2 Customization */
.select2-container--default .select2-selection--single {
    height: auto;
    padding: 0.625rem 0.875rem;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 100%;
}
</style>
{% endblock %}

{% block content %}
<div class="task-create-container">
    <form method="post" enctype="multipart/form-data" class="task-form-card">
        {% csrf_token %}

        <!-- Header -->
        <div class="form-header">
            <h1>
                <i class="bi bi-plus-circle-fill"></i>
                Create New Task
            </h1>
        </div>

        <div class="form-content">
            <!-- Basic Information Section -->
            <div class="form-section">
                <div class="section-header">
                    <i class="bi bi-info-circle-fill"></i>
                    <h2 class="section-title">Basic Information</h2>
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="required-field">Case</label>
                        {{ form.case }}
                        {% if form.case.errors %}
                            <div class="field-error">{{ form.case.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="form-group">
                        <label class="required-field">Title</label>
                        {{ form.title }}
                        {% if form.title.errors %}
                            <div class="field-error">{{ form.title.errors }}</div>
                        {% endif %}
                    </div>
                </div>
                <div class="form-group">
                    <label>Description</label>
                    {{ form.description }}
                    {% if form.description.errors %}
                        <div class="field-error">{{ form.description.errors }}</div>
                    {% endif %}
                </div>
            </div>

                        <!-- Assignment Section -->
            <div class="form-section">
                <div class="section-header">
                    <i class="bi bi-person-fill"></i>
                    <h2 class="section-title">Assignment Details</h2>
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="required-field">Workflow Stage</label>
                        {{ form.workflow_stage }}
                        {% if form.workflow_stage.errors %}
                            <div class="field-error">{{ form.workflow_stage.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="form-group">
                        <label>Assigned To</label>
                        {{ form.assigned_to }}
                        {% if form.assigned_to.errors %}
                            <div class="field-error">{{ form.assigned_to.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="form-group">
                        <label class="required-field">Status</label>
                        {{ form.status }}
                        {% if form.status.errors %}
                            <div class="field-error">{{ form.status.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>



            <!-- Schedule Section -->
            <div class="form-section">
                <div class="section-header">
                    <i class="bi bi-calendar-fill"></i>
                    <h2 class="section-title">Schedule & Priority</h2>
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="required-field">Start Time</label>
                        {{ form.scheduled_start_time }}
                        {% if form.scheduled_start_time.errors %}
                            <div class="field-error">{{ form.scheduled_start_time.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="form-group">
                        <label class="required-field">End Time</label>
                        {{ form.scheduled_end_time }}
                        {% if form.scheduled_end_time.errors %}
                            <div class="field-error">{{ form.scheduled_end_time.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="form-group">
                        <label class="required-field">Priority</label>
                        {{ form.priority }}
                        {% if form.priority.errors %}
                            <div class="field-error">{{ form.priority.errors }}</div>
                        {% endif %}
                    </div>
                    <div class="form-group">
                        <label class="required-field">Estimated Duration</label>
                        {{ form.estimated_duration }}
                        {% if form.estimated_duration.errors %}
                            <div class="field-error">{{ form.estimated_duration.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Additional Information Section -->
            <div class="form-section">
                <div class="section-header">
                    <i class="bi bi-file-earmark-text-fill"></i>
                    <h2 class="section-title">Additional Information</h2>
                </div>
                <div class="form-group">
                    <label>Notes</label>
                    {{ form.notes }}
                    {% if form.notes.errors %}
                        <div class="field-error">{{ form.notes.errors }}</div>
                    {% endif %}
                </div>
                <div class="form-group">
                    <label>Attachments</label>
                    {{ form.attachments }}
                    {% if form.attachments.errors %}
                        <div class="field-error">{{ form.attachments.errors }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- Hidden Fields -->
            {{ form.required_skills }}
            {{ form.required_equipment }}
            {{ form.quality_checklist }}
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
            <a href="{% url 'case:task_list' %}" class="btn btn-secondary">
                <i class="bi bi-x-lg"></i>
                Cancel
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="bi bi-check-lg"></i>
                Create Task
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'classic',
        width: '100%'
    });

    // Date/Time Handling
    const startTimeInput = document.querySelector('[name="scheduled_start_time"]');
    const endTimeInput = document.querySelector('[name="scheduled_end_time"]');
    
    if (startTimeInput && endTimeInput) {
        startTimeInput.addEventListener('change', function() {
            endTimeInput.min = this.value;
            if (endTimeInput.value && endTimeInput.value < this.value) {
                endTimeInput.value = this.value;
            }
        });
    }

    // Priority Color Coding
    const prioritySelect = document.querySelector('[name="priority"]');
    if (prioritySelect) {
        const updatePriorityStyle = () => {
            const value = prioritySelect.value;
            prioritySelect.className = 'form-control';
            switch(value) {
                case '4': prioritySelect.classList.add('bg-danger', 'text-white'); break;
                case '3': prioritySelect.classList.add('bg-warning'); break;
                case '2': prioritySelect.classList.add('bg-info'); break;
                case '1': prioritySelect.classList.add('bg-success', 'text-white'); break;
            }
        };
        
        prioritySelect.addEventListener('change', updatePriorityStyle);
        updatePriorityStyle();
    }

    // Form Validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
});
</script>
{% endblock %}