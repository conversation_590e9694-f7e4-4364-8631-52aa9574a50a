# accounts/urls.py

from django.urls import path
from django.contrib.auth import views as auth_views
from . import views

app_name = 'accounts'

urlpatterns = [
    # Authentication URLs
    path('register/', views.RegisterView.as_view(), name='register'),
    path('login/', views.LoginView.as_view(), name='login'),
    path('logout/', views.logout_view, name='logout'),
    
    # Profile Management URLs
    path('profile/', views.ProfileView.as_view(), name='profile'),
    path('profile/settings/', views.UserSettingsView.as_view(), name='profile_settings'),
    path('profile/password/', views.PasswordManagementView.as_view(), name='change_password'),
    path('profile/image/update/', views.ProfileImageUpdateView.as_view(), name='update_profile_image'),
    
    # Qualification URLs
    path('qualifications/', views.QualificationListView.as_view(), name='qualifications'),
    path('qualifications/add/', views.QualificationCreateView.as_view(), name='add_qualification'),
    path('qualifications/<int:pk>/delete/', views.QualificationDeleteView.as_view(), name='delete_qualification'),
    
    path('qualifications/', views.QualificationListView.as_view(), name='qualifications'),
    path('qualifications/create/', views.QualificationCreateView.as_view(), name='qualification_create'),
    # path('qualifications/<int:pk>/update/', views.QualificationUpdateView.as_view(), name='qualification_update'),
    path('qualifications/<int:pk>/delete/', views.QualificationDeleteView.as_view(), name='qualification_delete'),
    
    
    # Activity Log
    path('activity-log/', views.ActivityLogView.as_view(), name='activity_log'),
    
    # Password Reset URLs
    path('password-reset/', views.PasswordResetView.as_view(), name='password_reset'),
    path('password-reset/done/', 
        auth_views.PasswordResetDoneView.as_view(
            template_name='accounts/auth/password_reset_done.html'
        ),
        name='password_reset_done'
    ),
    path('password-reset/confirm/<uidb64>/<token>/',
        auth_views.PasswordResetConfirmView.as_view(
            template_name='accounts/auth/password_reset_confirm.html',
            success_url='/accounts/password-reset/complete/'
        ),
        name='password_reset_confirm'
    ),
    path('password-reset/complete/',
        auth_views.PasswordResetCompleteView.as_view(
            template_name='accounts/auth/password_reset_complete.html'
        ),
        name='password_reset_complete'
    ),
    
    # Email Verification URLs
    path('verify-email/<str:uidb64>/<str:token>/', 
        views.EmailVerificationView.as_view(), 
        name='verify_email'
    ),
    path('resend-verification/', 
        views.ResendVerificationView.as_view(), 
        name='resend_verification'
    ),
    
    # Admin URLs (require superuser)
    path('users/', views.UserManagementView.as_view(), name='user_management'),
    path('users/<int:pk>/', views.UserDetailView.as_view(), name='user_detail'),
    path('users/<int:pk>/edit/', views.UserUpdateView.as_view(), name='user_edit'),
    path('users/<int:pk>/delete/', views.UserDeleteView.as_view(), name='user_delete'),
    
    # Error Pages
    path('403/', views.custom_403, name='403'),
    path('404/', views.custom_404, name='404'),
    path('500/', views.custom_500, name='500'),
    
    
    path('settings/update/', views.UserSettingsUpdateView.as_view(), name='settings_update'),
    path('2fa/setup/', views.TwoFactorSetupView.as_view(), name='setup_2fa'),
    path('activity-log/', views.ActivityLogView.as_view(), name='activity_log'),
    path('activity-log/export/', views.ExportActivityLogView.as_view(), name='export_activity_log'),
    
    
    path('system-settings/', views.SystemSettingsView.as_view(), name='system_settings'),



]

# Add to your project's main urls.py (LAB/urls.py)
handler403 = 'accounts.views.custom_403'
handler404 = 'accounts.views.custom_404'
handler500 = 'accounts.views.custom_500'