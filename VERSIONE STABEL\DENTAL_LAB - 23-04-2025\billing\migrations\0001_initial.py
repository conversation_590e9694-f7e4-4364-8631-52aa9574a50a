# Generated by Django 5.0.4 on 2025-04-17 21:40

import django.utils.timezone
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Invoice",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "patient_name",
                    models.CharField(
                        help_text="Full name of the patient as it should appear on the invoice.",
                        max_length=255,
                        verbose_name="Patient Name",
                    ),
                ),
                (
                    "date",
                    models.DateField(
                        default=django.utils.timezone.now,
                        help_text="The date the invoice was issued.",
                        verbose_name="Invoice Date",
                    ),
                ),
                (
                    "due_date",
                    models.DateField(
                        blank=True,
                        help_text="Optional: The date by which the payment is due.",
                        null=True,
                        verbose_name="Due Date",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("unpaid", "Unpaid"),
                            ("partial", "Partially Paid"),
                            ("paid", "Paid"),
                            ("cancelled", "Cancelled"),
                        ],
                        db_index=True,
                        default="unpaid",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="The calculated total amount of the invoice in the specified currency.",
                        max_digits=12,
                        verbose_name="Total Amount",
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Optional internal or external notes for the invoice.",
                        verbose_name="Notes",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Invoice",
                "verbose_name_plural": "Invoices",
                "ordering": ["-date", "-id"],
            },
        ),
        migrations.CreateModel(
            name="InvoiceItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "description",
                    models.CharField(
                        blank=True,
                        help_text="Optional description, defaults to item name if blank.",
                        max_length=255,
                        verbose_name="Description",
                    ),
                ),
                (
                    "quantity",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("1.0"),
                        max_digits=10,
                        verbose_name="Quantity",
                    ),
                ),
                (
                    "selling_price",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="The price per unit for this item at the time of invoicing.",
                        max_digits=10,
                        verbose_name="Unit Price",
                    ),
                ),
            ],
            options={
                "verbose_name": "Invoice Item",
                "verbose_name_plural": "Invoice Items",
                "ordering": ["id"],
            },
        ),
        migrations.CreateModel(
            name="PurchaseOrder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "order_date",
                    models.DateField(
                        default=django.utils.timezone.now, verbose_name="Order Date"
                    ),
                ),
                (
                    "expected_delivery_date",
                    models.DateField(
                        blank=True, null=True, verbose_name="Expected Delivery Date"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("submitted", "Submitted"),
                            ("approved", "Approved"),
                            ("partially_received", "Partially Received"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        db_index=True,
                        default="draft",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Approximate total, recalculate based on items if needed.",
                        max_digits=12,
                        verbose_name="Estimated Total Amount",
                    ),
                ),
                ("notes", models.TextField(blank=True, verbose_name="Notes")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Purchase Order",
                "verbose_name_plural": "Purchase Orders",
                "ordering": ["-order_date", "-id"],
            },
        ),
        migrations.CreateModel(
            name="PurchaseOrderItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "quantity",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="Quantity"
                    ),
                ),
                (
                    "price_per_unit",
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name="Price Per Unit"
                    ),
                ),
            ],
            options={
                "verbose_name": "Purchase Order Item",
                "verbose_name_plural": "Purchase Order Items",
                "ordering": ["id"],
            },
        ),
    ]
