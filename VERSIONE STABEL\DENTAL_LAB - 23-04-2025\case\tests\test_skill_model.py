from django.test import TestCase
from case.models import *
from decimal import Decimal
from django.utils import timezone

class SkillModelTest(TestCase):
    """Test case for the Skill model."""
    
    def setUp(self):
        """Set up test data."""
        # TODO: Create test data
        pass
    
    def test_model_creation(self):
        """Test that a Skill can be created."""
        # TODO: Implement test
        pass
    
    def test_model_str(self):
        """Test the string representation of a Skill."""
        # TODO: Implement test
        pass
