{% load static %}
{% load i18n %}
{% block navbar %}

<style>
    /* Adjust main content to account for navbar */
    main.main-content {
        margin-top: 60px !important; /* Adjust based on the navbar height */
        padding-top: 1rem !important;
    }
    :root {
        --primary-color: #090979;
        --secondary-color: #4b12cf;
        --text-color: #ffffff;
        --hover-color: #e0e0e0;
        --transition-speed: 0.3s;
        --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        --border-radius: 8px;
        --font-family: 'Inter', sans-serif;
        --dropdown-bg: #ffffff;
        --dropdown-text: #1a1a1a;
        --dropdown-hover: #f3f4f6;
        --header-bg: #f1f5f9;
        --divider-color: #e2e8f0;
        --icon-size: 1.2rem;
        --nav-link-padding: 0.75rem 1.2rem;
        /* <PERSON><PERSON>yrat e reja për ikonat */
        --icon-color: #00ffff; /* <PERSON>jyr<PERSON> e hapur për kontrast */
        --icon-hover-color: #ffffff; /* Ngjyrë e bardhë kur kalon miu sipër */
        --dropdown-icon-color: #0bd7cc; /* Ngjyra e ikonave në dropdown (e njëjtë me secondary-color) */
        --dropdown-icon-hover-color: #090979; /* Ngjyra e ikonave në dropdown kur kalon miu sipër */
    }

    .navbar {
        background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
        padding: 0.4rem 0.25rem;
        box-shadow: var(--box-shadow);
        font-family: var(--font-family);
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 1000;
        height: auto;
    }

    .navbar-brand {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 0;
        margin-right: 0.5rem;
    }

    .navbar-brand img {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        transition: transform var(--transition-speed);
        object-fit: cover;
    }

    .navbar-brand span {
        color: var(--text-color);
        font-size: 1.1rem;
        font-weight: 600;
        letter-spacing: 0.5px;
    }

    .navbar-brand:hover img {
        transform: scale(1.1);
    }

    .nav-link {
        color: var(--text-color) !important;
        font-weight: 500;
        padding: 0.4rem 0.5rem !important; /* Very compact padding */
        border-radius: var(--border-radius);
        transition: all var(--transition-speed);
        position: relative;
        letter-spacing: 0px;
        display: flex;
        align-items: center;
        gap: 0.3rem; /* Minimal gap */
        font-size: 0.85rem; /* Smaller font for better fit */
        white-space: nowrap;
    }

    /* Stilizimi i ikonave në navbar */
    .nav-link i {
        font-size: 1rem;
        color: var(--icon-color); /* Përdorim variablën e re */
    }

    .nav-link:hover,
    .nav-link:focus {
        color: var(--hover-color) !important;
        background-color: rgba(255, 255, 255, 0.1);
    }

    /* Stilizimi i ikonave kur kalon miu sipër */
    .nav-link:hover i,
    .nav-link:focus i {
        color: var(--icon-hover-color); /* Përdorim variablën e re për hover */
    }

    .dropdown {
        position: relative;
    }

    .dropdown-menu {
        background: var(--dropdown-bg);
        border: none;
        box-shadow: var(--box-shadow);
        border-radius: var(--border-radius);
        padding: 0.5rem;
        margin-top: -2px !important;
        min-width: 250px; /* Increased width for better readability */
        max-height: calc(100vh - 100px); /* Limit height to prevent overflow */
        overflow-y: auto; /* Add scrolling for long menus */
        display: none;
        opacity: 0;
        transform: translateY(-10px);
        transition: all var(--transition-speed);
    }

    .dropdown-menu.show {
        display: block;
        opacity: 1;
        transform: translateY(0);
    }

    .dropdown:hover .dropdown-menu::before {
        content: '';
        position: absolute;
        top: -10px;
        left: 0;
        right: 0;
        height: 10px;
        background: transparent;
    }

    .dropdown-header {
        color: var(--secondary-color);
        font-weight: 600;
        padding: 0.4rem 0.75rem;
        font-size: 0.8rem;
        background-color: var(--header-bg);
        border-radius: var(--border-radius);
        margin-bottom: 0.25rem;
    }

    .dropdown-item {
        color: var(--dropdown-text);
        padding: 0.5rem 0.75rem;
        border-radius: var(--border-radius);
        transition: all var(--transition-speed);
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        white-space: nowrap;
    }

    /* Stilizimi i ikonave në dropdown */
    .dropdown-item i {
        font-size: 1rem;
        width: 20px;
        text-align: center;
        color: var(--dropdown-icon-color); /* Përdorim variablën e re */
    }

    .dropdown-item:hover,
    .dropdown-item:focus {
        background-color: var(--dropdown-hover);
        color: var(--primary-color);
        transform: translateX(3px);
    }

    /* Stilizimi i ikonave në dropdown kur kalon miu sipër */
    .dropdown-item:hover i,
    .dropdown-item:focus i {
        color: var(--dropdown-icon-hover-color); /* Përdorim variablën e re për hover */
    }

    .dropdown-divider {
        margin: 0.5rem 0;
        border-color: var(--divider-color);
    }

    .navbar-toggler {
        border: none;
        padding: 0.5rem;
        color: var(--text-color);
    }

    .navbar-toggler:focus {
        box-shadow: none;
        outline: none;
    }

    .user-dropdown {
        margin-left: auto;
    }

    .navbar-nav {
        margin-left: 0.25rem;
    }

    /* Ensure all navbar items fit */
    .navbar-collapse {
        justify-content: space-between;
    }

    .user-dropdown .nav-link {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .user-icon {
        width: 30px;
        height: 30px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .nav-link.active {
        background-color: rgba(255, 255, 255, 0.1);
        font-weight: 600;
    }

    .dropdown-item.active {
        background-color: var(--primary-color);
        color: white !important;
    }

    .dropdown-item.active i {
        color: white;
    }

    /* Multi-level dropdown support */
    .dropdown-submenu {
        position: relative;
    }

    .dropdown-submenu > .dropdown-menu {
        top: 0;
        left: 100%;
        margin-top: -0.5rem;
        margin-left: 0;
        border-radius: var(--border-radius);
    }

    .dropdown-submenu > a:after {
        display: block;
        content: "";
        float: right;
        width: 0;
        height: 0;
        border-color: transparent;
        border-style: solid;
        border-width: 5px 0 5px 5px;
        border-left-color: var(--dropdown-icon-color);
        margin-top: 5px;
        margin-right: -10px;
    }

    .dropdown-submenu:hover > a:after {
        border-left-color: var(--dropdown-icon-hover-color);
    }

    @media (min-width: 992px) {
        .dropdown:hover > .dropdown-menu {
            display: block;
            opacity: 1;
            transform: translateY(0);
            animation: fadeIn 0.2s ease-in-out;
        }

        .dropdown-submenu:hover > .dropdown-menu {
            display: block;
            opacity: 1;
            transform: translateY(0);
            animation: fadeIn 0.2s ease-in-out;
        }

        .dropdown:hover > .nav-link,
        .dropdown-submenu:hover > a {
            background-color: rgba(255, 255, 255, 0.1);
        }
    }

    @media (max-width: 991.98px) {
        .navbar-collapse {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-top: 0.5rem;
            max-height: calc(100vh - 70px); /* Limit height to prevent overflow */
            overflow-y: auto; /* Add scrolling for long menus */
        }

        .nav-item {
            margin: 0.25rem 0;
        }

        .dropdown-menu {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            box-shadow: none;
            margin-top: 0 !important;
            max-height: none; /* Remove max-height for mobile */
            overflow-y: visible; /* Remove scrolling for mobile */
        }

        .dropdown-item {
            color: var(--text-color);
            padding-left: 2rem; /* Indent dropdown items for better hierarchy */
        }

        .dropdown-header {
            color: var(--hover-color);
            background-color: rgba(255, 255, 255, 0.05);
            padding-left: 1.5rem; /* Indent headers for better hierarchy */
        }

        .dropdown-divider {
            border-color: rgba(255, 255, 255, 0.1);
        }

        /* override mobile dropdown icon color */
        .dropdown-item i{
            color: var(--icon-color);
        }
        .dropdown-item:hover i,
        .dropdown-item:focus i{
            color: var(--icon-hover-color);
        }

        /* Mobile submenu styling */
        .dropdown-submenu > .dropdown-menu {
            position: static;
            margin-left: 1rem;
            padding-left: 1rem;
            border-left: 1px solid rgba(255, 255, 255, 0.2);
        }

        .dropdown-submenu > a:after {
            transform: rotate(90deg);
            margin-top: 8px;
        }

        /* Collapse all dropdowns initially on mobile */
        .dropdown-menu {
            display: none;
        }

        /* Show dropdown when toggled */
        .dropdown-menu.show {
            display: block;
        }
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>

<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container-fluid" style="padding-left: 0.25rem; padding-right: 0.25rem; max-width: 100%;">
        <!-- Brand -->
        <a class="navbar-brand" href="{% url 'home' %}">
            <img src="{% static 'img/dental.jpg' %}" alt="Dental Lab Logo">
            <span>Dental</span>
        </a>

        <!-- Mobile Toggle -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMain">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Main Navigation -->
        <div class="collapse navbar-collapse" id="navbarMain">
            {% if user.is_authenticated %}
            <ul class="navbar-nav me-auto">
                <!-- Dashboard -->
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'home' %}">
                        <i class="fas fa-home"></i>Home
                    </a>
                </li>

                <!-- Cases Section -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="casesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-folder-open"></i>Cases
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="casesDropdown">
                        <li>
                            <h6 class="dropdown-header">Case Management</h6>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'case:case_list' %}">
                                <i class="fas fa-list"></i>All Cases
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'case:case_create' %}">
                                <i class="fas fa-plus"></i>New Case
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'case:case_calendar' %}">
                                <i class="fas fa-calendar-alt"></i>Case Calendar
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'case:dhtmlx_gantt_view' %}">
                                <i class="fas fa-chart-bar"></i>Gantt Chart
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'case:search_cases' %}">
                                <i class="fas fa-search"></i>Search Cases
                            </a>
                        </li>
                        <li>
                            <hr class="dropdown-divider">
                        </li>
                        <li>
                            <h6 class="dropdown-header">Workflow</h6>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'case:tryout_list' %}">
                                <i class="fas fa-check-circle"></i>Tryouts
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'case:task_list' %}">
                                <i class="fas fa-tasks"></i>Tasks
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'case:workflow_stage_list' %}">
                                <i class="fas fa-stream"></i>Workflow Stages
                            </a>
                        </li>


                    </ul>
                </li>

                <!-- Finance Section -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="financeDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-dollar-sign"></i>Finance
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="financeDropdown">
                        <li>
                            <h6 class="dropdown-header">Billing</h6>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'billing:invoice_list' %}">
                                <i class="fas fa-file-invoice"></i>Invoices
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'billing:purchase_order_list' %}">
                                <i class="fas fa-shopping-cart"></i>Purchase Orders
                            </a>
                        </li>
                        <li>
                            <hr class="dropdown-divider">
                        </li>
                        <li>
                            <h6 class="dropdown-header">Accounts & Transactions</h6>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'finance:account_list' %}">
                                <i class="fas fa-university"></i>Accounts
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'finance:payment_list' %}">
                                <i class="fas fa-money-bill"></i>Payments
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'finance:expenses_list' %}">
                                <i class="fas fa-receipt"></i>Expenses
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'finance:transfer_list' %}">
                                <i class="fas fa-exchange-alt"></i>Transfers
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'finance:supplier_payment_list' %}">
                                <i class="fas fa-hand-holding-usd"></i>Supplier Payments
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Inventory Section -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="inventoryDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-box"></i>Inventory
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="inventoryDropdown">
                        <li>
                            <a class="dropdown-item" href="{% url 'items:item_list' %}">
                                <i class="fas fa-boxes"></i>Items
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'items:raw_material_list' %}">
                                <i class="fas fa-tools"></i>Raw Materials
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'items:unit_list' %}">
                                <i class="fas fa-ruler-combined"></i>Units
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'items:supplier_list' %}">
                                <i class="fas fa-truck"></i>Suppliers
                            </a>
                        </li>
                        <li>
                            <hr class="dropdown-divider">
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'items:item_inventory' %}">
                                <i class="fas fa-warehouse"></i>Item Inventory
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'items:raw_material_inventory' %}">
                                <i class="fas fa-warehouse"></i>Raw Material Inventory
                            </a>
                        </li>
                    </ul>
                </li>



                <!-- Accounting Section -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="accountingDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-book"></i>Accounting
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="accountingDropdown">
                        <li>
                            <a class="dropdown-item" href="{% url 'ledger:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i>Ledger Dashboard
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'ledger:journal_entry_list' %}">
                                <i class="fas fa-file-invoice"></i>Journal Entries
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'ledger:ledger_account_list' %}">
                                <i class="fas fa-list-alt"></i>Chart of Accounts
                            </a>
                        </li>
                        <li class="dropdown-divider"></li>
                        <li>
                            <h6 class="dropdown-header">Reports</h6>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'ledger:trial_balance' %}">
                                <i class="fas fa-balance-scale"></i>Trial Balance
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'ledger:general_ledger' %}">
                                <i class="fas fa-book-open"></i>General Ledger
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'ledger:balance_sheet' %}">
                                <i class="fas fa-file-invoice-dollar"></i>Balance Sheet
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'ledger:income_statement' %}">
                                <i class="fas fa-chart-pie"></i>Income Statement
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'ledger:accounts_receivable_aging' %}">
                                <i class="fas fa-clock"></i>Accounts Receivable Aging
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'ledger:accounts_payable_aging' %}">
                                <i class="fas fa-credit-card"></i>Accounts Payable Aging
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'ledger:cash_flow_statement' %}">
                                <i class="fas fa-money-bill-wave"></i>Cash Flow Statement
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'ledger:budget_vs_actual' %}">
                                <i class="fas fa-chart-bar"></i>Budget vs Actual
                            </a>
                        </li>
                        <li class="dropdown-divider"></li>
                        <li>
                            <h6 class="dropdown-header">Budget Management</h6>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'ledger:budget_list' %}">
                                <i class="fas fa-money-check-alt"></i>Budgets
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'ledger:budget_create' %}">
                                <i class="fas fa-plus"></i>Create Budget
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Scheduling Section -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="schedulingDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-calendar-alt"></i>Scheduling
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="schedulingDropdown">
                        <li>
                            <h6 class="dropdown-header">Schedule Management</h6>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'scheduling:schedule_list' %}">
                                <i class="fas fa-list"></i>Schedule List
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'scheduling:select_case' %}">
                                <i class="fas fa-plus"></i>Create Schedule
                            </a>
                        </li>
                        <li>
                            <hr class="dropdown-divider">
                        </li>
                        <li>
                            <h6 class="dropdown-header">Dashboards & Analysis</h6>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'scheduling:workflow_dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i>Workflow Dashboard
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'scheduling:workload_calendar' %}">
                                <i class="fas fa-calendar-week"></i>Workload Calendar
                            </a>
                        </li>
                        <li class="dropdown-submenu">
                            <a class="dropdown-item" href="#">
                                <i class="fas fa-chart-line"></i>Workload Analysis
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="{% url 'scheduling:department_workload_report' %}">
                                        <i class="fas fa-chart-line"></i>Department Workload
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'scheduling:department_workload_chart' %}">
                                        <i class="fas fa-chart-bar"></i>Workload Chart
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'scheduling:workload_trend_chart' %}">
                                        <i class="fas fa-chart-line"></i>Trend Chart
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'scheduling:workload_heatmap' %}">
                                        <i class="fas fa-fire"></i>Workload Heatmap
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'scheduling:department_workload_balance' %}">
                                        <i class="fas fa-balance-scale"></i>Workload Balance
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'scheduling:scheduling_report' %}">
                                <i class="fas fa-file-alt"></i>Scheduling Report
                            </a>
                        </li>
                        <li class="dropdown-submenu">
                            <a class="dropdown-item" href="#">
                                <i class="fas fa-calendar-check"></i>Resource Availability
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="{% url 'scheduling:resource_availability_list' %}">
                                        <i class="fas fa-list"></i>Availability List
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'scheduling:resource_availability_calendar' %}">
                                        <i class="fas fa-calendar-alt"></i>Availability Calendar
                                    </a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </li>

                <!-- Reports Section -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-chart-bar"></i>Reports
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="reportsDropdown">
                        <li>
                            <a class="dropdown-item" href="{% url 'reports-dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i>Reports Dashboard
                            </a>
                        </li>
                        <li class="dropdown-divider"></li>
                        <li>
                            <h6 class="dropdown-header">Case Reports</h6>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'case_volume_report' %}">
                                <i class="fas fa-chart-line"></i>Case Volume
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'case_status_report' %}">
                                <i class="fas fa-tasks"></i>Case Status
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'case_progress_report' %}">
                                <i class="fas fa-project-diagram"></i>Case Progress
                            </a>
                        </li>
                        <li class="dropdown-divider"></li>
                        <li>
                            <h6 class="dropdown-header">Financial Reports</h6>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'revenue_report' %}">
                                <i class="fas fa-money-bill-wave"></i>Revenue Report
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'detailed_financial_report' %}">
                                <i class="fas fa-file-invoice-dollar"></i>Detailed Financial
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'dentist_financial_report' %}">
                                <i class="fas fa-user-md"></i>Dentist Financial
                            </a>
                        </li>
                        <li class="dropdown-divider"></li>
                        <li>
                            <h6 class="dropdown-header">Performance Reports</h6>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'dentist_leaderboard' %}">
                                <i class="fas fa-trophy"></i>Dentist Leaderboard
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'dentist_performance_report' %}">
                                <i class="fas fa-user-md"></i>Dentist Performance
                            </a>
                        </li>
                        <li class="dropdown-divider"></li>
                        <li>
                            <h6 class="dropdown-header">Inventory Reports</h6>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'inventory_management_report' %}">
                                <i class="fas fa-boxes"></i>Inventory Management
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'item_usage_report' %}">
                                <i class="fas fa-box-open"></i>Item Usage
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'item_inventory_report' %}">
                                <i class="fas fa-shopping-cart"></i>Item Inventory
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Contacts Section -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="contactsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-address-book"></i>Contacts
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="contactsDropdown">
                        <li>
                            <a class="dropdown-item" href="{% url 'Dentists:dentist_list' %}">
                                <i class="fas fa-user-md"></i>Dentists
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'patients:patient_list' %}">
                                <i class="fas fa-user"></i>Patients
                            </a>
                        </li>
                        <li>
                            <hr class="dropdown-divider">
                        </li>
                        <li>
                            <h6 class="dropdown-header">Notifications</h6>
                        </li>
                        <li>
                            <a class="dropdown-item" href="#">
                                <i class="fas fa-bell"></i>Notifications (Temporarily Unavailable)
                            </a>
                        </li>
                        {% comment %}
                        <!-- Temporarily commented out due to notifications app issues -->
                        <li>
                            <a class="dropdown-item" href="{% url 'notifications:notification_list' %}">
                                <i class="fas fa-bell"></i>Notifications
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'notifications:notification_create' %}">
                                <i class="fas fa-plus"></i>Create Notification
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'notifications:notification_preferences' %}">
                                <i class="fas fa-cog"></i>Notification Preferences
                            </a>
                        </li>
                        {% endcomment %}

                    </ul>
                </li>



                {% if user.is_superuser %}
                <!-- Administration Section -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-users-cog"></i>Administration
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="adminDropdown">
                        <li>
                            <h6 class="dropdown-header">System Management</h6>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'accounts:user_management' %}">
                                <i class="fas fa-users"></i>User Management
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'admin:index' %}">
                                <i class="fas fa-toolbox"></i>Admin Panel
                            </a>
                        </li>
                        <li>
                            <hr class="dropdown-divider">
                        </li>
                        <li>
                            <h6 class="dropdown-header">Settings</h6>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'accounts:system_settings' %}">
                                <i class="fas fa-cogs"></i>System Settings
                            </a>
                        </li>
                    </ul>
                </li>
                {% endif %}
            </ul>

            <!-- User Menu -->
            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <div class="user-icon">
                            {% if user.profile_image %}
                            <img src="{{ user.profile_image.url }}" alt="Profile" class="rounded-circle">
                            {% else %}
                            <i class="fas fa-user"></i>
                            {% endif %}
                        </div>
                        <span>{{ user.get_full_name|default:user.email }}</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li>
                            <h6 class="dropdown-header">Account Management</h6>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'accounts:profile' %}">
                                <i class="fas fa-user-circle"></i>My Profile
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'accounts:profile_settings' %}">
                                <i class="fas fa-cog"></i>Settings
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'accounts:qualifications' %}">
                                <i class="fas fa-certificate"></i>Qualifications
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'accounts:activity_log' %}">
                                <i class="fas fa-history"></i>Activity Log
                            </a>
                        </li>
                        <li>
                            <hr class="dropdown-divider">
                        </li>
                        <li>
                            <a class="dropdown-item" href="{% url 'accounts:change_password' %}">
                                <i class="fas fa-key"></i>Change Password
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item text-danger" href="{% url 'accounts:logout' %}">
                                <i class="fas fa-sign-out-alt"></i>Logout
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
            {% else %}
            <!-- Non-authenticated User Menu -->
            <ul class="navbar-nav ms-auto">
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'accounts:login' %}">
                        <i class="fas fa-sign-in-alt"></i>Login
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'accounts:register' %}">
                        <i class="fas fa-user-plus"></i>Register
                    </a>
                </li>
            </ul>
            {% endif %}
        </div>
    </div>
</nav>

<!-- Include Bootstrap JS (with Popper.js) -->
<script src="{% static 'js/bootstrap.bundle.min.js' %}"></script>

<script>
    // JavaScript për të menaxhuar dropdowns në mobile dhe multi-level dropdowns
    document.addEventListener('DOMContentLoaded', function() {
        // Funksion për të mbyllur të gjitha dropdown menus
        function closeAllDropdowns() {
            document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                menu.classList.remove('show');
            });
        }

        // Mbyll dropdown kur klikohet jashtë
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.dropdown')) {
                closeAllDropdowns();
            }
        });

        // Menaxhimi i dropdown në mobile
        if (window.innerWidth < 992) {
            // Dropdown kryesore
            const dropdowns = document.querySelectorAll('.dropdown');
            dropdowns.forEach(dropdown => {
                const dropdownToggle = dropdown.querySelector('.dropdown-toggle');
                const dropdownMenu = dropdown.querySelector('.dropdown-menu');

                if (dropdownToggle && dropdownMenu) {
                    dropdownToggle.addEventListener('click', function(event) {
                        event.preventDefault();
                        event.stopPropagation(); // Parandalon mbylljen e collapse menu

                        // Mbyll të gjitha dropdown menus e tjera në të njëjtin nivel
                        const siblingDropdowns = dropdown.parentElement.querySelectorAll('.dropdown');
                        siblingDropdowns.forEach(sibling => {
                            if (sibling !== dropdown) {
                                const siblingMenu = sibling.querySelector('.dropdown-menu');
                                if (siblingMenu) siblingMenu.classList.remove('show');
                            }
                        });

                        dropdownMenu.classList.toggle('show');
                    });
                }
            });

            // Dropdown submenu
            const dropdownSubmenus = document.querySelectorAll('.dropdown-submenu');
            dropdownSubmenus.forEach(submenu => {
                const submenuToggle = submenu.querySelector('a');
                const submenuDropdown = submenu.querySelector('.dropdown-menu');

                if (submenuToggle && submenuDropdown) {
                    submenuToggle.addEventListener('click', function(event) {
                        event.preventDefault();
                        event.stopPropagation();

                        // Mbyll të gjitha dropdown submenus e tjera në të njëjtin nivel
                        const siblingSubmenus = submenu.parentElement.querySelectorAll('.dropdown-submenu');
                        siblingSubmenus.forEach(sibling => {
                            if (sibling !== submenu) {
                                const siblingMenu = sibling.querySelector('.dropdown-menu');
                                if (siblingMenu) siblingMenu.classList.remove('show');
                            }
                        });

                        submenuDropdown.classList.toggle('show');
                    });
                }
            });
        } else {
            // Desktop behavior - hover effect is handled by CSS
            // But we need to handle click events for touch devices
            const dropdownSubmenus = document.querySelectorAll('.dropdown-submenu');
            dropdownSubmenus.forEach(submenu => {
                const submenuToggle = submenu.querySelector('a');

                if (submenuToggle) {
                    submenuToggle.addEventListener('click', function(event) {
                        // Only prevent default if it has a submenu
                        if (submenu.querySelector('.dropdown-menu')) {
                            event.preventDefault();
                        }
                    });
                }
            });
        }
    });
</script>

{% endblock %}
