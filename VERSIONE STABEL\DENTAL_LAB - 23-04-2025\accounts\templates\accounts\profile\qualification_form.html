{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}
    {% if object %}
        {% trans "Edit Qualification" %}
    {% else %}
        {% trans "Add Qualification" %}
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12 col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        {% if object %}
                            {% trans "Edit Qualification" %}
                        {% else %}
                            {% trans "Add New Qualification" %}
                        {% endif %}
                    </h3>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.title|crispy }}
                            </div>
                            <div class="col-md-6">
                                {{ form.institution|crispy }}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                {{ form.date_obtained|crispy }}
                            </div>
                            <div class="col-md-6">
                                {{ form.expiry_date|crispy }}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                {{ form.certificate_number|crispy }}
                            </div>
                            <div class="col-md-6">
                                {{ form.document|crispy }}
                            </div>
                        </div>

                        {{ form.description|crispy }}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                {% if object %}
                                    {% trans "Update Qualification" %}
                                {% else %}
                                    {% trans "Add Qualification" %}
                                {% endif %}
                            </button>
                            <a href="{% url 'accounts:qualifications' %}" class="btn btn-secondary">
                                {% trans "Cancel" %}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Datepicker initialization for date fields
        $('.dateinput').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true,
            todayHighlight: true
        });

        // File input customization
        $('.custom-file-input').on('change', function() {
            let fileName = $(this).val().split('\\').pop();
            $(this).next('.custom-file-label').html(fileName);
        });
    });
</script>
{% endblock %}

{% block extra_css %}
<style>
    .card {
        margin-top: 20px;
        margin-bottom: 20px;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .custom-file-label {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    .datepicker {
        z-index: 1060;
    }
</style>
{% endblock %}