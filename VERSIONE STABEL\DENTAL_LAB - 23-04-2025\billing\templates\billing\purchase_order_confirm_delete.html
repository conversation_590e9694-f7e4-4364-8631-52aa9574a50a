{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Confirm Delete Purchase Order" %} #{{ purchase_order.id }} | {% trans "Billing" %}{% endblock %}

{% block extra_css %}
<style>
  :root {
    /* Primary Colors - Fresher, Lighter Palette */
    --primary: #4285F4; /* Google Blue */
    --primary-light: rgba(66, 133, 244, 0.1);
    --secondary: #8ab4f8;
    --success: #34A853; /* Google Green */
    --success-light: rgba(52, 168, 83, 0.1);
    --danger: #EA4335; /* Google Red */
    --danger-light: rgba(234, 67, 53, 0.1);
    --warning: #FBBC05; /* Google Yellow */
    --warning-light: rgba(251, 188, 5, 0.1);
    --info: #46bdc6;
    --info-light: rgba(70, 189, 198, 0.1);
    --dark: #3c4043;
    --light: #f8f9fa;
    --white: #ffffff;
    
    /* Background and Text Colors */
    --bg-main: #f8f9fa;
    --text-main: #202124;
    --card-bg: #ffffff;
    --border-color: rgba(0,0,0,0.08);
    
    /* UI Elements */
    --shadow-sm: 0 1px 2px rgba(0,0,0,0.05);
    --shadow: 0 4px 6px rgba(0,0,0,0.05);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.05);
    --border-radius: 8px;
    --transition: all 0.3s ease;
  }

  /* Dark Mode Colors - Softer Dark Theme */
  [data-theme="dark"] {
    --primary: #8ab4f8; /* Lighter blue for dark mode */
    --primary-light: rgba(138, 180, 248, 0.15);
    --success: #81c995; /* Lighter green for dark mode */
    --success-light: rgba(129, 201, 149, 0.15);
    --danger: #f28b82; /* Lighter red for dark mode */
    --danger-light: rgba(242, 139, 130, 0.15);
    --warning: #fdd663; /* Lighter yellow for dark mode */
    --warning-light: rgba(253, 214, 99, 0.15);
    --info: #78d9ec;
    --info-light: rgba(120, 217, 236, 0.15);

    --dark: #e8eaed;
    --light: #3c4043;
    --white: #202124;

    --bg-main: #202124;
    --text-main: #e8eaed;
    --card-bg: #292a2d;
    --border-color: rgba(255,255,255,0.08);
  }

  body {
    background-color: var(--bg-main);
    color: var(--text-main);
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Card styles */
  .card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    margin-bottom: 1.5rem;
  }

  .card:hover {
    box-shadow: var(--shadow);
  }

  .card-header {
    background-color: var(--danger-light);
    color: var(--danger);
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
  }

  /* Delete confirmation styles */
  .delete-warning {
    color: var(--danger);
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
  }

  .delete-icon {
    font-size: 3rem;
    color: var(--danger);
    margin-bottom: 1rem;
  }

  .delete-details {
    background-color: var(--danger-light);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .delete-details-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--danger);
  }

  .delete-details-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
  }

  .delete-details-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }

  .delete-details-label {
    font-weight: 500;
  }

  .delete-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
  }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
  <!-- Page Header -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div class="d-flex align-items-center">
      <a href="{% url 'billing:purchase_order_detail' purchase_order.id %}" class="btn btn-outline-primary me-3">
        <i class="fas fa-arrow-left"></i>
      </a>
      <div>
        <h1 class="mb-0">{% trans "Confirm Delete" %}</h1>
        <p class="text-muted mb-0">{% trans "Purchase Order" %} #{{ purchase_order.id }}</p>
      </div>
    </div>
  </div>

  <div class="card">
    <div class="card-header">
      <h5 class="mb-0">{% trans "Delete Purchase Order" %}</h5>
    </div>
    <div class="card-body text-center">
      <i class="fas fa-exclamation-triangle delete-icon"></i>
      <h3 class="delete-warning">{% trans "Are you sure you want to delete this purchase order?" %}</h3>
      <p class="mb-4">{% trans "This action cannot be undone. All items associated with this purchase order will also be deleted." %}</p>

      <div class="delete-details">
        <h5 class="delete-details-title">{% trans "Purchase Order Details" %}</h5>
        <div class="delete-details-item">
          <span class="delete-details-label">{% trans "Purchase Order #" %}:</span>
          <span>{{ purchase_order.id }}</span>
        </div>
        <div class="delete-details-item">
          <span class="delete-details-label">{% trans "Supplier" %}:</span>
          <span>{{ purchase_order.supplier.name }}</span>
        </div>
        <div class="delete-details-item">
          <span class="delete-details-label">{% trans "Order Date" %}:</span>
          <span>{{ purchase_order.order_date|date:"Y-m-d" }}</span>
        </div>
        <div class="delete-details-item">
          <span class="delete-details-label">{% trans "Status" %}:</span>
          <span>{{ purchase_order.get_status_display }}</span>
        </div>
        <div class="delete-details-item">
          <span class="delete-details-label">{% trans "Total Amount" %}:</span>
          <span>{{ purchase_order.total_amount }}</span>
        </div>
        <div class="delete-details-item">
          <span class="delete-details-label">{% trans "Number of Items" %}:</span>
          <span>{{ purchase_order.purchaseorderitem_set.count }}</span>
        </div>
      </div>

      <form method="post">
        {% csrf_token %}
        <div class="delete-actions">
          <a href="{% url 'billing:purchase_order_detail' purchase_order.id %}" class="btn btn-outline-secondary">
            {% trans "Cancel" %}
          </a>
          <button type="submit" class="btn btn-danger">
            <i class="fas fa-trash me-1"></i> {% trans "Delete Purchase Order" %}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}
