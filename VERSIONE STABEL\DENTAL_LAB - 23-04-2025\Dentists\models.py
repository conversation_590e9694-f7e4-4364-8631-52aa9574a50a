from django.db import models
from django.conf import settings
from case.models import Case
from django.db.models.signals import post_save
from django.dispatch import receiver
from accounts.models import CustomUser

class Dentist(models.Model):
    first_name = models.Char<PERSON><PERSON>(max_length=50)
    last_name = models.Char<PERSON><PERSON>(max_length=50)
    clinic_name = models.CharField(max_length=100)
    phone_number = models.CharField(max_length=15)
    cases = models.ManyToManyField('case.Case', related_name='dentists', blank=True)
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='dentist_profile',
        null=True,
        blank=True
    )

    class Meta:
        unique_together = ('first_name', 'last_name', 'clinic_name', 'phone_number')
        ordering = ['first_name', 'last_name']

    def __str__(self):
        return f'{self.first_name} {self.last_name}'

    def get_full_name(self):
        return str(self)

    def is_dentist(self):
        return hasattr(self, 'dentist_profile')

@receiver(post_save, sender=CustomUser)
def create_dentist_profile(sender, instance, created, **kwargs):
    # Krijojmë profil dentisti vetëm nëse është krijuar një përdorues i ri me user_type=2
    # dhe nuk ka ende një profil dentisti
    if created and instance.user_type == 2 and not hasattr(instance, 'dentist_profile'):
        try:
            # Krijojmë profilin e dentistit me të dhënat nga përdoruesi
            Dentist.objects.create(
                user=instance,
                first_name=instance.first_name,
                last_name=instance.last_name,
                phone_number=instance.phone_number or '',
                clinic_name=''
            )
            print(f"Created dentist profile for user {instance.email}")
        except Exception as e:
            print(f"Error creating dentist profile: {str(e)}")

@receiver(post_save, sender=CustomUser)
def save_dentist_profile(sender, instance, created, **kwargs):
    # Përditësojmë profilin e dentistit vetëm nëse nuk është krijim i ri
    # dhe përdoruesi është i tipit dentist dhe ka një profil dentisti
    if not created and instance.user_type == 2 and hasattr(instance, 'dentist_profile'):
        try:
            # Përditësojmë të dhënat e profilit të dentistit me të dhënat nga përdoruesi
            dentist = instance.dentist_profile
            # Përditësojmë vetëm nëse të dhënat janë të ndryshme
            if (dentist.first_name != instance.first_name or
                dentist.last_name != instance.last_name or
                (dentist.phone_number != instance.phone_number and instance.phone_number)):
                dentist.first_name = instance.first_name
                dentist.last_name = instance.last_name
                if instance.phone_number:
                    dentist.phone_number = instance.phone_number
                dentist.save()
                print(f"Updated dentist profile for user {instance.email}")
        except Exception as e:
            print(f"Error updating dentist profile: {str(e)}")
