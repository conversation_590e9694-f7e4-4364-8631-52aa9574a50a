{% extends "base.html" %}
{% load static %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css" rel="stylesheet">
<style>
    .dashboard-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease-in-out;
        margin-bottom: 20px;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
    }
    .summary-card {
        height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
    .progress {
        height: 10px;
        border-radius: 5px;
    }
    .department-title {
        font-size: 1.2rem;
        font-weight: bold;
        border-bottom: 2px solid #007bff;
        padding-bottom: 10px;
        margin-bottom: 15px;
    }
    .chart-container {
        height: 300px;
    }
    .alert-icon {
        font-size: 1.5rem;
        margin-right: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <h1 class="text-center mb-4">Paneli i Menaxhimit të Laboratorit Dentar</h1>

    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card dashboard-card bg-primary text-white summary-card">
                <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                <h5>Totali i Rasteve</h5>
                <h2>{{ total_cases }}</h2>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card dashboard-card bg-warning text-dark summary-card">
                <i class="fas fa-hourglass-half fa-2x mb-2"></i>
                <h5>Raste në Progres</h5>
                <h2>{{ total_in_progress }}</h2>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card dashboard-card bg-success text-white summary-card">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h5>Raste të Përfunduara (30 ditë)</h5>
                <h2>{{ total_completed }}</h2>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card dashboard-card bg-danger text-white summary-card">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h5>Raste të Vonuara</h5>
                <h2>{{ total_delayed }}</h2>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card dashboard-card">
                <div class="card-body">
                    <h3 class="department-title">Shpërndarja e Rasteve sipas Departamenteve</h3>
                    <div class="chart-container">
                        <canvas id="departmentCasesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card dashboard-card">
                <div class="card-body">
                    <h3 class="department-title">Koha Mesatare e Përfundimit (Ditë)</h3>
                    <div class="chart-container">
                        <canvas id="avgCompletionTimeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card dashboard-card">
                <div class="card-body">
                    <h3 class="department-title">Trendi i Rasteve të Reja (30 ditët e fundit)</h3>
                    <div class="chart-container">
                        <canvas id="newCasesTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card dashboard-card">
                <div class="card-body">
                    <h3 class="department-title">Top 5 Dentistët me Raste Aktive</h3>
                    <ul class="list-group">
                        {% for dentist in top_dentists %}
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            {{ dentist.get_full_name }}
                            <span class="badge bg-primary rounded-pill">{{ dentist.active_cases }}</span>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card dashboard-card">
                <div class="card-body">
                    <h3 class="department-title">Ngarkesa e Punës sipas Departamenteve</h3>
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Departamenti</th>
                                <th>Raste Totale</th>
                                <th>Në Progres</th>
                                <th>Të Vonuara</th>
                                <th>Koha Mesatare e Përfundimit</th>
                                <th>Ngarkesa</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for dept in dashboard_data %}
                            <tr>
                                <td>{{ dept.department }}</td>
                                <td>{{ dept.total_cases }}</td>
                                <td>{{ dept.cases_in_progress }}</td>
                                <td>{{ dept.cases_delayed }}</td>
                                <td>{{ dept.avg_completion_time }} ditë</td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar bg-info" role="progressbar"
                                             style="width: {% widthratio dept.cases_in_progress dept.total_cases 100 %}%;"
                                             aria-valuenow="{% widthratio dept.cases_in_progress dept.total_cases 100 %}"
                                             aria-valuemin="0" aria-valuemax="100">
                                            {% widthratio dept.cases_in_progress dept.total_cases 100 %}%
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card dashboard-card">
                <div class="card-body">
                    <h3 class="department-title">Raste që Kërkojnë Vëmendje</h3>
                    <ul class="list-group">
                        {% for case in urgent_cases %}
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>
                                <i class="fas fa-exclamation-circle text-danger alert-icon"></i>
                                Rasti #{{ case.case_number }} - {{ case.dentist.get_full_name }}
                            </span>
                            <span class="badge bg-danger rounded-pill">{{ case.days_overdue }} ditë vonesë</span>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card dashboard-card">
                <div class="card-body">
                    <h3 class="department-title">Nivelet e Inventarit</h3>
                    <ul class="list-group">
                        {% for item in low_inventory_items %}
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>
                                <i class="fas fa-box text-warning alert-icon"></i>
                                {{ item.name }}
                            </span>
                            <span class="badge bg-warning text-dark rounded-pill">{{ item.quantity }} mbetur</span>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var ctx = document.getElementById('departmentCasesChart').getContext('2d');
        new Chart(ctx, {
            type: 'pie',
            data: {
                labels: [{% for dept in dashboard_data %}'{{ dept.department }}',{% endfor %}],
                datasets: [{
                    data: [{% for dept in dashboard_data %}{{ dept.total_cases }},{% endfor %}],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 206, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(153, 102, 255, 0.8)'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Shpërndarja e Rasteve sipas Departamenteve'
                    }
                }
            }
        });

        var ctx2 = document.getElementById('avgCompletionTimeChart').getContext('2d');
        new Chart(ctx2, {
            type: 'bar',
            data: {
                labels: [{% for dept in dashboard_data %}'{{ dept.department }}',{% endfor %}],
                datasets: [{
                    label: 'Ditë (nga pranimi deri në shipping)',
                    data: [{% for dept in dashboard_data %}{{ dept.avg_completion_time }},{% endfor %}],
                    backgroundColor: 'rgba(75, 192, 192, 0.8)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Ditë'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Koha Mesatare e Përfundimit sipas Departamenteve'
                    }
                }
            }
        });

        var ctx3 = document.getElementById('newCasesTrendChart').getContext('2d');
        new Chart(ctx3, {
            type: 'line',
            data: {
                labels: {{ new_cases_trend.dates|safe }},
                datasets: [{
                    label: 'Raste të Reja',
                    data: {{ new_cases_trend.counts|safe }},
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Numri i Rasteve'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Trendi i Rasteve të Reja (30 ditët e fundit)'
                    }
                }
            }
        });
    });
</script>
{% endblock %}