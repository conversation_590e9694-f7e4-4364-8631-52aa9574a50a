<!DOCTYPE html>
<html>
<head>
  <style>
    svg {
      width: 100%;
      height: auto;
      cursor: pointer;
    }
    svg.clicked {
      stroke: red;
    }
  </style>
</head>
<body>

<svg version="1.1" x="0px" y="0px" viewBox="0 0 535 712"><path d="M63,295.5c-10.2-2-16.9-3.3-24.2-0.7c-7.5,2.5-9,8.2-10.2,12.6c-1.4,9.6,0.9,15.8,4.3,21   c1.1,5.1,1.6,10.2,13.2,13.8c10.1,4.8,20.2,3.1,30.4,1.7c8.6-1.3,14.1-4.2,16.8-8.5l4.2-7.4c1.5-4.6,3.4-8.9,0.9-14.8   c0.4-3.9-4.6-7.5-12.6-10.9C80.4,298.1,72.1,296.3,63,295.5L63,295.5z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>18</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 0.9795 321.1232)">18</text><path d="M39.3,264.3c-2.4,15.7,5.1,15.3,9.2,20.7c10.2,6.8,17.3,5,25.4,5.8c20.3,2,27.5-8.6,31.6-22   c1.8-8-1.5-10.1-5.2-14.7c-3.7-4.6-9.3-9.2-16.4-11.6c-22.6-12-37.1-8.6-45.2,6.8C36.7,255.4,36.3,260.7,39.3,264.3L39.3,264.3z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>17</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 10.5629 257.8733)">17</text><path d="M54.3,195.8c-4.3,16.3,3.2,28.7,23,37.1c5.6,2.5,9.4,7.7,18.9,3.5c2-1,4.3-3,6.9-5.4   c2.2-3.6,6.3-8.1,10.6-12.8c7-6.5,9-13.8-3.9-24c-4.2-3.1-7.3-6.3-10.4-9.4c-11.3-6.2-22.6-12.4-37.1-0.2   C57.8,186.3,54.6,189.5,54.3,195.8z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>16</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 29.1462 194.5399)">16</text><path d="M96.7,141c8.3,0.7,15.7-0.1,29,9.4c6.8,4.7,7.3,11.1,4.5,18.3c-3.4,9-11.6,6.8-17.7,9.5   c-3.2,0.3-6.2,0.3-8.8-0.2c-1.8-1-4.4-1.2-7.4-1.1c-8.5-2.3-13-7.8-18-13C73.6,149.6,81.1,142.9,96.7,141L96.7,141z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>15</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 52.7295 144.4566)">15</text><path d="M114.1,107.3c5.9-1.6,11.6-2.3,15.8,2c3.1,1.9,5.4,3.6,13,4.5c7.1,2.7,6.3,4.6,7.4,6.8   c1.5,3.8,1.1,6.7,0.1,9.4c-1.2,5-4.5,8.1-10.9,8.3c-3.5,1.1-7.9,1.6-14.3,0.7c-4.1,1.4-25,1.1-23.7-19.5   C101.6,114.9,102.5,110.3,114.1,107.3L114.1,107.3z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>14</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 80.8129 102.8733)">14</text><path d="M141.7,105.5c2.8,1.1,0.7,0.6,8.9,3.5c4.5,0.3,6.4,3.6,17-3.1c3.6-2.7,3.9-7.7,3.1-13.5   c-1.6-5.2-0.9-9.5-7.4-16.8c-1.9-1.9-4.7-4.9-12.5-4.6c-2.5,0.2-5.2,0.4-8.1,0.1c-3.2,0.5-6.4,0.1-9.7,7.7   c-3.7,4.9-4.8,9.9-3.8,15.1C130.2,99.3,133.8,103.3,141.7,105.5L141.7,105.5z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>13</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 114.8133 66.7066)">13</text><path d="M161.8,54.4c-2.2,2.3-3.2,4.7-2.8,7.6c1.8,4.3,3.3,8.9,8.6,12.1c2.5,1.4,5.1,3.1,8,2.7   l7.5,1.5c4.6,0.7,8.8,0.8,11.9-1c5.1-1.8,8-5.4,10.4-9.4c0.3-4.7,2.2-4.6,0.6-15.6c-0.4-1.2-0.2-2-3.6-5.5l-5-4.2   c-1.3-1.9-4-1.7-7.2-0.7c-9.1,2.7-4.7-0.8-26.2,11L161.8,54.4z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>12</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 160.4798 34.0399)">12</text><path d="M206.2,35.8c0.1-7.2,5.2-6.6,9.1-8.1l10.7-2c11.3-2.9,15.2-1.5,21-1.1l9.9,2.2   c3.7,1.5,7.6,5.3,5.8,9.3l-2.5,5.5l-13.8,17.5c-5.6,6.7-12.3,6.8-19,7.5c-8.2-2-11.2-5.6-13.1-9.7c-1.5-3.7-7.1-12.1-7.2-12.9   C205,41.1,206.3,38.5,206.2,35.8z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>11</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 216.4799 14.5399)">11</text><path d="M324.6,44.2c-0.1,0.8-5.7,9.1-7.2,12.9c-2,4.1-5,7.7-13.1,9.7c-6.7-0.7-13.4-0.8-19-7.5   l-13.8-17.5l-2.5-5.5c-1.8-4,2.1-7.7,5.8-9.3l9.9-2.2c5.8-0.3,9.7-1.7,21,1.1l10.7,2c3.9,1.5,9,0.9,9.1,8.1   C325.5,38.6,326.8,41.1,324.6,44.2z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>21</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 296.23 14.2066)">21</text><path d="M367.6,53c-21.4-11.8-17-8.3-26.2-11c-3.2-1-5.9-1.1-7.2,0.7l-5,4.2c-3.3,3.6-3.2,4.3-3.6,5.5   c-1.6,11,0.2,10.8,0.6,15.6c2.4,4,5.4,7.6,10.4,9.4c3.1,1.8,7.3,1.6,11.9,1l7.5-1.5c2.9,0.4,5.5-1.3,8-2.7   c5.4-3.2,6.8-7.7,8.6-12.1c0.4-2.9-0.6-5.3-2.8-7.6L367.6,53z " stroke="#810c0c" stroke-width="3" fill="#d5d5d5" stroke-dasharray=""><title>22</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 358.9796 37.3733)">22</text><path d="M390.1,105.6c7.9-2.2,11.6-6.3,12.5-11.6c1-5.1-0.1-10.2-3.8-15.1c-3.3-7.6-6.5-7.2-9.7-7.7   c-2.9,0.3-5.5,0.2-8.1-0.1c-7.7-0.3-10.6,2.7-12.5,4.6c-6.5,7.3-5.8,11.6-7.4,16.8c-0.8,5.8-0.5,10.8,3.1,13.5   c10.7,6.7,12.6,3.4,17,3.1C389.4,106.2,387.3,106.7,390.1,105.6L390.1,105.6z " stroke="#810c0c" stroke-width="3" fill="#d5d5d5" stroke-dasharray=""><title>23</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 403.5629 68.7899)">23</text><path d="M417.7,107.4c11.6,2.9,12.5,7.5,12.7,12.2c1.3,20.6-19.6,21-23.7,19.5   c-6.4,0.9-10.8,0.4-14.3-0.7c-6.5-0.2-9.8-3.4-10.9-8.3c-1-2.7-1.4-5.6,0.1-9.4c1.1-2.2,0.2-4.1,7.4-6.8c7.6-1,9.9-2.7,13-4.5   C406.1,105.1,411.8,105.8,417.7,107.4L417.7,107.4z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>24</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 438.4796 106.9566)">24</text><path d="M435.1,141.1c15.7,1.9,23.2,8.6,18.4,22.7c-5,5.1-9.5,10.7-18,13c-3-0.1-5.6,0.2-7.4,1.1   c-2.6,0.6-5.6,0.6-8.8,0.2c-6.1-2.7-14.3-0.5-17.7-9.5c-2.9-7.2-2.4-13.5,4.5-18.3C419.4,141,426.7,141.7,435.1,141.1L435.1,141.1z " stroke="#810c0c" stroke-width="3" fill="#d5d5d5" stroke-dasharray=""><title>25</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 463.3129 145.7899)">25</text><path d="M469.6,184.6c-14.5-12.2-25.8-6-37.1,0.2c-3.1,3.1-6.2,6.4-10.4,9.4c-13,10.1-10.9,17.5-3.9,24   c4.3,4.7,8.5,9.2,10.6,12.8c2.5,2.5,4.8,4.4,6.9,5.4c9.5,4.2,13.4-1,18.9-3.5c19.8-8.3,27.3-20.7,23-37.1   C477.2,189.6,474,186.4,469.6,184.6z " stroke="#810c0c" stroke-width="3" fill="#d5d5d5" stroke-dasharray=""><title>26</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 485.2305 190.8733)">26</text><path d="M492.5,264.4c3.1-3.6,2.7-8.9,0.6-14.9c-8.1-15.5-22.6-18.8-45.2-6.8   c-7.1,2.4-12.7,7-16.4,11.6s-7,6.7-5.2,14.7c4.2,13.5,11.3,24,31.6,22c8.1-0.8,15.2,1,25.4-5.8   C487.3,279.7,494.9,280.1,492.5,264.4L492.5,264.4z " stroke="#810c0c" stroke-width="3" fill="#d5d5d5" stroke-dasharray=""><title>27</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 504.8972 255.8733)">27</text><path d="M468.8,295.6c-9.1,0.7-17.4,2.5-22.9,6.7c-7.9,3.4-13,7-12.6,10.9c-2.5,6-0.6,10.3,0.9,14.8   l4.2,7.4c2.7,4.3,8.2,7.2,16.8,8.5c10.2,1.4,20.3,3.1,30.4-1.7c11.6-3.6,12.1-8.7,13.2-13.8c3.4-5.2,5.7-11.4,4.3-21   c-1.2-4.4-2.7-10-10.2-12.6C485.7,292.3,479,293.6,468.8,295.6L468.8,295.6z " stroke="#810c0c" stroke-width="3" fill="#d5d5d5" stroke-dasharray=""><title>28</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 513.7305 318.4566)">28</text><path d="M498.9,403.7c0.9-5.6,4.5-6.1,1.7-18.5c-2-8.7-6-17.5-17.4-20.4c-6.4-1.6-20-5.3-27.5,0.1   c-2.3,1.6-4,2.4-5.4,3.4c-3.4,2.4-10.4,2.7-11.8,13.7c-0.5,4,1,5.7-0.9,14.5c-0.7,3.3-3.6,6-0.4,11.8c1.8,3.3,2.7,4.7,9.5,8.5   c2.2,1.3,24.1,3.2,29.2,3.1c5.1-0.1,6.4,2,13.7-5.2C492.9,411.3,495.4,409.7,498.9,403.7L498.9,403.7z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>38</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 514.8138 396.0399)">38</text><path d="M473.2,425.7c4.9,2.1,4,3.7,9,7.8c3.6,3,6.8,7.6,7.4,17.2c0.4,6.7,3.3,14.3-2.9,20.8   c-3.7,3.9-1.4,4.8-13.4,9.9c-5.9,2.5-10.3,7-19.6,4.3c-4.4-1.3-5.8-1.3-13.7-3.5c-3.2-0.9-5.6-2.5-7.4-6.2   c-1.4-2.7-3.6-5.8-3.9-10.9c-0.2-4.3,1.2-6,1.7-8.3c1.4-5.8,0.3-8.2,0-10c-0.5-2.9,1-5.6,2.7-8.8c1.5-3,1.9-5.9,8.1-9.7   C446.8,424.8,456,421.6,473.2,425.7L473.2,425.7z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>37</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 503.8138 468.0399)">37</text><path d="M430,489.5c-3.1-0.2-6.5,0.3-9.5,2.7c-1.3,1-3.3,3.5-4.4,4.6c-3,2.8-4.4,5.5-4.2,8.1   c0.3,4.7-1.8,9-4.6,14.2c-2.9,5.4-3.3,11.1,0.2,15.6c2.7,3.6,5.1,7.8,10.9,10.2c5.9,2.4,11.2,5.4,18.4,6.1   c4.7,0.4,6.8,2.3,14.1-1.5c2.7-1.4,7.7-4.7,10.3-6.3c1.7-1.1,4.2-1.5,6.1-9c1.6-6.5,4-12.9,0.2-20.2c-1.8-3.4-4.5-10.5-6.5-13.7   c-2.6-3.9-3.2-7.6-9.8-9.7c-3.8-1.2-6.5-3.1-13.8-2.3C435.5,488.7,431.9,489.6,430,489.5z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>36</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 478.8138 542.0399)">36</text><path d="M403.2,552.5c-13.1,5.8-13.4,8.9-13.2,13.3c0.3,5.4,0.7,7.3,0.7,9.8c-0.1,5.4,3.4,7,6,9.8   c7.1,7.4,11.6,5.6,17.1,5.8c5,0.2,8-1.4,10.9-2.1c8.1-1.9,7.7-4.1,10.5-6.8c4.5-4.2,4.7-10.9,2.2-17.7c-1.5-4.2-4.8-7.7-8.8-10.2   c-2.1-1.3-6.7-3.1-8.9-4C412.9,547.7,405,551.3,403.2,552.5L403.2,552.5z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>35</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 447.4796 598.3732)">35</text><path d="M401.4,596.4c4.7,5.5,5.9,9.5,5.3,12.7c-1.1,5.7-2.1,11.9-9,14c-11,3.4-13.6,0.8-18-0.6   c-5.3-1.8-8.7-5.3-12.2-8.1c-2-1.6-2.2-2.7-2.2-4c0-2.5-0.3-4.2-0.5-6.1c-0.4-3.9,0.7-7.1,2.8-9.7c2.4-3.1,5.6-5.5,11-5.7   c2.3-0.1,6.9-0.1,9.3-0.2C393.5,588.4,397.3,592.5,401.4,596.4L401.4,596.4z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>34</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 412.4796 642.0399)">34</text><path d="M359.1,619.4c-7.5-0.9-13.8-0.9-15.3,4.2c-0.8,2.6-3,7.5-3.8,10.1c-1.8,6.5-0.5,6.4,0.6,8.5   c0.9,1.8,4.2,4.5,5.6,5.9c2.1,2.1,4.5,3.9,9.5,3.9c7.5-0.1,15.5-0.3,16.6-10c0.3-2.8,1-8.5,0.7-11.3   C372.4,625.1,369.7,620,359.1,619.4L359.1,619.4z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>33</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 376.1462 670.3733)">33</text><path d="M314.4,641c2.5-6.4,9.2-6.9,13.1-3.4c1.7,1.5,4.5,5.2,6.1,6.8c1.7,1.6,5.8,4.2,7.1,6.1   c1.7,2.5,4,2.8,0.6,13c-1.9,5.6-5.9,8-10.7,9.2c-6.2,1.6-11.9,1.2-16.3-2c-3.6-2.6-9.1-4.6-7.1-11.3c1-3.3,2-3.5,4.4-9.8   C312.8,646.8,313.5,645.4,314.4,641L314.4,641z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>32</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 333.1467 693.3733)">32</text><path d="M296.3,658.6c2.3,3.5,4,7.3,7.7,9.9c1.8,1.2,4.5,3.3,1.7,10.1c-1.3,3.3-5.2,5.3-10.5,5.2   c-4.3-0.1-8-0.4-14.7-0.5c-3.3-0.1-6.5,0.8-10.3-3.5c-1.5-1.8-4.3-3.6-0.4-9.6c2-3.1,6.8-8.6,9.6-11c2.4-2.1,2.8-4.4,9.2-4.7   C293.7,654,294.7,656.5,296.3,658.6L296.3,658.6z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>31</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 284.1467 706.0399)">31</text><path d="M235.5,658.5c1.5-2,2.5-4.5,7.6-4.2c6.4,0.4,6.8,2.7,9.2,4.7c2.8,2.4,7.6,7.9,9.6,11   c4,6,1.1,7.9-0.4,9.6c-3.7,4.3-6.9,3.5-10.3,3.5c-6.7,0.1-10.4,0.4-14.7,0.5c-5.3,0.1-9.2-1.9-10.5-5.2c-2.8-6.8-0.1-8.9,1.7-10.1   C231.6,665.8,233.3,662,235.5,658.5L235.5,658.5z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>41</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 231.1466 706.2066)">41</text><path d="M217.4,640.9c1,4.4,1.6,5.8,2.7,8.6c2.4,6.3,3.4,6.5,4.4,9.8c2.1,6.7-3.5,8.7-7.1,11.3   c-4.4,3.2-10.1,3.6-16.3,2c-4.8-1.2-8.8-3.6-10.7-9.2c-3.4-10.2-1.2-10.5,0.6-13c1.3-1.9,5.5-4.4,7.1-6.1c1.6-1.6,4.4-5.3,6.1-6.8   C208.2,634,214.9,634.5,217.4,640.9L217.4,640.9z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>42</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 184.4799 694.0399)">42</text><path d="M172.7,619.3c-10.6,0.6-13.2,5.7-13.9,11.3c-0.3,2.8,0.3,8.5,0.7,11.3   c1.1,9.7,9.1,9.9,16.6,10c5,0,7.4-1.8,9.5-3.9c1.5-1.4,4.7-4.1,5.6-5.9c1.1-2.2,2.4-2,0.6-8.5c-0.7-2.6-3-7.5-3.8-10.1   C186.5,618.4,180.2,618.4,172.7,619.3L172.7,619.3z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>43</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 140.1467 670.5399)">43</text><path d="M130.4,596.3c4-3.9,7.9-8,13.6-7.8c2.3,0.1,6.9,0.1,9.3,0.2c5.4,0.2,8.6,2.6,11,5.7   c2,2.6,3.1,5.8,2.8,9.7c-0.2,1.9-0.5,3.7-0.5,6.1c0,1.3-0.2,2.3-2.2,4c-3.5,2.8-6.8,6.3-12.2,8.1c-4.4,1.5-7,4-18,0.6   c-7-2.1-7.9-8.2-9-14C124.5,605.8,125.8,601.8,130.4,596.3L130.4,596.3z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>44</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 103.3134 641.7066)">44</text><path d="M128.6,552.4c-1.8-1.2-9.7-4.8-16.5-2c-2.3,0.9-6.8,2.7-8.9,4c-4,2.5-7.3,6-8.8,10.2   c-2.5,6.8-2.3,13.4,2.2,17.7c2.8,2.6,2.4,4.8,10.5,6.8c2.9,0.7,5.8,2.2,10.9,2.1c5.5-0.2,10,1.6,17.1-5.8c2.6-2.7,6.1-4.3,6-9.8   c0-2.6,0.4-4.4,0.7-9.8C142,561.3,141.6,558.2,128.6,552.4L128.6,552.4z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>45</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 67.3129 600.8732)">45</text><path d="M94.5,488.4c-7.3-0.8-9.9,1.1-13.8,2.3c-6.7,2.1-7.3,5.8-9.8,9.7c-2.1,3.2-4.8,10.3-6.5,13.7   c-3.8,7.3-1.4,13.7,0.2,20.2c1.9,7.4,4.3,7.8,6.1,9c2.5,1.6,7.6,4.9,10.3,6.3c7.3,3.7,9.5,1.9,14.1,1.5c7.2-0.7,12.6-3.7,18.4-6.1   c5.8-2.4,8.2-6.6,10.9-10.2c3.4-4.6,3.1-10.2,0.2-15.6c-2.8-5.2-4.9-9.4-4.6-14.2c0.2-2.7-1.2-5.3-4.2-8.1   c-1.2-1.1-3.2-3.7-4.4-4.6c-3-2.3-6.4-2.9-9.5-2.7C99.9,489.5,96.3,488.6,94.5,488.4z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>46</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 37.1462 546.0399)">46</text><path d="M58.6,425.6c17.1-4.1,26.4-0.9,32.2,2.7c6.1,3.8,6.5,6.7,8.1,9.7c1.7,3.2,3.2,5.9,2.7,8.8   c-0.3,1.8-1.4,4.2,0,10c0.6,2.3,2,4,1.7,8.3c-0.3,5.1-2.5,8.3-3.9,10.9c-1.9,3.7-4.2,5.3-7.4,6.2c-7.9,2.2-9.3,2.2-13.7,3.5   c-9.4,2.7-13.7-1.8-19.6-4.3c-12-5.1-9.6-6-13.4-9.9c-6.2-6.4-3.3-14-2.9-20.8c0.6-9.6,3.8-14.2,7.4-17.2   C54.7,429.4,53.7,427.7,58.6,425.6L58.6,425.6z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>47</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 11.6462 474.0399)">47</text><path d="M32.9,403.6c3.4,6,6,7.6,9.4,11c7.3,7.2,8.6,5,13.7,5.2c5.1,0.1,27-1.8,29.2-3.1   c6.8-3.8,7.6-5.2,9.5-8.5c3.2-5.8,0.3-8.5-0.4-11.8c-1.9-8.8-0.4-10.5-0.9-14.5c-1.5-11-8.4-11.3-11.8-13.7   c-1.3-0.9-3.1-1.7-5.4-3.4c-7.5-5.4-21.1-1.7-27.5-0.1c-11.4,2.9-15.4,11.7-17.4,20.4C28.4,397.5,32,398,32.9,403.6L32.9,403.6z " stroke="#333" stroke-width="1" fill="#d5d5d5" stroke-dasharray=""><title>48</title></path><text xmlns="http://www.w3.org/2000/svg" transform="matrix(1 0 0 1 0.4795 400.0399)">48</text></svg>


<script>
    // Add an event listener to the SVG element
    document.querySelector("svg").addEventListener("click", function(event) {
      // Toggle the "clicked" class on the clicked SVG element
      event.target.classList.toggle("clicked");
    });
  </script>
</body>
</html>
Replace the <!-- Paste your SVG code here --> comment with your SVG code.

Save the HTML file with a .html extension.

Now, when you open the HTML file in a web browser, you should see the SVG displayed. Clicking on any part of the SVG will toggle the contour color to red. Clicking again will revert it back to the original color.






