{% extends 'base.html' %}

{% block extra_css %}

<style>
    /* General <PERSON>ing */
    .cases-per-department-section {
        background-color: #f8f9fc;
        padding: 2rem 0;
        
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .cases-per-department-section h3 {
        color: #4a4e69;
        margin-bottom: 1.5rem;
        font-weight: 700;
        letter-spacing: 1px;
        text-transform: uppercase;
    }
    
    /* Card Styles */
    .department-card {
        background: linear-gradient(135deg, #ffffff, #ece9e6); /* Subtle gradient for card background */
        border: none;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        cursor: pointer;
        overflow: hidden; /* Ensures the shadow doesn't leak outside the border-radius */
        border-radius: 25px;
        
    }
    
    .department-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 30px rgba(0, 0, 0, 0.15);
    }
    
    /* Card Body */
   
        
    .department-card .card-body {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.25rem;
        transition: background-color 0.3s;
        
      
    }
    .card:hover {
        transform: translateY(-10px); /* Moves the card up by 10px on hover */
        transition: transform 0.3s ease; /* Adds a smooth transition effect */
    }
    
    
    /* Department Name */
    .department-name {
        font-size: 1.3rem;
        font-weight: 700;
        color: #333;
    }
    
    /* Badge Style */
    .department-badge {
        background-color: #4e73df;
        color: #fff;
        font-size: 1.1rem;
        padding: 0.75em 1.5em;
        border-radius: 2em;
        font-weight: 600;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: background-color 0.3s, transform 0.3s;
    }
    
    .department-badge:hover {
        background-color: #2e59d9; /* Slightly darker shade on hover */
        transform: scale(1.05);
    }
    
    /* Responsive Layout */
    @media (max-width: 768px) {
        .department-card .card-body {
            flex-direction: column;
            align-items: flex-start;
        }
    
        .department-name {
            margin-bottom: 0.5rem;
        }
    }
    </style>

{% endblock %}

{% block content %}
<main class="container-fluid p-4">
    <!-- Dashboard Header -->
    <header class="dashboard-header mb-4">
        <h1 class="text-center text-gray-800">Dental Lab Dashboard</h1>
    </header>

<!-- Quick Stats Section -->
<section class="row text-center">
    <!-- Cases Today and One Month Ago -->
    <div class="col-md-4 mb-4">
        <div class="card bg-primary text-white shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col-6 border-right">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Cases Today</div>
                        <div class="h5 mb-0 font-weight-bold">{{ cases_today }}</div>
                    </div>
                    <div class="col-6">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Cases One Month Ago</div>
                        <div class="h5 mb-0 font-weight-bold">{{ cases_one_month_ago }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cases This Week and Last Week -->
    <div class="col-md-4 mb-4">
        <div class="card bg-success text-white shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col-6 border-right">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Cases This Week</div>
                        <div class="h5 mb-0 font-weight-bold">{{ cases_this_week }}</div>
                    </div>
                    <div class="col-6">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Cases Last Week</div>
                        <div class="h5 mb-0 font-weight-bold">{{ cases_last_week }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cases This Month and Last Month -->
    <div class="col-md-4 mb-4">
        <div class="card bg-info text-white shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col-6 border-right">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Cases This Month</div>
                        <div class="h5 mb-0 font-weight-bold">{{ cases_this_month }}</div>
                    </div>
                    <div class="col-6">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Cases Last Month</div>
                        <div class="h5 mb-0 font-weight-bold">{{ cases_last_month }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="row text-center">
    <!-- Cases Today and One Month Ago -->
    <div class="col-md-4 mb-4">
        <div class="card bg-primary text-white shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col-6 border-right">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">SALES VALUE ALL Today</div>
                        <div class="h5 mb-0 font-weight-bold">{{ cases_today }}</div>
                    </div>
                    <div class="col-6">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">SALES VALUE EURO Today</div>
                        <div class="h5 mb-0 font-weight-bold">{{ cases_one_month_ago }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cases This Week and Last Week -->
    <div class="col-md-4 mb-4">
        <div class="card bg-success text-white shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col-6 border-right">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">SALES VALUE ALL This Week</div>
                        <div class="h5 mb-0 font-weight-bold">{{ cases_this_week }}</div>
                    </div>
                    <div class="col-6">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">SALES VALUE ALL Last Week</div>
                        <div class="h5 mb-0 font-weight-bold">{{ cases_last_week }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cases This Month and Last Month -->
    <div class="col-md-4 mb-4">
        <div class="card bg-info text-white shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col-6 border-right">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">SALES VALUE ALL This Month</div>
                        <div class="h5 mb-0 font-weight-bold">{{ cases_this_month }}</div>
                    </div>
                    <div class="col-6">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">SALES VALUE EURO Month</div>
                        <div class="h5 mb-0 font-weight-bold">{{ cases_last_month }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>



<!-- Cases Per Department Section -->




<section class="cases-per-department-section">
    <div class="container">
        <h3 class="text-center">Cases per Department</h3>
        <div class="row">
            {% for department in cases_per_department %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card department-card shadow h-100">
                    <div class="card-body">
                        <div class="department-name">
                            {{ department.name }}
                        </div>
                        <div class="badge department-badge">
                            {{ department.total_cases }}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>



    <!-- Data Visualization Charts Section -->
    <section class="charts-section row">
        <div class="col-xl-6 col-lg-7 mb-4">
            <div class="card shadow h-100">
                <div class="card-body">
                    <canvas id="priorityChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-xl-6 col-lg-7 mb-4">
            <div class="card shadow h-100">
                <div class="card-body">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>
        </div>
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Cases Over Time last month</h6>
            </div>
            <div class="card-body">
                <canvas id="casesByYearChart"></canvas>



            </div>
        </div>
        
        
    </section>


    <section class="search-section mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">Search Cases</h6>
            </div>
            <div class="card-body">
                <div class="input-group mb-3">
                    <input type="text" id="caseSearch" class="form-control" placeholder="Search by case number or patient / dentist name...">
                    <button class="btn btn-warning" type="button" onclick="performSearch()">
                        <i class="fas fa-search"></i> Search
                    </button>
                </div>
                <div id="searchResults">
                    <!-- Search results will be dynamically loaded here -->
                </div>
            </div>
        </div>
    </section>
    
    
    <!-- Recent Cases Section -->
<section class="recent-cases-section mb-4">
    <div class="card border-left-info shadow h-100 py-2">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-info">Recent Cases</h6>
        </div>
        <div class="card-body">
            <table class="table table-sm table-hover">
                <thead>
                    <tr>
                        <th>Case Number</th>
                        <th>Patient</th>
                        <th>Dentist</th>
                        <th>Status</th>
                        <th>Received Date</th>
                        <th>Finished Date</th>
                        <!-- Additional columns as needed -->
                    </tr>
                </thead>
                <tbody>
                    {% for case in latest_cases %}
                        <tr>
                            <td>{{ case.case_number }}</td>
                            <td>{{ case.patient.first_name }} {{ case.patient.last_name }}</td>
                            <td>{{ case.dentist.first_name }} {{ case.dentist.last_name }}</td>
                            <td>{{ case.get_status_display }}</td>
                            <td>{{ case.received_date_time|date:"Y-m-d H:i" }}</td>
                            <td>{{ case.finished_date_time|date:"Y-m-d H:i" }}</td>
                            <!-- Additional data cells as needed -->
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</section>
</main>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.5/dist/umd/popper.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to initialize charts
    function initChart(chartId, chartType, chartData, chartOptions) {
        const ctx = document.getElementById(chartId).getContext('2d');
        return new Chart(ctx, {
            type: chartType,
            data: chartData,
            options: chartOptions
        });
    }

    // Function to map data for charts
    function mapData(data, key) {
        return data.map(item => item[key]);
    }

    // Initialize Priority Chart with Dynamic Data
    const priorityData = {{ cases_by_priority|safe }};
    console.log('Priority Data:', priorityData);
    const priorityNames = {
        1: 'High',
        2: 'Normal',
        3: 'Low',
    };
    initChart('priorityChart', 'bar', {
        labels: priorityData.map(item => priorityNames[item.priority]),
        datasets: [{
            label: 'Priority Counts',
            data: mapData(priorityData, 'total'),
            backgroundColor: [
                'rgba(255, 2, 132, 0.2)',
                'rgba(54, 162, 235, 0.2)',
                'rgba(255, 206, 86, 0.2)'
            ],
            borderColor: [
                'rgba(255, 18, 2, 5)',
                'rgba(54, 16, 235, 1)',
                'rgba(255, 206, 86, 1)'
            ],
            borderWidth: 1
        }]
    });

    // Initialize Status Chart with Dynamic Data
    const statusData = {{ cases_by_status|safe }};
    console.log('Status Data:', statusData);
    initChart('statusChart', 'pie', {
        labels: mapData(statusData, 'status'),
        datasets: [{
            label: 'Status Counts',
            data: mapData(statusData, 'total'),
            backgroundColor: [
                'rgba(75, 192, 192, 0.2)',
                'rgba(153, 102, 255, 0.2)',
                'rgba(255, 159, 64, 0.2)'
            ],
            borderColor: [
                'rgba(75, 192, 192, 1)',
                'rgba(153, 102, 255, 1)',
                'rgba(255, 159, 64, 1)'
            ],
            borderWidth: 1
        }]
    });

    // Cases Last Month Chart with Dynamic Data
    console.log('{{ cases_last_month_json|safe }}');  // Log the actual JSON string for debugging
    const casesLastMonthData = JSON.parse('{{ cases_last_month_json|safe }}');
    console.log('Parsed casesLastMonthData:', casesLastMonthData);  // Log the parsed data

    // Cases Last Year Chart with Dynamic Data
    console.log('{{ cases_last_year_json|safe }}');  // Log the actual JSON string for debugging
    const casesLastYearData = JSON.parse('{{ cases_last_year_json|safe }}');
    console.log('Parsed casesLastYearData:', casesLastYearData);  // Log the parsed data

    // Prepare the data for the chart
    const days = casesLastYearData.map(data => data.date);  // Use 'date' field as label
    const caseCounts = casesLastYearData.map(data => data.case_count);  // Use 'case_count' field as data

    // Get the canvas context
    const ctxCasesByYear = document.getElementById('casesByYearChart').getContext('2d');

    // Initialize the chart
    const casesByYearChart = new Chart(ctxCasesByYear, {
        type: 'line', // Line chart to show trend over time
        data: {
            labels: days, // The days of the last year
            datasets: [{
                label: 'Cases Created',
                data: caseCounts, // The count of cases for each day
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1,
                fill: false
            }]
        },
        options: {
            scales: {
                xAxes: [{
                    type: 'time',
                    time: {
                        unit: 'month',  // Changed from 'day' to 'month' for better readability
                        tooltipFormat: 'll'
                    },
                    distribution: 'linear'
                }],
                yAxes: [{
                    ticks: {
                        beginAtZero: true
                    }
                }]
            },
            responsive: true,
            maintainAspectRatio: false
        }
    });


    // Function to perform a search
    function performSearch() {
        var searchTerm = document.getElementById('caseSearch').value;
        var xhr = new XMLHttpRequest();
        xhr.open('GET', '/case/search/?q=' + encodeURIComponent(searchTerm), true);
        xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
        xhr.onload = function () {
            if (xhr.status === 200) {
                var data = JSON.parse(xhr.responseText);
                document.getElementById('searchResults').innerHTML = data.html;
            } else {
                console.error("Error occurred while searching: ", xhr.statusText);
            }
        };
        
        xhr.send();
    }

    // Event listener for the search input
    document.getElementById('caseSearch').addEventListener('keypress', function(event) {
        if (event.key === 'Enter') {
            event.preventDefault();
            performSearch();
        }
    });
});
</script>
{% endblock %}
