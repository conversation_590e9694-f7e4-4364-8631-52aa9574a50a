{% for case in results %}
    {% if case.case_number %}
        <div class="search-result-item">
            <a href="{% url 'case_detail' case.case_number %}" class="search-result-link">
                <div class="search-result-header">
                    <h5>Case ##{{ case.case_number }}</h5>
                </div>
                <div class="search-result-body">
                    <p><strong>Patient:</strong> {{ case.patient.first_name }} {{ case.patient.last_name }}</p>
                    <p><strong>Dentist:</strong> {{ case.dentist.first_name }} {{ case.dentist.last_name }}</p>
                    <p><strong>Status:</strong> {{ case.get_status_display }}</p>
                    <p><strong>Ship Date:</strong> {{ case.ship_date_time|date:"Y-m-d H:i"}}</p>
                    <p><strong>Received Date:</strong> {{ case.received_date_time|date:"Y-m-d H:i" }}</p>

                    <!-- Additional case details here -->
                </div>
            </a>
        </div>
    {% else %}
        <div class="search-result-item">
            <p>Case with missing case number</p>
            <!-- Handle the display for cases without a case number -->
        </div>
    {% endif %}
{% empty %}
    <p>No results found for your query.</p>
{% endfor %}
