from datetime import date, datetime, time, timedelta
from decimal import Decimal
from django.db import models
from django.contrib.auth.models import AbstractBase<PERSON>ser, BaseUserManager, PermissionsMixin
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone

class CustomUserManager(BaseUserManager):
    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError(_('Users must have an email address'))
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)
        if extra_fields.get('is_staff') is not True:
            raise ValueError(_('Superuser must have is_staff=True'))
        if extra_fields.get('is_superuser') is not True:
            raise ValueError(_('Superuser must have is_superuser=True'))
        return self.create_user(email, password, **extra_fields)

    def active(self):
        return self.filter(is_active=True)

    def staff_members(self):
        return self.filter(user_type=1, is_active=True)

    def dentists(self):
        return self.filter(user_type=2, is_active=True)

class CustomUser(AbstractBaseUser, PermissionsMixin):
    email = models.EmailField(unique=True)
    first_name = models.CharField(max_length=50)
    last_name = models.CharField(max_length=50)
    phone_number = models.CharField(max_length=15, blank=True)
    date_of_birth = models.DateField(null=True, blank=True)
    gender = models.CharField(max_length=1, choices=[('M', 'Male'), ('F', 'Female'), ('O', 'Other')], blank=True)
    address_line1 = models.CharField(max_length=100, blank=True)
    address_line2 = models.CharField(max_length=100, blank=True)
    city = models.CharField(max_length=50, blank=True)
    state = models.CharField(max_length=50, blank=True)
    postal_code = models.CharField(max_length=10, blank=True)
    country = models.CharField(max_length=50, blank=True)
    USER_TYPE_CHOICES = ((1, 'Staff'), (2, 'Dentist'))
    user_type = models.PositiveSmallIntegerField(choices=USER_TYPE_CHOICES, default=1)
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)
    is_superuser = models.BooleanField(default=False)
    profile_image = models.ImageField(upload_to='profile_images/', blank=True)
    bio = models.TextField(blank=True)
    specialization = models.CharField(max_length=100, blank=True)
    skills = models.ManyToManyField('case.Skill', blank=True)
    date_joined = models.DateTimeField(auto_now_add=True)
    last_login = models.DateTimeField(null=True, blank=True)
    last_login_ip = models.GenericIPAddressField(null=True, blank=True)
    LANGUAGE_CHOICES = [('en', 'English'), ('sq', 'Albanian'), ('de', 'German')]
    preferred_language = models.CharField(max_length=2, choices=LANGUAGE_CHOICES, default='en')
    email_notifications = models.BooleanField(default=True)
    sms_notifications = models.BooleanField(default=False)

    objects = CustomUserManager()

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['first_name', 'last_name']

    class Meta:
        verbose_name = _('user')
        verbose_name_plural = _('users')
        ordering = ['first_name', 'last_name']
        indexes = [
            models.Index(fields=['email', 'user_type']),
            models.Index(fields=['first_name', 'last_name']),
            models.Index(fields=['is_active', 'user_type'])
        ]

    def __str__(self):
        return self.email

    def get_full_name(self):
        return f'{self.first_name} {self.last_name}'.strip()

    def get_short_name(self):
        return self.first_name

    def is_dentist(self):
        return self.user_type == 2 and hasattr(self, 'dentist_profile')

    def get_absolute_url(self):
        from django.urls import reverse
        return reverse('user-detail', kwargs={'pk': self.pk})

    def save(self, *args, **kwargs):
        self.email = self.email.lower()
        super().save(*args, **kwargs)

class WorkingHour(models.Model):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='working_hours')
    day_of_week = models.IntegerField(choices=[
        (0, 'Monday'),
        (1, 'Tuesday'),
        (2, 'Wednesday'),
        (3, 'Thursday'),
        (4, 'Friday'),
        (5, 'Saturday'),
        (6, 'Sunday')
    ])
    start_time = models.TimeField()
    end_time = models.TimeField()
    is_working_day = models.BooleanField(default=True)

    class Meta:
        ordering = ['day_of_week', 'start_time']
        unique_together = ['user', 'day_of_week']

    def clean(self):
        if self.start_time >= self.end_time:
            raise ValidationError(_('End time must be after start time'))

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.get_day_of_week_display()}"

class UserDepartment(models.Model):
    ROLE_CHOICES = [
        ('manager', 'Department Manager'),
        ('supervisor', 'Supervisor'),
        ('staff', 'Staff Member'),
        ('trainee', 'Trainee')
    ]
    ACCESS_LEVELS = [
        (1, 'Basic - View Only'),
        (2, 'Standard - View and Execute'),
        (3, 'Advanced - View, Execute and Modify'),
        (4, 'Full - All Permissions')
    ]
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='departments')
    department = models.ForeignKey('case.Department', on_delete=models.CASCADE, related_name='users')
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='staff')
    is_manager = models.BooleanField(default=False)
    access_level = models.IntegerField(choices=ACCESS_LEVELS, default=1)
    date_joined = models.DateField(auto_now_add=True)
    end_date = models.DateField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    performance_rating = models.DecimalField(max_digits=3, decimal_places=2, validators=[MinValueValidator(0), MaxValueValidator(5)], null=True, blank=True)
    completed_tasks = models.IntegerField(default=0)
    total_work_hours = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    class Meta:
        unique_together = ('user', 'department')
        indexes = [
            models.Index(fields=['user', 'department', 'is_active']),
            models.Index(fields=['department', 'role', 'is_active'])
        ]

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.department.name} ({self.get_role_display()})"

    def calculate_performance(self, start_date=None, end_date=None):
        if not start_date:
            start_date = timezone.now() - timedelta(days=30)
        if not end_date:
            end_date = timezone.now()
        completed_tasks = self.user.assigned_tasks.filter(
            workflow_stage__department=self.department,
            status='completed',
            actual_end_time__range=(start_date, end_date)
        )
        metrics = {
            'completed_count': completed_tasks.count(),
            'on_time_count': completed_tasks.filter(actual_end_time__lte=models.F('scheduled_end_time')).count(),
            'total_duration': sum(
                (task.actual_end_time - task.actual_start_time).total_seconds()
                for task in completed_tasks if task.actual_start_time and task.actual_end_time
            ) / 3600
        }
        if metrics['completed_count'] > 0:
            metrics['on_time_percentage'] = (metrics['on_time_count'] / metrics['completed_count']) * 100
            metrics['average_duration'] = metrics['total_duration'] / metrics['completed_count']
        return metrics

    def update_performance_metrics(self):
        metrics = self.calculate_performance()
        self.completed_tasks = metrics['completed_count']
        self.total_work_hours = metrics['total_duration']
        if metrics['completed_count'] > 0:
            self.performance_rating = (metrics['on_time_percentage'] / 20)
        self.save()

class UserAvailability(models.Model):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='availabilities')
    date = models.DateField()
    start_time = models.TimeField()
    end_time = models.TimeField()
    is_available = models.BooleanField(default=True)
    reason = models.CharField(max_length=255, blank=True, help_text=_("Reason for unavailability if not available"))
    capacity = models.PositiveSmallIntegerField(default=100, validators=[MinValueValidator(0), MaxValueValidator(100)], help_text=_("Percentage of working capacity for this day"))
    recurring = models.BooleanField(default=False)
    recurring_pattern = models.CharField(max_length=20, choices=[
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly')
    ], blank=True)
    recurring_end_date = models.DateField(null=True, blank=True)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    modified_at = models.DateTimeField(auto_now=True)
    approved_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='approved_availabilities')

    class Meta:
        verbose_name_plural = _("User availabilities")
        unique_together = ['user', 'date']
        ordering = ['date', 'start_time']
        indexes = [
            models.Index(fields=['user', 'date', 'is_available']),
            models.Index(fields=['date', 'start_time', 'end_time'])
        ]

    def __str__(self):
        status = 'Available' if self.is_available else 'Unavailable'
        return f"{self.user.get_full_name()} - {self.date} - {status}"

    def clean(self):
        if self.start_time >= self.end_time:
            raise ValidationError(_('End time must be after start time.'))
        if self.capacity < 0 or self.capacity > 100:
            raise ValidationError(_('Capacity must be between 0 and 100.'))
        if self.recurring and not self.recurring_pattern:
            raise ValidationError(_('Recurring pattern is required for recurring availability.'))
        if self.recurring_end_date and self.recurring_end_date < self.date:
            raise ValidationError(_('Recurring end date must be after start date.'))

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)
        if self.recurring:
            self.create_recurring_availabilities()

    @property
    def duration(self):
        return datetime.combine(date.min, self.end_time) - datetime.combine(date.min, self.start_time)

    def get_conflicts(self):
        from scheduling.models import ScheduleItem
        return ScheduleItem.objects.filter(
            assigned_to=self.user,
            start_time__date=self.date,
            start_time__time__lt=self.end_time,
            end_time__time__gt=self.start_time
        )

    def create_recurring_availabilities(self):
        if not self.recurring or not self.recurring_pattern:
            return
        current_date = self.date + timedelta(days=1)
        end_date = self.recurring_end_date or (self.date + timedelta(days=365))
        while current_date <= end_date:
            if self.recurring_pattern == 'daily':
                next_date = current_date
            elif self.recurring_pattern == 'weekly':
                if current_date.weekday() == self.date.weekday():
                    next_date = current_date
                else:
                    current_date += timedelta(days=1)
                    continue
            elif self.recurring_pattern == 'monthly':
                if current_date.day == self.date.day:
                    next_date = current_date
                else:
                    current_date += timedelta(days=1)
                    continue
            UserAvailability.objects.get_or_create(
                user=self.user,
                date=next_date,
                defaults={
                    'start_time': self.start_time,
                    'end_time': self.end_time,
                    'is_available': self.is_available,
                    'capacity': self.capacity,
                    'reason': self.reason,
                    'notes': self.notes
                }
            )
            current_date += timedelta(days=1)

    @classmethod
    def get_user_availability(cls, user, start_date, end_date):
        return cls.objects.filter(
            user=user,
            date__range=[start_date, end_date],
            is_available=True
        )

    @classmethod
    def set_user_unavailable(cls, user, date, reason=""):
        availability, created = cls.objects.get_or_create(
            user=user,
            date=date,
            defaults={
                'start_time': time(0, 0),
                'end_time': time(23, 59, 59),
                'is_available': False,
                'reason': reason
            }
        )
        if not created:
            availability.is_available = False
            availability.reason = reason
            availability.save()
        return availability

class UserActivityLog(models.Model):
    ACTION_TYPES = [
        ('login', 'Login'),
        ('logout', 'Logout'),
        ('profile_update', 'Profile Update'),
        ('password_change', 'Password Change'),
        ('settings_change', 'Settings Change'),
        ('department_assignment', 'Department Assignment'),
        ('availability_update', 'Availability Update'),
        ('task_assignment', 'Task Assignment'),
        ('other', 'Other')
    ]
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='activity_logs')
    action = models.CharField(max_length=50, choices=ACTION_TYPES)
    timestamp = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.CharField(max_length=255, blank=True)
    details = models.JSONField(default=dict)
    success = models.BooleanField(default=True)

    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', 'action', 'timestamp']),
            models.Index(fields=['timestamp', 'action'])
        ]

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.action} - {self.timestamp}"

class UserSettings(models.Model):
    THEME_CHOICES = [
        ('light', _('Light Theme')),
        ('dark', _('Dark Theme')),
        ('system', _('System Default'))
    ]
    LANGUAGE_CHOICES = [
        ('en', _('English')),
        ('sq', _('Albanian')),
        ('de', _('German'))
    ]
    user = models.OneToOneField('CustomUser', on_delete=models.CASCADE, related_name='settings')
    email_notifications = models.BooleanField(default=True, verbose_name=_('Email Notifications'))
    sms_notifications = models.BooleanField(default=False, verbose_name=_('SMS Notifications'))
    theme = models.CharField(max_length=20, choices=THEME_CHOICES, default='system', verbose_name=_('Theme'))
    preferred_language = models.CharField(max_length=10, choices=LANGUAGE_CHOICES, default='en', verbose_name=_('Preferred Language'))
    items_per_page = models.IntegerField(default=25, verbose_name=_('Items per Page'))

    class Meta:
        verbose_name = _('User Settings')
        verbose_name_plural = _('User Settings')

    def __str__(self):
        return f'{self.user.get_full_name()} - Settings'

class UserQualification(models.Model):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='qualifications')
    title = models.CharField(max_length=100)
    institution = models.CharField(max_length=200)
    date_obtained = models.DateField()
    expiry_date = models.DateField(null=True, blank=True)
    certificate_number = models.CharField(max_length=50, blank=True)
    description = models.TextField(blank=True)
    document = models.FileField(upload_to='qualifications/', blank=True)
    is_verified = models.BooleanField(default=False)
    verified_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='verified_qualifications')
    verified_date = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-date_obtained']

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.title}"

    def is_expired(self):
        if self.expiry_date:
            return self.expiry_date < timezone.now().date()
        return False

    def save(self, *args, **kwargs):
        if self.is_verified and not self.verified_date:
            self.verified_date = timezone.now()
        super().save(*args, **kwargs)

class EmergencyContact(models.Model):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='emergency_contacts')
    name = models.CharField(max_length=100)
    relationship = models.CharField(max_length=50)
    phone_primary = models.CharField(max_length=15)
    phone_secondary = models.CharField(max_length=15, blank=True)
    email = models.EmailField(blank=True)
    address = models.TextField(blank=True)
    is_primary = models.BooleanField(default=False)

    class Meta:
        ordering = ['-is_primary', 'name']

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.name} ({self.relationship})"

    def save(self, *args, **kwargs):
        if self.is_primary:
            EmergencyContact.objects.filter(user=self.user, is_primary=True).exclude(id=self.id).update(is_primary=False)
        super().save(*args, **kwargs)

class SystemSettings(models.Model):
    site_name = models.CharField(max_length=255, default='Dental Labor')
    maintenance_mode = models.CharField(max_length=3, choices=[('on', 'On'), ('off', 'Off')], default='off')

    def __str__(self):
        return "System Settings"

from django.db.models.signals import post_save
from django.dispatch import receiver

@receiver(post_save, sender=CustomUser)
def create_user_settings(sender, instance, created, **kwargs):
    if created:
        UserSettings.objects.create(user=instance)

@receiver(post_save, sender=CustomUser)
def update_user_departments(sender, instance, created, **kwargs):
    if instance.user_type == 2:
        pass

@receiver(post_save, sender=UserDepartment)
def update_department_metrics(sender, instance, created, **kwargs):
    if not created:
        instance.update_performance_metrics()

class UserAvailabilityError(Exception):
    pass

class DepartmentAssignmentError(Exception):
    pass

class ActiveUserManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(is_active=True)

class StaffManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(user_type=1, is_active=True)

class DentistManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(user_type=2, is_active=True)

CustomUser.active_users = ActiveUserManager()
CustomUser.staff = StaffManager()
CustomUser.dentists = DentistManager()
