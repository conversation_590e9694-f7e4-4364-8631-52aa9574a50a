from django import forms
from django.contrib.auth import authenticate, password_validation  # Add authenticate here
from django.contrib.auth.forms import UserCreationForm, AuthenticationForm
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from .models import CustomUser

class CustomUserCreationForm(UserCreationForm):
    """
    Enhanced user registration form with additional fields and validation
    """
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': _('Enter your email address'),
            'autocomplete': 'email'
        })
    )
    
    first_name = forms.CharField(
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Enter your first name'),
            'autocomplete': 'given-name'
        })
    )
    
    last_name = forms.CharField(
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Enter your last name'),
            'autocomplete': 'family-name'
        })
    )
    
    password1 = forms.CharField(
        label=_('Password'),
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': _('Enter your password'),
            'autocomplete': 'new-password'
        }),
        help_text=password_validation.password_validators_help_text_html()
    )
    
    password2 = forms.CharField(
        label=_('Confirm Password'),
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': _('Confirm your password'),
            'autocomplete': 'new-password'
        })
    )

    # Optional fields
    phone_number = forms.CharField(
        required=False,
        max_length=15,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Enter your phone number (optional)'),
            'autocomplete': 'tel'
        })
    )
    
    terms_agreement = forms.BooleanField(
        required=True,
        label=_('I agree to the Terms and Conditions'),
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )

    class Meta(UserCreationForm.Meta):
        model = CustomUser
        fields = ('email', 'first_name', 'last_name', 'phone_number')

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if email:
            email = email.lower()
            if CustomUser.objects.filter(email=email).exists():
                raise ValidationError(_('This email address is already registered.'))
        return email

    def clean_password2(self):
        password1 = self.cleaned_data.get('password1')
        password2 = self.cleaned_data.get('password2')
        if password1 and password2 and password1 != password2:
            raise ValidationError(_('The passwords do not match.'))
        return password2

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email'].lower()
        if commit:
            user.save()
        return user



# accounts/forms.py

class UserAdminForm(forms.ModelForm):
    """Form for admin user management"""
    password_change = forms.CharField(
        widget=forms.PasswordInput(attrs={'class': 'form-control'}),
        required=False,
        help_text='Leave empty if you don\'t want to change the password'
    )
    password_confirm = forms.CharField(
        widget=forms.PasswordInput(attrs={'class': 'form-control'}),
        required=False,
        help_text='Confirm the new password'
    )

    class Meta:
        model = CustomUser
        fields = [
            'email', 'first_name', 'last_name', 'is_active',
            'is_staff', 'is_superuser', 'user_type',
            'phone_number', 'date_of_birth', 'gender',
            'address_line1', 'address_line2', 'city',
            'state', 'postal_code', 'country'
        ]
        widgets = {
            'date_of_birth': forms.DateInput(
                attrs={'type': 'date', 'class': 'form-control'}
            ),
            'gender': forms.Select(
                attrs={'class': 'form-control'}
            ),
            'user_type': forms.Select(
                attrs={'class': 'form-control'}
            ),
            'email': forms.EmailInput(
                attrs={'class': 'form-control'}
            ),
            'first_name': forms.TextInput(
                attrs={'class': 'form-control'}
            ),
            'last_name': forms.TextInput(
                attrs={'class': 'form-control'}
            ),
            'phone_number': forms.TextInput(
                attrs={'class': 'form-control'}
            ),
            'address_line1': forms.TextInput(
                attrs={'class': 'form-control'}
            ),
            'address_line2': forms.TextInput(
                attrs={'class': 'form-control'}
            ),
            'city': forms.TextInput(
                attrs={'class': 'form-control'}
            ),
            'state': forms.TextInput(
                attrs={'class': 'form-control'}
            ),
            'postal_code': forms.TextInput(
                attrs={'class': 'form-control'}
            ),
            'country': forms.TextInput(
                attrs={'class': 'form-control'}
            ),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field in ['is_active', 'is_staff', 'is_superuser']:
            self.fields[field].widget.attrs.update({
                'class': 'form-check-input'
            })

    def clean(self):
        cleaned_data = super().clean()
        password_change = cleaned_data.get('password_change')
        password_confirm = cleaned_data.get('password_confirm')

        if password_change:
            if not password_confirm:
                raise forms.ValidationError(
                    'Please confirm the new password'
                )
            if password_change != password_confirm:
                raise forms.ValidationError(
                    'Passwords do not match'
                )
            try:
                password_validation.validate_password(password_change)
            except ValidationError as e:
                raise forms.ValidationError(e.messages)

        return cleaned_data

    def save(self, commit=True):
        user = super().save(commit=False)
        password_change = self.cleaned_data.get('password_change')
        
        if password_change:
            user.set_password(password_change)
            
        if commit:
            user.save()
        return user
    
    
    
    
class CustomLoginForm(AuthenticationForm):
    username = forms.EmailField(
        label=_('Email'),
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': _('Enter your email'),
            'autocomplete': 'email'
        })
    )
    
    password = forms.CharField(
        label=_('Password'),
        strip=False,
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': _('Enter your password'),
            'autocomplete': 'current-password'
        }),
    )

    remember_me = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        })
    )

    error_messages = {
        'invalid_login': _(
            "Please enter a correct email and password. Note that both "
            "fields may be case-sensitive."
        ),
        'inactive': _("This account is inactive."),
    }

    def clean(self):
        email = self.cleaned_data.get('username')
        password = self.cleaned_data.get('password')

        if email is not None and password:
            email = email.lower()
            self.cleaned_data['username'] = email
            self.user_cache = authenticate(
                self.request, email=email, password=password
            )
            if self.user_cache is None:
                raise self.get_invalid_login_error()
            else:
                self.confirm_login_allowed(self.user_cache)

        return self.cleaned_data

class UserProfileUpdateForm(forms.ModelForm):
    """
    Form for updating user profile information
    """
    class Meta:
        model = CustomUser
        fields = ['first_name', 'last_name', 'phone_number', 'date_of_birth', 
                 'address_line1', 'address_line2', 'city', 'state', 
                 'postal_code', 'country', 'profile_image']
        widgets = {
            'first_name': forms.TextInput(attrs={'class': 'form-control'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control'}),
            'phone_number': forms.TextInput(attrs={'class': 'form-control'}),
            'date_of_birth': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'address_line1': forms.TextInput(attrs={'class': 'form-control'}),
            'address_line2': forms.TextInput(attrs={'class': 'form-control'}),
            'city': forms.TextInput(attrs={'class': 'form-control'}),
            'state': forms.TextInput(attrs={'class': 'form-control'}),
            'postal_code': forms.TextInput(attrs={'class': 'form-control'}),
            'country': forms.TextInput(attrs={'class': 'form-control'}),
            'profile_image': forms.FileInput(attrs={'class': 'form-control'})
        }
        
# accounts/forms.py

from django import forms
from .models import SystemSettings

class SystemSettingsForm(forms.ModelForm):
    class Meta:
        model = SystemSettings
        fields = ['site_name', 'maintenance_mode']  # Add other fields as needed
        widgets = {
            'site_name': forms.TextInput(attrs={'class': 'form-control'}),
            'maintenance_mode': forms.Select(attrs={'class': 'form-select'}),
        }
        
        
from django import forms
from django.contrib.auth.forms import PasswordChangeForm, SetPasswordForm
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.password_validation import validate_password
from .models import CustomUser

class ChangePasswordForm(PasswordChangeForm):
    """
    Form for changing password with current password verification
    """
    old_password = forms.CharField(
        label=_('Current Password'),
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': _('Enter your current password'),
            'autocomplete': 'current-password'
        })
    )
    
    new_password1 = forms.CharField(
        label=_('New Password'),
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': _('Enter new password'),
            'autocomplete': 'new-password'
        }),
        help_text=password_validation.password_validators_help_text_html()
    )
    
    new_password2 = forms.CharField(
        label=_('Confirm New Password'),
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': _('Confirm new password'),
            'autocomplete': 'new-password'
        })
    )

    def clean_new_password2(self):
        password1 = self.cleaned_data.get('new_password1')
        password2 = self.cleaned_data.get('new_password2')
        if password1 and password2:
            if password1 != password2:
                raise forms.ValidationError(
                    _('The two password fields do not match.'),
                    code='password_mismatch'
                )
        validate_password(password2, self.user)
        return password2

class ResetPasswordForm(forms.Form):
    """
    Form for initiating password reset process
    """
    email = forms.EmailField(
        label=_('Email'),
        max_length=254,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': _('Enter your email address'),
            'autocomplete': 'email'
        })
    )

    def clean_email(self):
        email = self.cleaned_data['email'].lower()
        if not CustomUser.objects.filter(email=email).exists():
            raise forms.ValidationError(
                _('There is no user registered with this email address.'),
                code='invalid_email'
            )
        return email

class SetNewPasswordForm(SetPasswordForm):
    """
    Form for setting new password (after reset)
    """
    new_password1 = forms.CharField(
        label=_('New Password'),
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': _('Enter new password'),
            'autocomplete': 'new-password'
        }),
        help_text=password_validation.password_validators_help_text_html()
    )
    
    new_password2 = forms.CharField(
        label=_('Confirm New Password'),
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': _('Confirm new password'),
            'autocomplete': 'new-password'
        })
    )

    def clean_new_password2(self):
        password1 = self.cleaned_data.get('new_password1')
        password2 = self.cleaned_data.get('new_password2')
        if password1 and password2:
            if password1 != password2:
                raise forms.ValidationError(
                    _('The two password fields do not match.'),
                    code='password_mismatch'
                )
        validate_password(password2, self.user)
        return password2

class EmailVerificationForm(forms.Form):
    """
    Form for email verification
    """
    verification_code = forms.CharField(
        label=_('Verification Code'),
        max_length=6,
        min_length=6,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Enter 6-digit verification code'),
            'autocomplete': 'off'
        })
    )

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

    def clean_verification_code(self):
        code = self.cleaned_data['verification_code']
        # You would implement your verification code checking logic here
        # This is just a placeholder - implement according to your verification system
        if not code.isdigit():
            raise forms.ValidationError(
                _('Verification code must contain only numbers.'),
                code='invalid_code'
            )
        return code

class ResendVerificationForm(forms.Form):
    """
    Form for requesting new verification email
    """
    email = forms.EmailField(
        label=_('Email'),
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': _('Enter your email address'),
            'autocomplete': 'email'
        })
    )

    def clean_email(self):
        email = self.cleaned_data['email'].lower()
        try:
            user = CustomUser.objects.get(email=email)
            if user.is_active:
                raise forms.ValidationError(
                    _('This email is already verified.'),
                    code='already_verified'
                )
        except CustomUser.DoesNotExist:
            raise forms.ValidationError(
                _('No account found with this email address.'),
                code='invalid_email'
            )
        return email

# Optional: Form for Two-Factor Authentication
class TwoFactorAuthForm(forms.Form):
    """
    Form for two-factor authentication
    """
    token = forms.CharField(
        label=_('Authentication Code'),
        max_length=6,
        min_length=6,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Enter 6-digit authentication code'),
            'autocomplete': 'off'
        })
    )

    def clean_token(self):
        token = self.cleaned_data['token']
        if not token.isdigit():
            raise forms.ValidationError(
                _('Authentication code must contain only numbers.'),
                code='invalid_token'
            )
        return token        
    
    
    
