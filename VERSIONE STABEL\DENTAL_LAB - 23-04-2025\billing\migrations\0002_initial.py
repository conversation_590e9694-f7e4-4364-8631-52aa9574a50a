# Generated by Django 5.0.4 on 2025-04-17 21:40

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("Dentists", "0002_initial"),
        ("billing", "0001_initial"),
        ("case", "0002_initial"),
        ("finance", "0001_initial"),
        ("items", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="invoice",
            name="case",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="invoice",
                to="case.case",
            ),
        ),
        migrations.AddField(
            model_name="invoice",
            name="currency",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="billing_invoices",
                to="items.currency",
            ),
        ),
        migrations.AddField(
            model_name="invoice",
            name="dentist",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="billing_invoices",
                to="Dentists.dentist",
            ),
        ),
        migrations.AddField(
            model_name="invoice",
            name="payments",
            field=models.ManyToManyField(
                blank=True,
                related_name="invoices_paid",
                through="finance.InvoicePayment",
                to="finance.payment",
            ),
        ),
        migrations.AddField(
            model_name="invoiceitem",
            name="currency",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="invoice_items_priced_in",
                to="items.currency",
            ),
        ),
        migrations.AddField(
            model_name="invoiceitem",
            name="invoice",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="invoice_items",
                to="billing.invoice",
            ),
        ),
        migrations.AddField(
            model_name="invoiceitem",
            name="item",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="invoice_lines",
                to="items.item",
            ),
        ),
        migrations.AddField(
            model_name="purchaseorder",
            name="supplier",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="purchase_orders",
                to="items.supplier",
            ),
        ),
        migrations.AddField(
            model_name="purchaseorderitem",
            name="currency",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="po_items_priced_in",
                to="items.currency",
            ),
        ),
        migrations.AddField(
            model_name="purchaseorderitem",
            name="purchase_order",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="purchaseorderitem_set",
                to="billing.purchaseorder",
            ),
        ),
        migrations.AddField(
            model_name="purchaseorderitem",
            name="raw_material",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="purchase_order_lines",
                to="items.rawmaterial",
            ),
        ),
        migrations.AddField(
            model_name="purchaseorderitem",
            name="unit",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name="po_items_in_unit",
                to="items.unit",
            ),
        ),
        migrations.AddIndex(
            model_name="invoice",
            index=models.Index(
                fields=["dentist", "status"], name="billing_inv_dentist_32361c_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="invoice",
            index=models.Index(fields=["case"], name="billing_inv_case_id_6d517c_idx"),
        ),
        migrations.AddIndex(
            model_name="invoice",
            index=models.Index(fields=["date"], name="billing_inv_date_9e0933_idx"),
        ),
    ]
