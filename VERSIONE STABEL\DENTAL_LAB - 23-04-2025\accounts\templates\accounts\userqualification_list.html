{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "User Qualifications" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Qualifications" %}</h3>
                    <div class="card-tools">
                        <a href="{% url 'accounts:qualification_create' %}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> {% trans "Add New" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if object_list %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>{% trans "Title" %}</th>
                                        <th>{% trans "Institution" %}</th>
                                        <th>{% trans "Date Obtained" %}</th>
                                        <th>{% trans "Expiry Date" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for qualification in object_list %}
                                        <tr>
                                            <td>{{ qualification.title }}</td>
                                            <td>{{ qualification.institution }}</td>
                                            <td>{{ qualification.date_obtained }}</td>
                                            <td>
                                                {% if qualification.expiry_date %}
                                                    {{ qualification.expiry_date }}
                                                {% else %}
                                                    <span class="text-muted">{% trans "N/A" %}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if qualification.is_verified %}
                                                    <span class="badge bg-success">{% trans "Verified" %}</span>
                                                {% else %}
                                                    <span class="badge bg-warning">{% trans "Pending" %}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="{% url 'accounts:qualification_update' qualification.pk %}" 
                                                       class="btn btn-sm btn-info">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="{% url 'accounts:qualification_delete' qualification.pk %}" 
                                                       class="btn btn-sm btn-danger"
                                                       onclick="return confirm('{% trans "Are you sure you want to delete this qualification?" %}')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        {% if is_paginated %}
                            <nav aria-label="Page navigation" class="mt-3">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                                                {% trans "Previous" %}
                                            </a>
                                        </li>
                                    {% endif %}
                                    
                                    <li class="page-item disabled">
                                        <span class="page-link">
                                            {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                        </span>
                                    </li>
                                    
                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                                                {% trans "Next" %}
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="alert alert-info">
                            {% trans "No qualifications found." %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}