{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Select Case for Invoice" %} | {% trans "Billing" %}{% endblock %}

{% block extra_css %}
<style>
  :root {
    /* Primary Colors - Fresher, Lighter Palette */
    --primary: #4285F4; /* Google Blue */
    --primary-light: rgba(66, 133, 244, 0.1);
    --secondary: #8ab4f8;
    --success: #34A853; /* Google Green */
    --success-light: rgba(52, 168, 83, 0.1);
    --danger: #EA4335; /* Google Red */
    --danger-light: rgba(234, 67, 53, 0.1);
    --warning: #FBBC05; /* Google Yellow */
    --warning-light: rgba(251, 188, 5, 0.1);
    --info: #46bdc6;
    --info-light: rgba(70, 189, 198, 0.1);
    --dark: #3c4043;
    --light: #f8f9fa;
    --white: #ffffff;

    /* Background and Text Colors */
    --bg-main: #f8f9fa;
    --text-main: #202124;
    --card-bg: #ffffff;
    --border-color: rgba(0,0,0,0.08);

    /* UI Elements */
    --shadow-sm: 0 1px 2px rgba(0,0,0,0.05);
    --shadow: 0 4px 6px rgba(0,0,0,0.05);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.05);
    --border-radius: 8px;
    --transition: all 0.3s ease;
  }

  /* Dark Mode Colors - Softer Dark Theme */
  [data-theme="dark"] {
    --primary: #8ab4f8; /* Lighter blue for dark mode */
    --primary-light: rgba(138, 180, 248, 0.15);
    --success: #81c995; /* Lighter green for dark mode */
    --success-light: rgba(129, 201, 149, 0.15);
    --danger: #f28b82; /* Lighter red for dark mode */
    --danger-light: rgba(242, 139, 130, 0.15);
    --warning: #fdd663; /* Lighter yellow for dark mode */
    --warning-light: rgba(253, 214, 99, 0.15);
    --info: #78d9ec;
    --info-light: rgba(120, 217, 236, 0.15);

    --dark: #e8eaed;
    --light: #3c4043;
    --white: #202124;

    --bg-main: #202124;
    --text-main: #e8eaed;
    --card-bg: #292a2d;
    --border-color: rgba(255,255,255,0.08);
  }

  body {
    background-color: var(--bg-main);
    color: var(--text-main);
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Card styles */
  .card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
  }

  .card:hover {
    box-shadow: var(--shadow);
  }

  /* Table styles */
  .table {
    color: var(--text-main);
  }

  .table thead th {
    background-color: var(--primary-light);
    color: var(--primary);
    font-weight: 600;
    border-bottom: none;
  }

  .table-hover tbody tr:hover {
    background-color: var(--primary-light);
    cursor: pointer;
  }

  /* Status badges */
  .badge-closed {
    background-color: var(--success-light);
    color: var(--success);
  }

  .badge-in_progress {
    background-color: var(--primary-light);
    color: var(--primary);
  }

  .badge-on_hold {
    background-color: var(--warning-light);
    color: var(--warning);
  }

  .badge-pending_acceptance {
    background-color: var(--info-light);
    color: var(--info);
  }

  /* Sticky header */
  .sticky-header {
    position: sticky;
    top: 0;
    z-index: 10;
  }

  /* Search and filter section */
  .filters-section {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
  }

  /* Selected row */
  tr.selected {
    background-color: var(--primary-light) !important;
  }

  /* Case preview */
  .case-preview {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    padding: 1.5rem;
    height: 100%;
  }

  .case-preview-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-muted);
  }

  .case-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }

  .case-preview-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0;
  }

  .case-preview-section {
    margin-bottom: 1.5rem;
  }

  .case-preview-section-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--primary);
    margin-bottom: 0.75rem;
  }

  .case-preview-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
  }

  .case-preview-label {
    color: var(--text-muted);
  }

  .case-preview-value {
    font-weight: 500;
  }

  .case-preview-items {
    margin-top: 0.75rem;
  }

  .case-preview-item-row {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
  }

  .case-preview-item-row:last-child {
    border-bottom: none;
  }

  .case-preview-actions {
    margin-top: 2rem;
    display: flex;
    justify-content: flex-end;
  }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
  <!-- Page Header -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h1 class="section-title mb-1">{% trans "Select Case for Invoice" %}</h1>
      <p class="section-subtitle mb-0">{% trans "Choose a case to create an invoice for" %}</p>
    </div>
    <div>
      <a href="{% url 'billing:invoice_list' %}" class="btn btn-outline-primary">
        <i class="fas fa-arrow-left me-1"></i> {% trans "Back to Invoices" %}
      </a>
    </div>
  </div>

  <div class="row">
    <!-- Cases List Column -->
    <div class="col-lg-7">
      <!-- Filters Section -->
      <div class="filters-section mb-4">
        <form method="get" class="row g-3 align-items-end">
          <div class="col-md-4">
            <label for="search" class="form-label">{% trans "Search" %}</label>
            <input type="text" class="form-control" id="search" name="search" placeholder="{% trans 'Case #, Patient, Dentist...' %}" value="{{ request.GET.search }}">
          </div>
          <div class="col-md-3">
            <label for="status" class="form-label">{% trans "Status" %}</label>
            <select class="form-select" id="status" name="status">
              <option value="">{% trans "All Statuses" %}</option>
              <option value="closed" {% if request.GET.status == 'closed' %}selected{% endif %}>{% trans "Closed" %}</option>
              <option value="in_progress" {% if request.GET.status == 'in_progress' %}selected{% endif %}>{% trans "In Progress" %}</option>
              <option value="on_hold" {% if request.GET.status == 'on_hold' %}selected{% endif %}>{% trans "On Hold" %}</option>
            </select>
          </div>
          <div class="col-md-5 d-flex gap-2">
            <button type="submit" class="btn btn-primary flex-grow-1">
              <i class="fas fa-search me-1"></i> {% trans "Filter" %}
            </button>
            <a href="{% url 'billing:select_cases' %}" class="btn btn-outline-secondary flex-grow-1">
              <i class="fas fa-redo me-1"></i> {% trans "Reset" %}
            </a>
          </div>
        </form>
      </div>

      <!-- Cases Table Card -->
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center py-3">
          <h5 class="mb-0">{% trans "Available Cases" %}</h5>
          <span class="badge bg-primary">{{ form.case.field.queryset.count }} {% trans "found" %}</span>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover align-middle mb-0" id="casesTable">
              <thead class="sticky-header">
                <tr>
                  <th>{% trans "Case #" %}</th>
                  <th>{% trans "Dentist" %}</th>
                  <th>{% trans "Patient" %}</th>
                  <th>{% trans "Received Date" %}</th>
                  <th>{% trans "Status" %}</th>
                  <th>{% trans "Has Invoice" %}</th>
                </tr>
              </thead>
              <tbody>
                {% for case in form.case.field.queryset %}
                  <tr data-case-id="{{ case.id }}" class="case-row">
                    <td>
                      <span class="fw-bold">#{{ case.case_number }}</span>
                    </td>
                    <td>{{ case.dentist.get_full_name }}</td>
                    <td>{{ case.patient.get_full_name }}</td>
                    <td>{{ case.received_date_time|date:"Y-m-d" }}</td>
                    <td>
                      <span class="badge badge-{{ case.status }}">{{ case.get_status_display }}</span>
                    </td>
                    <td>
                      {% if case.invoice %}
                        <span class="badge bg-success">{% trans "Yes" %}</span>
                      {% else %}
                        <span class="badge bg-secondary">{% trans "No" %}</span>
                      {% endif %}
                    </td>
                  </tr>
                {% empty %}
                  <tr>
                    <td colspan="6" class="text-center py-4">
                      <div class="d-flex flex-column align-items-center">
                        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                        <h5>{% trans "No cases found" %}</h5>
                        <p class="text-muted">{% trans "Try adjusting your search or filter criteria" %}</p>
                        <a href="{% url 'case:case_create' %}" class="btn btn-primary mt-2">
                          <i class="fas fa-plus me-1"></i> {% trans "Create New Case" %}
                        </a>
                      </div>
                    </td>
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Case Preview Column -->
    <div class="col-lg-5">
      <div class="case-preview" id="casePreview">
        <div class="case-preview-empty">
          <i class="fas fa-folder-open fa-4x mb-3"></i>
          <h4>{% trans "No Case Selected" %}</h4>
          <p class="text-center">{% trans "Select a case from the list to preview details and create an invoice" %}</p>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const caseRows = document.querySelectorAll('.case-row');
    const casePreview = document.getElementById('casePreview');
    let selectedCaseId = null;

    // Handle case row click
    caseRows.forEach(row => {
      row.addEventListener('click', function() {
        // Remove selected class from all rows
        caseRows.forEach(r => r.classList.remove('selected'));

        // Add selected class to clicked row
        this.classList.add('selected');

        // Get case ID
        const caseId = this.dataset.caseId;
        selectedCaseId = caseId;

        // Show loading state
        casePreview.innerHTML = `
          <div class="d-flex justify-content-center align-items-center" style="height: 300px;">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
          </div>
        `;

        // Fetch case details
        fetch(`/billing/api/case-details/${caseId}/`)
          .then(response => response.json())
          .then(data => {
            if (data.error) {
              casePreview.innerHTML = `
                <div class="case-preview-empty">
                  <i class="fas fa-exclamation-circle fa-4x mb-3 text-danger"></i>
                  <h4>${data.error}</h4>
                  <p class="text-center">${data.message || 'An error occurred while loading case details.'}</p>
                </div>
              `;
              return;
            }

            // Format items HTML
            let itemsHtml = '';
            if (data.items && data.items.length > 0) {
              data.items.forEach(item => {
                itemsHtml += `
                  <div class="case-preview-item-row">
                    <div>
                      <div class="fw-medium">${item.name}</div>
                      <div class="small text-muted">Qty: ${item.quantity}</div>
                    </div>
                    <div class="fw-bold">${item.price} ${item.currency}</div>
                  </div>
                `;
              });
            } else {
              itemsHtml = `<p class="text-muted text-center">No items found for this case</p>`;
            }

            // Update preview with case details
            casePreview.innerHTML = `
              <div class="case-preview-header">
                <h4 class="case-preview-title">Case #${data.case_number}</h4>
                <span class="badge badge-${data.status}">${data.status_display}</span>
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="case-preview-section">
                    <h5 class="case-preview-section-title">Dentist</h5>
                    <p class="mb-1">${data.dentist_name}</p>
                    <p class="mb-1 small text-muted">${data.dentist_email || ''}</p>
                    <p class="mb-0 small text-muted">${data.dentist_phone || ''}</p>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="case-preview-section">
                    <h5 class="case-preview-section-title">Patient</h5>
                    <p class="mb-1">${data.patient_name}</p>
                    <p class="mb-0 small text-muted">${data.patient_email || ''}</p>
                  </div>
                </div>
              </div>

              <div class="case-preview-section">
                <h5 class="case-preview-section-title">Case Details</h5>
                <div class="case-preview-item">
                  <span class="case-preview-label">Received Date:</span>
                  <span class="case-preview-value">${data.received_date}</span>
                </div>
                <div class="case-preview-item">
                  <span class="case-preview-label">Finished Date:</span>
                  <span class="case-preview-value">${data.finished_date || 'Not finished'}</span>
                </div>
                <div class="case-preview-item">
                  <span class="case-preview-label">Department:</span>
                  <span class="case-preview-value">${data.department || 'Not assigned'}</span>
                </div>
              </div>

              <div class="case-preview-section">
                <h5 class="case-preview-section-title">Items</h5>
                <div class="case-preview-items">
                  ${itemsHtml}
                </div>
              </div>

              <div class="case-preview-actions">
                <a href="/billing/invoices/create/from-case/${data.id}/" class="btn btn-primary">
                  <i class="fas fa-file-invoice me-1"></i> Create Invoice
                </a>
              </div>
            `;
          })
          .catch(error => {
            console.error('Error fetching case details:', error);
            casePreview.innerHTML = `
              <div class="case-preview-empty">
                <i class="fas fa-exclamation-circle fa-4x mb-3 text-danger"></i>
                <h4>Error Loading Case</h4>
                <p class="text-center">An error occurred while loading case details. Please try again.</p>
              </div>
            `;
          });
      });
    });
  });
</script>
{% endblock %}
