{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Purchase Orders" %} | {% trans "Billing" %}{% endblock %}

{% block extra_css %}
<style>
  :root {
    /* Primary Colors - Fresher, Lighter Palette */
    --primary: #4285F4; /* Google Blue */
    --primary-light: rgba(66, 133, 244, 0.1);
    --secondary: #8ab4f8;
    --success: #34A853; /* Google Green */
    --success-light: rgba(52, 168, 83, 0.1);
    --danger: #EA4335; /* Google Red */
    --danger-light: rgba(234, 67, 53, 0.1);
    --warning: #FBBC05; /* Google Yellow */
    --warning-light: rgba(251, 188, 5, 0.1);
    --info: #46bdc6;
    --info-light: rgba(70, 189, 198, 0.1);
    --dark: #3c4043;
    --light: #f8f9fa;
    --white: #ffffff;
    
    /* Background and Text Colors */
    --bg-main: #f8f9fa;
    --text-main: #202124;
    --card-bg: #ffffff;
    --border-color: rgba(0,0,0,0.08);
    
    /* UI Elements */
    --shadow-sm: 0 1px 2px rgba(0,0,0,0.05);
    --shadow: 0 4px 6px rgba(0,0,0,0.05);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.05);
    --border-radius: 8px;
    --transition: all 0.3s ease;
  }

  /* Dark Mode Colors - Softer Dark Theme */
  [data-theme="dark"] {
    --primary: #8ab4f8; /* Lighter blue for dark mode */
    --primary-light: rgba(138, 180, 248, 0.15);
    --success: #81c995; /* Lighter green for dark mode */
    --success-light: rgba(129, 201, 149, 0.15);
    --danger: #f28b82; /* Lighter red for dark mode */
    --danger-light: rgba(242, 139, 130, 0.15);
    --warning: #fdd663; /* Lighter yellow for dark mode */
    --warning-light: rgba(253, 214, 99, 0.15);
    --info: #78d9ec;
    --info-light: rgba(120, 217, 236, 0.15);

    --dark: #e8eaed;
    --light: #3c4043;
    --white: #202124;

    --bg-main: #202124;
    --text-main: #e8eaed;
    --card-bg: #292a2d;
    --border-color: rgba(255,255,255,0.08);
  }

  body {
    background-color: var(--bg-main);
    color: var(--text-main);
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Card styles */
  .card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
  }

  .card:hover {
    box-shadow: var(--shadow);
  }

  /* Table styles */
  .table {
    color: var(--text-main);
  }

  .table thead th {
    background-color: var(--primary-light);
    color: var(--primary);
    font-weight: 600;
    border-bottom: none;
  }

  .table-hover tbody tr:hover {
    background-color: var(--primary-light);
  }

  /* Status badges */
  .badge-draft {
    background-color: var(--light);
    color: var(--dark);
  }

  .badge-submitted {
    background-color: var(--info-light);
    color: var(--info);
  }

  .badge-approved {
    background-color: var(--primary-light);
    color: var(--primary);
  }

  .badge-partially_received {
    background-color: var(--warning-light);
    color: var(--warning);
  }

  .badge-completed {
    background-color: var(--success-light);
    color: var(--success);
  }

  .badge-cancelled {
    background-color: var(--danger-light);
    color: var(--danger);
  }

  /* Sticky header */
  .sticky-header {
    position: sticky;
    top: 0;
    z-index: 10;
  }

  /* Search and filter section */
  .filters-section {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
  }

  /* Action buttons */
  .action-buttons .btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  }

  /* Theme toggle button */
  .theme-toggle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-main);
    cursor: pointer;
    transition: var(--transition);
  }

  .theme-toggle:hover {
    background-color: var(--primary-light);
    color: var(--primary);
  }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
  <!-- Page Header -->
  <div class="d-flex justify-content-between align-items-center mb-4 flex-wrap">
    <div>
      <h1 class="section-title mb-1">{% trans "Purchase Order Management" %}</h1>
      <p class="section-subtitle mb-0">{% trans "Track and manage all purchase orders in one place" %}</p>
    </div>
    <div class="d-flex gap-2 mt-2 mt-md-0 action-buttons align-items-center">
      <button id="darkModeToggle" class="theme-toggle me-2" title="{% trans 'Toggle Dark Mode' %}">
        <i class="fas fa-moon"></i>
      </button>
      <a href="{% url 'billing:purchase_order_create' %}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i> {% trans "New Purchase Order" %}
      </a>
      <button class="btn btn-outline-primary d-none d-md-inline-flex" id="exportBtn">
        <i class="fas fa-download me-1"></i> {% trans "Export" %}
      </button>
      <button class="btn btn-outline-secondary d-none d-md-inline-flex" id="printBtn">
        <i class="fas fa-print me-1"></i> {% trans "Print" %}
      </button>
    </div>
  </div>

  <!-- Filters Section -->
  <div class="filters-section mb-4">
    <form method="get" class="row g-3 align-items-end">
      <div class="col-md-3">
        <label for="search" class="form-label">{% trans "Search" %}</label>
        <input type="text" class="form-control" id="search" name="search" placeholder="{% trans 'PO #, Supplier...' %}" value="{{ request.GET.search }}">
      </div>
      <div class="col-md-2">
        <label for="status" class="form-label">{% trans "Status" %}</label>
        <select class="form-select" id="status" name="status">
          <option value="">{% trans "All Statuses" %}</option>
          <option value="draft" {% if request.GET.status == 'draft' %}selected{% endif %}>{% trans "Draft" %}</option>
          <option value="submitted" {% if request.GET.status == 'submitted' %}selected{% endif %}>{% trans "Submitted" %}</option>
          <option value="approved" {% if request.GET.status == 'approved' %}selected{% endif %}>{% trans "Approved" %}</option>
          <option value="partially_received" {% if request.GET.status == 'partially_received' %}selected{% endif %}>{% trans "Partially Received" %}</option>
          <option value="completed" {% if request.GET.status == 'completed' %}selected{% endif %}>{% trans "Completed" %}</option>
          <option value="cancelled" {% if request.GET.status == 'cancelled' %}selected{% endif %}>{% trans "Cancelled" %}</option>
        </select>
      </div>
      <div class="col-md-2">
        <label for="date_from" class="form-label">{% trans "Date From" %}</label>
        <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request.GET.date_from }}">
      </div>
      <div class="col-md-2">
        <label for="date_to" class="form-label">{% trans "Date To" %}</label>
        <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request.GET.date_to }}">
      </div>
      <div class="col-md-3 d-flex gap-2">
        <button type="submit" class="btn btn-primary flex-grow-1">
          <i class="fas fa-search me-1"></i> {% trans "Filter" %}
        </button>
        <a href="{% url 'billing:purchase_order_list' %}" class="btn btn-outline-secondary flex-grow-1">
          <i class="fas fa-redo me-1"></i> {% trans "Reset" %}
        </a>
      </div>
    </form>
  </div>

  <!-- Purchase Orders Table Card -->
  <div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center py-3">
      <h5 class="mb-0">{% trans "Purchase Orders" %}</h5>
      <span class="badge bg-primary">{{ purchase_orders|length }} {% trans "found" %}</span>
    </div>
    <div class="card-body p-0 position-relative">
      <div class="table-responsive">
        <table class="table table-hover align-middle mb-0">
          <thead class="sticky-header">
            <tr>
              <th>{% trans "PO #" %}</th>
              <th>{% trans "Supplier" %}</th>
              <th>{% trans "Order Date" %}</th>
              <th>{% trans "Expected Delivery" %}</th>
              <th>{% trans "Total Amount" %}</th>
              <th>{% trans "Status" %}</th>
              <th class="text-end">{% trans "Actions" %}</th>
            </tr>
          </thead>
          <tbody>
            {% if purchase_orders %}
              {% for po in purchase_orders %}
                <tr>
                  <td>
                    <a href="{% url 'billing:purchase_order_detail' po.id %}" class="fw-bold text-decoration-none">
                      #{{ po.id }}
                    </a>
                  </td>
                  <td>{{ po.supplier.name }}</td>
                  <td>{{ po.order_date|date:"Y-m-d" }}</td>
                  <td>{{ po.expected_delivery_date|date:"Y-m-d"|default:"-" }}</td>
                  <td>{{ po.total_amount }}</td>
                  <td>
                    <span class="badge badge-{{ po.status }}">
                      {{ po.get_status_display }}
                    </span>
                  </td>
                  <td class="text-end">
                    <div class="btn-group">
                      <a href="{% url 'billing:purchase_order_detail' po.id %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-eye"></i>
                      </a>
                      <a href="{% url 'billing:purchase_order_update' po.id %}" class="btn btn-sm btn-warning">
                        <i class="fas fa-edit"></i>
                      </a>
                      {% if po.status == 'draft' %}
                        <a href="{% url 'billing:purchase_order_delete' po.id %}" class="btn btn-sm btn-danger">
                          <i class="fas fa-trash"></i>
                        </a>
                      {% endif %}
                    </div>
                  </td>
                </tr>
              {% endfor %}
            {% else %}
              <tr>
                <td colspan="7" class="text-center py-4">
                  <div class="d-flex flex-column align-items-center">
                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                    <h5>{% trans "No purchase orders found" %}</h5>
                    <p class="text-muted">{% trans "Try adjusting your search or filter criteria" %}</p>
                    <a href="{% url 'billing:purchase_order_create' %}" class="btn btn-primary mt-2">
                      <i class="fas fa-plus me-1"></i> {% trans "Create New Purchase Order" %}
                    </a>
                  </div>
                </td>
              </tr>
            {% endif %}
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  {% if is_paginated %}
  <nav aria-label="Purchase order pagination">
    <ul class="pagination justify-content-center">
      {% if page_obj.has_previous %}
        <li class="page-item">
          <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
            <span aria-hidden="true">&laquo;&laquo;</span>
          </a>
        </li>
        <li class="page-item">
          <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
            <span aria-hidden="true">&laquo;</span>
          </a>
        </li>
      {% endif %}

      {% for num in page_obj.paginator.page_range %}
        {% if page_obj.number == num %}
          <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
          <li class="page-item">
            <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
          </li>
        {% endif %}
      {% endfor %}

      {% if page_obj.has_next %}
        <li class="page-item">
          <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
            <span aria-hidden="true">&raquo;</span>
          </a>
        </li>
        <li class="page-item">
          <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
            <span aria-hidden="true">&raquo;&raquo;</span>
          </a>
        </li>
      {% endif %}
    </ul>
  </nav>
  {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // ----- Dark Mode Toggle -----
    const darkModeToggle = document.getElementById('darkModeToggle');
    const htmlElement = document.documentElement;
    
    // Check for saved theme preference or use system preference
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
      htmlElement.setAttribute('data-theme', savedTheme);
      updateThemeIcon(savedTheme);
    } else if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      htmlElement.setAttribute('data-theme', 'dark');
      updateThemeIcon('dark');
    }
    
    // Toggle theme when button is clicked
    darkModeToggle.addEventListener('click', function() {
      const currentTheme = htmlElement.getAttribute('data-theme');
      const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
      
      htmlElement.setAttribute('data-theme', newTheme);
      localStorage.setItem('theme', newTheme);
      updateThemeIcon(newTheme);
    });
    
    function updateThemeIcon(theme) {
      const icon = darkModeToggle.querySelector('i');
      if (theme === 'dark') {
        icon.classList.remove('fa-moon');
        icon.classList.add('fa-sun');
      } else {
        icon.classList.remove('fa-sun');
        icon.classList.add('fa-moon');
      }
    }
    
    // ----- Print Functionality -----
    document.getElementById('printBtn').addEventListener('click', function() {
      window.print();
    });
    
    // ----- Export Functionality -----
    document.getElementById('exportBtn').addEventListener('click', function() {
      // This would typically call an API endpoint to generate a CSV/Excel file
      alert('Export functionality will be implemented here');
    });
    
    // ----- Table row hover effect -----
    document.querySelectorAll('tbody tr').forEach(row => {
      row.addEventListener('click', function(e) {
        // Don't navigate if clicking on a button or link
        if (e.target.tagName === 'BUTTON' || e.target.tagName === 'A' ||
            e.target.closest('button') || e.target.closest('a')) {
          return;
        }
        
        // Navigate to purchase order detail page
        const poId = this.querySelector('td:first-child a').textContent.trim().substring(1);
        window.location.href = `{% url 'billing:purchase_order_detail' pk=0 %}`.replace('0', poId);
      });
    });
  });
</script>
{% endblock %}
