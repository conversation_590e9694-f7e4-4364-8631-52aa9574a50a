{% extends 'base.html' %}
{% load i18n %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-12 col-md-8 col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">{% trans "Update Settings" %}</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        {% for field in form %}
                        <div class="mb-3">
                            <label class="form-label" for="{{ field.id_for_label }}">
                                {{ field.label }}
                            </label>
                            {{ field }}
                            {% if field.help_text %}
                                <div class="form-text">{{ field.help_text }}</div>
                            {% endif %}
                            {% if field.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ field.errors }}
                                </div>
                            {% endif %}
                        </div>
                        {% endfor %}

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                {% trans "Save Changes" %}
                            </button>
                            <a href="{% url 'accounts:profile' %}" class="btn btn-outline-secondary">
                                {% trans "Cancel" %}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}